<?php
// SSH (Satış Sonrası Hizmet) tablosu oluşturma
include '../config.php';

echo "<h2>SSH (Satış Sonrası Hizmet) Tablosu Oluşturuluyor...</h2>";

// SSH kayıtları tablosu
$ssh_table = "CREATE TABLE IF NOT EXISTS b2b_ssh_kayitlari (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cari_id INT NOT NULL,
    siparis_id INT NULL,
    urun_id INT NULL,
    urun_adi VARCHAR(255) NOT NULL,
    parca_adi VARCHAR(255) NULL,
    ssh_nedeni ENUM('hasar', 'iade', 'degisim', 'garanti', 'kalite_sorunu', 'yanlis_urun', 'diger') NOT NULL,
    ssh_kaynagi ENUM('musteri', 'kargo', 'uretim', 'kalite_kontrol', 'diger') NOT NULL,
    durum ENUM('beklemede', 'inceleniyor', 'onaylandi', 'reddedildi', 'tamamlandi', 'iptal') DEFAULT 'beklemede',
    aciklama TEXT NULL,
    admin_notu TEXT NULL,
    giris_tarihi DATETIME DEFAULT CURRENT_TIMESTAMP,
    cikis_tarihi DATETIME NULL,
    islem_yapan_admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_cari_id (cari_id),
    INDEX idx_siparis_id (siparis_id),
    INDEX idx_urun_id (urun_id),
    INDEX idx_durum (durum),
    INDEX idx_ssh_nedeni (ssh_nedeni),
    INDEX idx_giris_tarihi (giris_tarihi),
    
    FOREIGN KEY (cari_id) REFERENCES cari_firmalar(id) ON DELETE CASCADE,
    FOREIGN KEY (siparis_id) REFERENCES b2b_siparisler(id) ON DELETE SET NULL,
    FOREIGN KEY (urun_id) REFERENCES urunler(id) ON DELETE SET NULL,
    FOREIGN KEY (islem_yapan_admin_id) REFERENCES b2b_admin(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci";

if ($conn->query($ssh_table) === TRUE) {
    echo "<p style='color: green;'>✓ b2b_ssh_kayitlari tablosu başarıyla oluşturuldu.</p>";
} else {
    echo "<p style='color: red;'>✗ b2b_ssh_kayitlari tablosu oluşturulurken hata: " . $conn->error . "</p>";
}

// SSH dosyaları tablosu (opsiyonel - fotoğraf, belge ekleri için)
$ssh_files_table = "CREATE TABLE IF NOT EXISTS b2b_ssh_dosyalar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ssh_id INT NOT NULL,
    dosya_adi VARCHAR(255) NOT NULL,
    dosya_yolu VARCHAR(500) NOT NULL,
    dosya_tipi ENUM('resim', 'belge', 'diger') DEFAULT 'resim',
    dosya_boyutu INT NULL,
    yuklenme_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_ssh_id (ssh_id),
    FOREIGN KEY (ssh_id) REFERENCES b2b_ssh_kayitlari(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci";

if ($conn->query($ssh_files_table) === TRUE) {
    echo "<p style='color: green;'>✓ b2b_ssh_dosyalar tablosu başarıyla oluşturuldu.</p>";
} else {
    echo "<p style='color: red;'>✗ b2b_ssh_dosyalar tablosu oluşturulurken hata: " . $conn->error . "</p>";
}

// SSH durum geçmişi tablosu
$ssh_history_table = "CREATE TABLE IF NOT EXISTS b2b_ssh_durum_gecmisi (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ssh_id INT NOT NULL,
    eski_durum VARCHAR(50) NULL,
    yeni_durum VARCHAR(50) NOT NULL,
    degisiklik_notu TEXT NULL,
    degistiren_admin_id INT NULL,
    degisiklik_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_ssh_id (ssh_id),
    INDEX idx_degisiklik_tarihi (degisiklik_tarihi),
    FOREIGN KEY (ssh_id) REFERENCES b2b_ssh_kayitlari(id) ON DELETE CASCADE,
    FOREIGN KEY (degistiren_admin_id) REFERENCES b2b_admin(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci";

if ($conn->query($ssh_history_table) === TRUE) {
    echo "<p style='color: green;'>✓ b2b_ssh_durum_gecmisi tablosu başarıyla oluşturuldu.</p>";
} else {
    echo "<p style='color: red;'>✗ b2b_ssh_durum_gecmisi tablosu oluşturulurken hata: " . $conn->error . "</p>";
}

echo "<h3>SSH Modülü Veritabanı Kurulumu Tamamlandı!</h3>";
echo "<p><a href='ssh_kayitlari.php'>SSH Kayıtları Sayfasına Git</a></p>";
echo "<p><a href='dashboard.php'>Dashboard'a Dön</a></p>";

$conn->close();
?> 