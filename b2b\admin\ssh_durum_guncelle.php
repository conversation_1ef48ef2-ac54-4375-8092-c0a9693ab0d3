<?php
// SSH durum ENUM'ına "gönderildi" seçeneği ekleme
include '../config.php';

echo "<h2>SSH Durum Güncelleniyor...</h2>";

// SSH durum ENUM'ını güncelle
$update_sql = "ALTER TABLE b2b_ssh_kayitlari 
               MODIFY durum ENUM('beklemede', 'inceleniyor', 'gonderildi', 'onaylandi', 'reddedildi', 'tamamlandi', 'iptal') 
               DEFAULT 'beklemede'";

if ($conn->query($update_sql) === TRUE) {
    echo "<p style='color: green;'>✅ SSH durum ENUM'ına 'gönderildi' seçeneği başarıyla eklendi!</p>";
} else {
    echo "<p style='color: red;'>❌ Hata: " . $conn->error . "</p>";
}

// Mevcut durum seçeneklerini kontrol et
$check_sql = "SHOW COLUMNS FROM b2b_ssh_kayitlari LIKE 'durum'";
$result = $conn->query($check_sql);

if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    echo "<h3>Güncel Durum Seçenekleri:</h3>";
    echo "<pre>" . htmlspecialchars($row['Type']) . "</pre>";
}

echo "<br><p><a href='ssh_ekle.php'>SSH Ekleme Sayfasına Git</a></p>";
echo "<p><a href='ssh_kayitlari.php'>SSH Kayıtları Sayfasına Git</a></p>";

$conn->close();
?> 