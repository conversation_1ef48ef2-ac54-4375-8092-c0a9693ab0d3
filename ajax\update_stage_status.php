<?php
// ajax/update_stage_status.php - Aşama Durum<PERSON> (Genel)
require_once '../includes/db.php';

header('Content-Type: application/json');

// JSON verisini al
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['stage_id']) || !isset($input['new_status'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Eksik parametreler'
    ]);
    exit;
}

$stage_id = (int)$input['stage_id'];
$new_status = $input['new_status'];
$notes = $input['notes'] ?? '';

// Geçerli durum kontrolü
$gecerli_durumlar = ['bekliyor', 'siraya_alindi', 'devam_ediyor', 'tamamlandi', 'sorun_var'];
if (!in_array($new_status, $gecerli_durumlar)) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz durum'
    ]);
    exit;
}

try {
    // Aşama var mı kontrol et
    $stmt = $db->prepare("SELECT id, plan_id FROM uretim_asamalari WHERE id = ?");
    $stmt->execute([$stage_id]);
    $stage = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$stage) {
        echo json_encode([
            'success' => false,
            'message' => 'Aşama bulunamadı'
        ]);
        exit;
    }

    // Aşama durumunu güncelle
    $stmt = $db->prepare("
        UPDATE uretim_asamalari 
        SET durum = ?, notlar = CONCAT(IFNULL(notlar, ''), ?, '\n[', NOW(), '] Durum: ', ?), guncelleme_tarihi = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$new_status, $notes ? "\n" . $notes : '', $new_status, $stage_id]);

    // Planın genel durumunu kontrol et ve güncelle
    $plan_id = $stage['plan_id'];
    $stmt = $db->prepare("
        SELECT COUNT(*) as toplam_asama,
               SUM(CASE WHEN durum = 'tamamlandi' THEN 1 ELSE 0 END) as tamamlanan,
               SUM(CASE WHEN durum = 'sorun_var' THEN 1 ELSE 0 END) as sorunlu
        FROM uretim_asamalari 
        WHERE plan_id = ?
    ");
    $stmt->execute([$plan_id]);
    $asama_stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // Plan durumunu otomatik güncelle
    $plan_durum = 'planlandi';
    if ($asama_stats['sorunlu'] > 0) {
        $plan_durum = 'sorun_var';
    } elseif ($asama_stats['tamamlanan'] > 0) {
        if ($asama_stats['tamamlanan'] == $asama_stats['toplam_asama']) {
            $plan_durum = 'tamamlandi';
        } else {
            $plan_durum = 'devam_ediyor';
        }
    }

    $stmt = $db->prepare("UPDATE uretim_planlari SET durum = ? WHERE id = ?");
    $stmt->execute([$plan_durum, $plan_id]);

    echo json_encode([
        'success' => true,
        'message' => 'Aşama durumu güncellendi',
        'stage_id' => $stage_id,
        'new_status' => $new_status,
        'plan_status' => $plan_durum
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Güncelleme hatası: ' . $e->getMessage()
    ]);
}
?> 