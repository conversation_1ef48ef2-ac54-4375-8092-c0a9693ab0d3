<?php
// <PERSON>a raporlamayı kapat (canlı için)
error_reporting(0);
ini_set('display_errors', 0);

session_start();
include __DIR__ . '/../config.php';
include 'includes/admin_check.php';

$page_title = "<PERSON><PERSON>ın Ürünleri Yönetimi";
$current_page = "ayin_urunleri";

// Tablo kontrolü ve oluşturma
$check_table = "SHOW TABLES LIKE 'b2b_ayin_urunleri'";
$table_result = $conn->query($check_table);

if ($table_result->num_rows == 0) {
    $create_table = "CREATE TABLE IF NOT EXISTS `b2b_ayin_urunleri` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `urun_id` int(11) NOT NULL,
        `sira` int(11) NOT NULL DEFAULT 1,
        `aktif` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `urun_id` (`urun_id`),
        KEY `sira` (`sira`),
        KEY `aktif` (`aktif`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci";
    $conn->query($create_table);
}

// İşlemler
if (isset($_POST['action'])) {
    if ($_POST['action'] == 'urun_ekle' && isset($_POST['urun_id'])) {
        // Mevcut ürün sayısını kontrol et
        $count_result = $conn->query("SELECT COUNT(*) as sayi FROM b2b_ayin_urunleri WHERE aktif = 1");
        $count_row = $count_result->fetch_assoc();
        
        if ($count_row['sayi'] >= 8) {
            $error_message = "En fazla 8 ürün seçebilirsiniz!";
        } else {
            $urun_id = (int)$_POST['urun_id'];
            
            // Ürün zaten seçili mi kontrol et
            $check_sql = "SELECT id FROM b2b_ayin_urunleri WHERE urun_id = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param('i', $urun_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $error_message = "Bu ürün zaten ayın ürünleri arasında!";
            } else {
                // En büyük sıra numarasını bul
                $sira_result = $conn->query("SELECT MAX(sira) as max_sira FROM b2b_ayin_urunleri");
                $sira_row = $sira_result->fetch_assoc();
                $yeni_sira = ($sira_row['max_sira'] ?? 0) + 1;
                
                $insert_sql = "INSERT INTO b2b_ayin_urunleri (urun_id, sira) VALUES (?, ?)";
                $insert_stmt = $conn->prepare($insert_sql);
                $insert_stmt->bind_param('ii', $urun_id, $yeni_sira);
                
                if ($insert_stmt->execute()) {
                    $success_message = "Ürün başarıyla eklendi!";
                } else {
                    $error_message = "Ürün eklenirken hata oluştu!";
                }
            }
        }
    }
    
    if ($_POST['action'] == 'urun_sil' && isset($_POST['ayin_urun_id'])) {
        $ayin_urun_id = (int)$_POST['ayin_urun_id'];
        $delete_sql = "DELETE FROM b2b_ayin_urunleri WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param('i', $ayin_urun_id);
        
        if ($delete_stmt->execute()) {
            $success_message = "Ürün başarıyla kaldırıldı!";
        } else {
            $error_message = "Ürün kaldırılırken hata oluştu!";
        }
    }
    
    if ($_POST['action'] == 'sira_guncelle' && isset($_POST['sira_data'])) {
        $sira_data = json_decode($_POST['sira_data'], true);
        
        foreach ($sira_data as $item) {
            $id = (int)$item['id'];
            $sira = (int)$item['sira'];
            
            $update_sql = "UPDATE b2b_ayin_urunleri SET sira = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param('ii', $sira, $id);
            $update_stmt->execute();
        }
        
        $success_message = "Sıralama başarıyla güncellendi!";
    }
}

// Ayın ürünlerini getir
$ayin_urunleri = [];
$ayin_sql = "SELECT au.*, u.stok_adi as urun_adi, u.urun_kodu, u.fiyat, u.resim1
             FROM b2b_ayin_urunleri au
             LEFT JOIN urunler u ON au.urun_id = u.id
             WHERE au.aktif = 1
             ORDER BY au.sira";
$ayin_result = $conn->query($ayin_sql);

if ($ayin_result && $ayin_result->num_rows > 0) {
    while ($row = $ayin_result->fetch_assoc()) {
        $ayin_urunleri[] = $row;
    }
} else {
    // En basit yöntemle dene - sadece ID'leri al sonra tek tek ürün bilgilerini getir
    $basit_sql = "SELECT * FROM b2b_ayin_urunleri WHERE aktif = 1 ORDER BY sira";
    $basit_result = $conn->query($basit_sql);
    
    if ($basit_result && $basit_result->num_rows > 0) {
        while ($au_row = $basit_result->fetch_assoc()) {
            // Her ürün için ayrı sorgu
            $urun_sql = "SELECT * FROM urunler WHERE id = " . $au_row['urun_id'];
            $urun_result = $conn->query($urun_sql);
            
            if ($urun_result && $urun_result->num_rows > 0) {
                $urun_data = $urun_result->fetch_assoc();
                $combined_row = array_merge($au_row, [
                    'urun_adi' => $urun_data['stok_adi'],
                    'urun_kodu' => $urun_data['urun_kodu'] ?? '',
                    'fiyat' => $urun_data['fiyat'] ?? 0,
                    'resim1' => $urun_data['resim1'] ?? ''
                ]);
                $ayin_urunleri[] = $combined_row;
            }
        }
    }
}

// Tüm ürünleri getir (seçim için) - Basit versiyon
$tum_urunler = [];
$urun_sql = "SELECT u.id, u.stok_adi as urun_adi, u.urun_kodu, u.fiyat
             FROM urunler u
             WHERE u.id NOT IN (SELECT COALESCE(urun_id, 0) FROM b2b_ayin_urunleri WHERE aktif = 1)
             AND (u.takip_kodu IS NULL OR u.takip_kodu NOT LIKE 'P%')
             ORDER BY u.stok_adi";
$urun_result = $conn->query($urun_sql);

if ($urun_result && $urun_result->num_rows > 0) {
    while ($row = $urun_result->fetch_assoc()) {
        $tum_urunler[] = $row;
    }
} else {
    // En basit sorgu
    $basit_sql = "SELECT id, stok_adi as urun_adi FROM urunler ORDER BY stok_adi";
    $basit_result = $conn->query($basit_sql);
    if ($basit_result && $basit_result->num_rows > 0) {
        while ($row = $basit_result->fetch_assoc()) {
            $tum_urunler[] = $row;
        }
    }
}

include 'includes/header.php';
?>

<style>
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.product-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px dashed transparent;
}

.product-card.sortable {
    cursor: move;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
    height: 200px;
    background: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.product-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

.product-image .placeholder {
    color: #94a3b8;
    font-size: 3rem;
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark);
}

.product-meta {
    color: var(--gray);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.order-badge {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: var(--secondary);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--gray);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.drag-placeholder {
    border: 2px dashed var(--secondary);
    background: rgba(249, 115, 22, 0.1);
}

/* Sortable.js stilleri */
.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    transform: scale(1.05);
}

.sortable-drag {
    transform: rotate(5deg);
}

/* Form stilleri */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--gray-light);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary);
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

.btn-primary {
    background: var(--secondary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary:hover {
    background: var(--accent);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
}
</style>

<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-star"></i> Ayın Ürünleri Yönetimi</h1>
        <p>Ana sayfada gösterilecek öne çıkan ürünleri seçin ve sıralayın (En fazla 8 ürün)</p>
    </div>
</div>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <h3><i class="fas fa-star"></i> Seçili Ürünler (<?php echo count($ayin_urunleri); ?>/8)</h3>
    
    <?php if (empty($ayin_urunleri)): ?>
        <div class="empty-state">
            <i class="fas fa-star"></i>
            <h4>Henüz ürün seçilmemiş</h4>
            <p>Aşağıdan ürün seçerek ayın ürünleri listesini oluşturun.</p>
        </div>
    <?php else: ?>
        <p><i class="fas fa-info-circle"></i> Ürünleri sürükleyerek sıralayabilirsiniz.</p>
        
        <div class="products-grid" id="sortableProducts">
            <?php foreach ($ayin_urunleri as $index => $urun): ?>
                <div class="product-card sortable" data-id="<?php echo $urun['id']; ?>">
                    <div class="product-image">
                        <div class="order-badge"><?php echo $index + 1; ?></div>
                        <?php if (!empty($urun['resim1'])): ?>
                            <img src="<?php echo getUrunResimUrl($urun['resim1']); ?>" alt="<?php echo htmlspecialchars($urun['urun_adi']); ?>">
                        <?php else: ?>
                            <div class="placeholder">
                                <i class="fas fa-image"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-info">
                        <div class="product-title"><?php echo htmlspecialchars($urun['urun_adi']); ?></div>
                        <div class="product-meta">
                            <?php if ($urun['urun_kodu']): ?>
                                <span><i class="fas fa-barcode"></i> <?php echo htmlspecialchars($urun['urun_kodu']); ?></span><br>
                            <?php endif; ?>
                            <?php if ($urun['fiyat']): ?>
                                <span><i class="fas fa-lira-sign"></i> <?php echo number_format($urun['fiyat'], 2); ?> TL</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-actions">
                            <form method="POST" style="display: inline-block;" onsubmit="return confirm('Bu ürünü kaldırmak istediğinizden emin misiniz?')">
                                <input type="hidden" name="action" value="urun_sil">
                                <input type="hidden" name="ayin_urun_id" value="<?php echo $urun['id']; ?>">
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> Kaldır
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<?php if (count($ayin_urunleri) < 8): ?>
<div class="content-card">
    <h3><i class="fas fa-plus"></i> Ürün Ekle</h3>
    
    <?php if (empty($tum_urunler)): ?>
        <div class="empty-state">
            <i class="fas fa-box"></i>
            <h4>Eklenecek ürün bulunamadı</h4>
            <p>Tüm uygun ürünler zaten seçilmiş.</p>
        </div>
    <?php else: ?>
        <form method="POST">
            <input type="hidden" name="action" value="urun_ekle">
            
            <div class="form-group">
                <label>Ürün Seçin</label>
                <select name="urun_id" class="form-control" required>
                    <option value="">Ürün seçin...</option>
                    <?php foreach ($tum_urunler as $urun): ?>
                        <option value="<?php echo $urun['id']; ?>">
                            <?php echo htmlspecialchars($urun['urun_adi']); ?>
                            <?php if (!empty($urun['urun_kodu'])): ?>
                                [<?php echo htmlspecialchars($urun['urun_kodu']); ?>]
                            <?php endif; ?>
                            <?php if (!empty($urun['fiyat'])): ?>
                                - <?php echo number_format($urun['fiyat'], 2); ?> TL
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ürünü Ekle
            </button>
        </form>
    <?php endif; ?>
</div>
<?php endif; ?>

<!-- Sortable.js kütüphanesi -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<script>
// Sürükle bırak işlevi
if (document.getElementById('sortableProducts')) {
    const sortable = Sortable.create(document.getElementById('sortableProducts'), {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        onEnd: function(evt) {
            // Yeni sıralamayı al
            const items = [];
            const productCards = document.querySelectorAll('#sortableProducts .product-card');
            
            productCards.forEach((card, index) => {
                items.push({
                    id: parseInt(card.dataset.id),
                    sira: index + 1
                });
                
                // Badge numarasını güncelle
                const badge = card.querySelector('.order-badge');
                if (badge) {
                    badge.textContent = index + 1;
                }
            });
            
            // AJAX ile sıralamayı kaydet
            const formData = new FormData();
            formData.append('action', 'sira_guncelle');
            formData.append('sira_data', JSON.stringify(items));
            
            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // Başarılı mesajı göster
                console.log('Sıralama güncellendi');
            })
            .catch(error => {
                console.error('Hata:', error);
                // Hata durumunda sayfayı yenile
                location.reload();
            });
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?> 