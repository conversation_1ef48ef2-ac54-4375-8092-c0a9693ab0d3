<?php
// Cari yönetimi için gerekli B2B tablolarını oluştur
include '../config.php';

echo "<h2>B2B Cari Yönetimi Tabloları Oluşturuluyor...</h2>";

// 1. B2B Cari Ayarlar Tablosu - Her cari için <PERSON>zel ayarlar
$sql1 = "CREATE TABLE IF NOT EXISTS `b2b_cari_ayarlar` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `cari_id` int(11) NOT NULL,
    `iskonto_orani` decimal(5,2) DEFAULT 0.00 COMMENT 'Cari özel iskonto oranı (%)',
    `ek_indirim_orani` decimal(5,2) DEFAULT 0.00 COMMENT 'Ek indirim oranı (%)',
    `kredi_limiti` decimal(15,2) DEFAULT 0.00 COMMENT 'Kredi limiti (TL)',
    `odeme_vadesi` int(11) DEFAULT 0 COMMENT 'Ödeme vadesi (gün)',
    `aktif` tinyint(1) DEFAULT 1,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `cari_id` (`cari_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql1) === TRUE) {
    echo "✅ b2b_cari_ayarlar tablosu oluşturuldu<br>";
} else {
    echo "❌ Hata: " . $conn->error . "<br>";
}

// 2. B2B Cari Hareketler Tablosu - Borç/Alacak hareketleri
$sql2 = "CREATE TABLE IF NOT EXISTS `b2b_cari_hareketler` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `cari_id` int(11) NOT NULL,
    `hareket_tipi` enum('borc','alacak','odeme','tahsilat') NOT NULL,
    `tutar` decimal(15,2) NOT NULL,
    `aciklama` text,
    `referans_tip` varchar(50) DEFAULT NULL COMMENT 'siparis, manuel, odeme',
    `referans_id` int(11) DEFAULT NULL COMMENT 'Bağlı olduğu kayıt ID',
    `belge_no` varchar(100) DEFAULT NULL,
    `vade_tarihi` date DEFAULT NULL,
    `islem_tarihi` date NOT NULL,
    `created_by` int(11) DEFAULT NULL COMMENT 'İşlemi yapan admin ID',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `cari_id` (`cari_id`),
    KEY `hareket_tipi` (`hareket_tipi`),
    KEY `islem_tarihi` (`islem_tarihi`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql2) === TRUE) {
    echo "✅ b2b_cari_hareketler tablosu oluşturuldu<br>";
} else {
    echo "❌ Hata: " . $conn->error . "<br>";
}

// 3. B2B Cari Ödemeler Tablosu - Ödeme kayıtları
$sql3 = "CREATE TABLE IF NOT EXISTS `b2b_cari_odemeler` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `cari_id` int(11) NOT NULL,
    `odeme_tipi` enum('nakit','kredi_karti','havale','cek','senet') NOT NULL,
    `tutar` decimal(15,2) NOT NULL,
    `aciklama` text,
    `banka_bilgisi` varchar(200) DEFAULT NULL,
    `belge_no` varchar(100) DEFAULT NULL,
    `odeme_tarihi` date NOT NULL,
    `durum` enum('beklemede','onaylandi','iptal') DEFAULT 'beklemede',
    `onaylayan_admin` int(11) DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `cari_id` (`cari_id`),
    KEY `odeme_tipi` (`odeme_tipi`),
    KEY `durum` (`durum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

if ($conn->query($sql3) === TRUE) {
    echo "✅ b2b_cari_odemeler tablosu oluşturuldu<br>";
} else {
    echo "❌ Hata: " . $conn->error . "<br>";
}

// 4. B2B Cari Bakiye View'i oluştur
$sql4 = "CREATE OR REPLACE VIEW `b2b_cari_bakiye` AS 
SELECT 
    c.id as cari_id,
    c.firm_name,
    COALESCE(SUM(CASE WHEN h.hareket_tipi = 'borc' THEN h.tutar ELSE 0 END), 0) as toplam_borc,
    COALESCE(SUM(CASE WHEN h.hareket_tipi = 'alacak' THEN h.tutar ELSE 0 END), 0) as toplam_alacak,
    COALESCE(SUM(CASE WHEN h.hareket_tipi = 'odeme' THEN h.tutar ELSE 0 END), 0) as toplam_odeme,
    COALESCE(SUM(CASE WHEN h.hareket_tipi = 'tahsilat' THEN h.tutar ELSE 0 END), 0) as toplam_tahsilat,
    (COALESCE(SUM(CASE WHEN h.hareket_tipi = 'borc' THEN h.tutar ELSE 0 END), 0) - 
     COALESCE(SUM(CASE WHEN h.hareket_tipi = 'alacak' THEN h.tutar ELSE 0 END), 0) -
     COALESCE(SUM(CASE WHEN h.hareket_tipi = 'odeme' THEN h.tutar ELSE 0 END), 0) +
     COALESCE(SUM(CASE WHEN h.hareket_tipi = 'tahsilat' THEN h.tutar ELSE 0 END), 0)) as bakiye
FROM cari_firmalar c
LEFT JOIN b2b_cari_hareketler h ON c.id = h.cari_id
WHERE c.b2b_aktif = 1
GROUP BY c.id, c.firm_name";

if ($conn->query($sql4) === TRUE) {
    echo "✅ b2b_cari_bakiye view'i oluşturuldu<br>";
} else {
    echo "❌ Hata: " . $conn->error . "<br>";
}

echo "<br><h3>🎉 Tüm B2B Cari Yönetimi tabloları başarıyla oluşturuldu!</h3>";
echo "<p><a href='musteriler.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Müşteri Listesine Dön</a></p>";

$conn->close();
?> 