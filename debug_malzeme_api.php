<?php
// 🔧 MALZEME API DEBUG SAYFASI
// Bu sayfa malzeme yükleme sorunlarını tespit eder

header('Content-Type: text/html; charset=UTF-8');
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'includes/db.php';

function debugOutput($title, $content, $type = 'info') {
    $colors = [
        'success' => '#28a745',
        'error' => '#dc3545', 
        'warning' => '#ffc107',
        'info' => '#17a2b8',
        'primary' => '#007bff'
    ];
    
    $color = $colors[$type] ?? $colors['info'];
    
    echo "<div style='background: {$color}; color: white; padding: 10px; margin: 5px; border-radius: 5px; font-family: monospace;'>";
    echo "<strong>🔧 {$title}</strong><br>";
    if (is_array($content) || is_object($content)) {
        echo "<pre style='color: white; margin: 5px 0;'>" . print_r($content, true) . "</pre>";
    } else {
        echo $content;
    }
    echo "</div>";
}

// AJAX isteği kontrolü
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['action']) {
        case 'test_sunta':
            try {
                $query = "SELECT id, malz_kodu, malzeme_adi, birim, stok_adet, fiyat_cm2 FROM uretim_malzemeler_sunta LIMIT 10";
                $result = $conn->query($query);
                $data = [];
                while ($row = $result->fetch_assoc()) {
                    $data[] = $row;
                }
                echo json_encode(['success' => true, 'data' => $data, 'count' => count($data)]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'test_pvc':
            try {
                $query = "SELECT id, malz_kodu, malzeme_adi, birim, stok_adet, fiyat FROM uretim_malzemeler_pvc LIMIT 10";
                $result = $conn->query($query);
                $data = [];
                while ($row = $result->fetch_assoc()) {
                    $data[] = $row;
                }
                echo json_encode(['success' => true, 'data' => $data, 'count' => count($data)]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'test_hirdavat':
            try {
                $query = "SELECT id, malz_kodu, malzeme_adi, birim, stok_adet, fiyat FROM uretim_malzemeler_hirdavat LIMIT 10";
                $result = $conn->query($query);
                $data = [];
                while ($row = $result->fetch_assoc()) {
                    $data[] = $row;
                }
                echo json_encode(['success' => true, 'data' => $data, 'count' => count($data)]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'test_koli':
            try {
                $query = "SELECT id, malz_kodu, malzeme_adi, birim, stok_adet, birim_fiyat_m2, birim_fiyat_adet FROM uretim_malzemeler_koli LIMIT 10";
                $result = $conn->query($query);
                $data = [];
                while ($row = $result->fetch_assoc()) {
                    $data[] = $row;
                }
                echo json_encode(['success' => true, 'data' => $data, 'count' => count($data)]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'test_urunler':
            try {
                $query = "SELECT id, urun_adi, stok_kodu FROM urunler LIMIT 10";
                $result = $conn->query($query);
                $data = [];
                while ($row = $result->fetch_assoc()) {
                    $data[] = $row;
                }
                echo json_encode(['success' => true, 'data' => $data, 'count' => count($data)]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
    }
}

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>🔧 Malzeme API Debug</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Courier New', monospace; }
        .debug-section { margin: 20px 0; }
        .api-test { margin: 10px 0; }
        .json-output { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">🔧 Malzeme API Debug Sistemi</h1>
            
            <!-- 1. MALZEME TABLOLARI TEST -->
            <div class="debug-section">
                <div class="card">
                    <div class="card-header">
                        <h5>📦 Malzeme Tabloları Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-success w-100 api-test" onclick="testAPI('sunta')">
                                    🌲 Sunta Test
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info w-100 api-test" onclick="testAPI('pvc')">
                                    🎨 PVC Test
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-warning w-100 api-test" onclick="testAPI('hirdavat')">
                                    🔩 Hırdavat Test
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-danger w-100 api-test" onclick="testAPI('koli')">
                                    📦 Koli Test
                                </button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <button class="btn btn-primary w-100 api-test" onclick="testAPI('urunler')">
                                    🏷️ Ürünler Test
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-secondary w-100" onclick="testAllAPIs()">
                                    🚀 Tümünü Test Et
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. TEST SONUÇLARI -->
            <div class="debug-section">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Test Sonuçları</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <div class="alert alert-info">
                                Yukarıdaki butonlara tıklayarak API testlerini başlatın.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 3. JAVASCRIPT MALZEME YÜKLEME TEST -->
            <div class="debug-section">
                <div class="card">
                    <div class="card-header">
                        <h5>⚡ JavaScript Malzeme Yükleme Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Sunta Malzemeler</label>
                                <select class="form-select" id="test-sunta-select">
                                    <option value="">Yükleniyor...</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">PVC Malzemeler</label>
                                <select class="form-select" id="test-pvc-select">
                                    <option value="">Yükleniyor...</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">Hırdavat Malzemeler</label>
                                <select class="form-select" id="test-hirdavat-select">
                                    <option value="">Yükleniyor...</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Koli Malzemeler</label>
                                <select class="form-select" id="test-koli-select">
                                    <option value="">Yükleniyor...</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-success" onclick="loadAllMalzemeler()">
                                🔄 Malzemeleri Yükle
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 4. ÜRÜN YÜKLEME TEST -->
            <div class="debug-section">
                <div class="card">
                    <div class="card-header">
                        <h5>🏷️ Ürün Yükleme Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">Ürünler</label>
                                <select class="form-select" id="test-urun-select">
                                    <option value="">Yükleniyor...</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary w-100" onclick="loadUrunler()">
                                    🔄 Ürünleri Yükle
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// API Test Fonksiyonları
function testAPI(type) {
    const resultsDiv = document.getElementById('test-results');
    resultsDiv.innerHTML = `<div class="alert alert-info">🔄 ${type.toUpperCase()} API test ediliyor...</div>`;
    
    fetch(`?action=test_${type}`)
        .then(response => response.json())
        .then(data => {
            let html = `<div class="alert alert-${data.success ? 'success' : 'danger'}">`;
            html += `<h6>${data.success ? '✅' : '❌'} ${type.toUpperCase()} API Test Sonucu</h6>`;
            
            if (data.success) {
                html += `<p>Toplam kayıt: ${data.count}</p>`;
                html += `<div class="json-output">${JSON.stringify(data.data, null, 2)}</div>`;
            } else {
                html += `<p>Hata: ${data.error}</p>`;
            }
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">❌ AJAX Hatası: ${error}</div>`;
        });
}

function testAllAPIs() {
    const types = ['sunta', 'pvc', 'hirdavat', 'koli', 'urunler'];
    let currentIndex = 0;
    
    function testNext() {
        if (currentIndex < types.length) {
            testAPI(types[currentIndex]);
            currentIndex++;
            setTimeout(testNext, 2000);
        }
    }
    
    testNext();
}

// Malzeme Yükleme Fonksiyonları
function loadAllMalzemeler() {
    loadSuntaMalzemeler();
    loadPvcMalzemeler();
    loadHirdavatMalzemeler();
    loadKoliMalzemeler();
}

function loadSuntaMalzemeler() {
    fetch('?action=test_sunta')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('test-sunta-select');
            select.innerHTML = '<option value="">Seçiniz...</option>';
            
            if (data.success) {
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.malz_kodu} - ${item.malzeme_adi}`;
                    option.setAttribute('data-kod', item.malz_kodu);
                    option.setAttribute('data-ad', item.malzeme_adi);
                    option.setAttribute('data-birim', item.birim);
                    option.setAttribute('data-stok', item.stok_adet);
                    option.setAttribute('data-fiyat', item.fiyat_cm2);
                    select.appendChild(option);
                });
                console.log('✅ Sunta malzemeler yüklendi:', data.count);
            } else {
                console.error('❌ Sunta yükleme hatası:', data.error);
            }
        })
        .catch(error => console.error('❌ Sunta AJAX hatası:', error));
}

function loadPvcMalzemeler() {
    fetch('?action=test_pvc')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('test-pvc-select');
            select.innerHTML = '<option value="">Seçiniz...</option>';
            
            if (data.success) {
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.malz_kodu} - ${item.malzeme_adi}`;
                    select.appendChild(option);
                });
                console.log('✅ PVC malzemeler yüklendi:', data.count);
            }
        })
        .catch(error => console.error('❌ PVC AJAX hatası:', error));
}

function loadHirdavatMalzemeler() {
    fetch('?action=test_hirdavat')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('test-hirdavat-select');
            select.innerHTML = '<option value="">Seçiniz...</option>';
            
            if (data.success) {
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.malz_kodu} - ${item.malzeme_adi}`;
                    select.appendChild(option);
                });
                console.log('✅ Hırdavat malzemeler yüklendi:', data.count);
            }
        })
        .catch(error => console.error('❌ Hırdavat AJAX hatası:', error));
}

function loadKoliMalzemeler() {
    fetch('?action=test_koli')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('test-koli-select');
            select.innerHTML = '<option value="">Seçiniz...</option>';
            
            if (data.success) {
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.malz_kodu} - ${item.malzeme_adi}`;
                    option.setAttribute('data-birim-fiyat-m2', item.birim_fiyat_m2);
                    option.setAttribute('data-birim-fiyat-adet', item.birim_fiyat_adet);
                    select.appendChild(option);
                });
                console.log('✅ Koli malzemeler yüklendi:', data.count);
            }
        })
        .catch(error => console.error('❌ Koli AJAX hatası:', error));
}

function loadUrunler() {
    fetch('?action=test_urunler')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('test-urun-select');
            select.innerHTML = '<option value="">Seçiniz...</option>';
            
            if (data.success) {
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.stok_kodu} - ${item.urun_adi}`;
                    select.appendChild(option);
                });
                console.log('✅ Ürünler yüklendi:', data.count);
            }
        })
        .catch(error => console.error('❌ Ürün AJAX hatası:', error));
}

// Sayfa yüklendiğinde otomatik test
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Debug sayfası yüklendi');
    loadAllMalzemeler();
    loadUrunler();
});
</script>

</body>
</html>
