<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "SSH Kaydı Düzenle - B2B Admin";
$current_page = "ssh_kayitlari";

$mesaj = '';
$mesaj_tur = '';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];

// SSH ID kontrolü
$ssh_id = intval($_GET['id'] ?? 0);
if (!$ssh_id) {
    header('Location: ssh_kayitlari.php');
    exit;
}

// SSH kaydını getir
$ssh_sql = "SELECT s.*, 
            cf.firm_name as musteri_adi
            FROM b2b_ssh_kayitlari s
            LEFT JOIN cari_firmalar cf ON s.cari_id = cf.id
            WHERE s.id = ?";
$ssh_stmt = $conn->prepare($ssh_sql);
$ssh_stmt->bind_param('i', $ssh_id);
$ssh_stmt->execute();
$ssh_result = $ssh_stmt->get_result();
$ssh = $ssh_result->fetch_assoc();

if (!$ssh) {
    header('Location: ssh_kayitlari.php');
    exit;
}

// Form submit edildiğinde
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $cari_id = intval($_POST['cari_id']);
    $siparis_id = !empty($_POST['siparis_id']) ? intval($_POST['siparis_id']) : null;
    $urun_id = !empty($_POST['urun_id']) ? intval($_POST['urun_id']) : null;
    $urun_adi = trim($_POST['urun_adi']);
    $parca_adi = trim($_POST['parca_adi']);
    $ssh_nedeni = $_POST['ssh_nedeni'];
    $ssh_kaynagi = $_POST['ssh_kaynagi'];
    $aciklama = trim($_POST['aciklama']);
    $durum = $_POST['durum'] ?? 'beklemede';
    
    if ($cari_id && $urun_adi && $ssh_nedeni && $ssh_kaynagi) {
        try {
            $conn->begin_transaction();
            
            // SSH kaydını güncelle
            $sql = "UPDATE b2b_ssh_kayitlari SET 
                    cari_id = ?, 
                    siparis_id = ?, 
                    urun_id = ?, 
                    urun_adi = ?, 
                    parca_adi = ?, 
                    ssh_nedeni = ?, 
                    ssh_kaynagi = ?, 
                    durum = ?, 
                    aciklama = ?, 
                    islem_yapan_admin_id = ?, 
                    updated_at = NOW() 
                    WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('iiissssssis', $cari_id, $siparis_id, $urun_id, $urun_adi, $parca_adi, $ssh_nedeni, $ssh_kaynagi, $durum, $aciklama, $admin_id, $ssh_id);
            
            if ($stmt->execute()) {
                // Dosya yükleme işlemi (yeni resimler varsa)
                $upload_dir = '../uploads/ssh/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $uploaded_files = [];
                
                // 3 adet resim yükleme
                for ($i = 1; $i <= 3; $i++) {
                    if (isset($_FILES["ssh_resim_$i"]) && $_FILES["ssh_resim_$i"]['error'] == 0) {
                        $file = $_FILES["ssh_resim_$i"];
                        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                        
                        // Dosya boyutu kontrolü (5MB = 5 * 1024 * 1024)
                        if ($file['size'] > 5 * 1024 * 1024) {
                            throw new Exception("Resim $i çok büyük. Maksimum 5MB olmalı.");
                        }
                        
                        // Dosya uzantısı kontrolü
                        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                        if (in_array(strtolower($file_extension), $allowed_extensions)) {
                            
                            // Benzersiz dosya adı oluştur
                            $new_filename = 'ssh_' . $ssh_id . '_' . $i . '_' . time() . '.' . $file_extension;
                            $upload_path = $upload_dir . $new_filename;
                            
                            // Dosyayı yükle
                            if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                                
                                // Veritabanına kaydet
                                $file_sql = "INSERT INTO b2b_ssh_dosyalar (ssh_id, dosya_adi, dosya_yolu, dosya_tipi, dosya_boyutu) VALUES (?, ?, ?, 'resim', ?)";
                                $file_stmt = $conn->prepare($file_sql);
                                $file_stmt->bind_param('issi', $ssh_id, $file['name'], $upload_path, $file['size']);
                                $file_stmt->execute();
                                
                                $uploaded_files[] = $file['name'];
                            } else {
                                throw new Exception("Resim $i yüklenemedi. Dosya izinlerini kontrol edin.");
                            }
                        } else {
                            throw new Exception("Resim $i desteklenmeyen format. JPG, PNG, GIF, WEBP kullanın.");
                        }
                    }
                }
                
                // Durum geçmişine kaydet (sadece değişiklik varsa)
                if ($ssh['durum'] != $durum) {
                    $history_sql = "INSERT INTO b2b_ssh_durum_gecmisi (ssh_id, eski_durum, yeni_durum, degisiklik_notu, degistiren_admin_id) VALUES (?, ?, ?, ?, ?)";
                    $history_stmt = $conn->prepare($history_sql);
                    $history_note = "SSH kaydı güncellendi" . (count($uploaded_files) > 0 ? " - " . count($uploaded_files) . " yeni resim eklendi" : "");
                    $history_stmt->bind_param('isssi', $ssh_id, $ssh['durum'], $durum, $history_note, $admin_id);
                    $history_stmt->execute();
                }
                
                $conn->commit();
                
                $mesaj = "SSH kaydı başarıyla güncellendi!";
                if (count($uploaded_files) > 0) {
                    $mesaj .= "<br><strong>Yeni yüklenen resimler:</strong> " . implode(', ', $uploaded_files);
                }
                $mesaj .= "<br><a href='ssh_detay.php?id=" . $ssh_id . "' class='alert-link'>Detayları görüntüle</a>";
                $mesaj_tur = "success";
                
                // SSH verilerini tekrar çek (güncel veri için)
                $ssh_stmt->execute();
                $ssh_result = $ssh_stmt->get_result();
                $ssh = $ssh_result->fetch_assoc();
                
            } else {
                throw new Exception("SSH kaydı güncellenirken hata oluştu: " . $conn->error);
            }
            
        } catch (Exception $e) {
            $conn->rollback();
            $mesaj = "Hata: " . $e->getMessage();
            $mesaj_tur = "danger";
        }
    } else {
        $mesaj = "Lütfen gerekli alanları doldurun!";
        $mesaj_tur = "warning";
    }
}

// Müşterileri çek
$musteriler = [];
$musteri_sql = "SELECT id, firm_name FROM cari_firmalar ORDER BY firm_name";
$musteri_result = $conn->query($musteri_sql);
if ($musteri_result && $musteri_result->num_rows > 0) {
    while ($row = $musteri_result->fetch_assoc()) {
        $musteriler[] = $row;
    }
}

// Ürünleri çek
$urunler = [];
$urun_sql = "SELECT id, stok_kodu, stok_adi FROM urunler ORDER BY stok_adi";
$urun_result = $conn->query($urun_sql);
if ($urun_result && $urun_result->num_rows > 0) {
    while ($row = $urun_result->fetch_assoc()) {
        $urunler[] = $row;
    }
}

// Mevcut resimleri çek
$resimler = [];
$resim_sql = "SELECT * FROM b2b_ssh_dosyalar WHERE ssh_id = ? AND dosya_tipi = 'resim' ORDER BY yuklenme_tarihi";
$resim_stmt = $conn->prepare($resim_sql);
$resim_stmt->bind_param('i', $ssh_id);
$resim_stmt->execute();
$resim_result = $resim_stmt->get_result();
while ($row = $resim_result->fetch_assoc()) {
    $resimler[] = $row;
}

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-edit"></i>
            SSH Kaydı Düzenle #<?php echo $ssh['id']; ?>
        </h1>
        <div style="display: flex; gap: 1rem;">
            <a href="ssh_detay.php?id=<?php echo $ssh['id']; ?>" class="btn" style="background: var(--gray);">
                <i class="fas fa-eye"></i>
                Detay
            </a>
            <a href="ssh_kayitlari.php" class="btn" style="background: var(--gray);">
                <i class="fas fa-arrow-left"></i>
                SSH Kayıtlarına Dön
            </a>
        </div>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-tools"></i>
        SSH Kaydını Düzenle
    </h3>
    
    <form method="post" enctype="multipart/form-data" style="display: grid; gap: 2rem;">
        <!-- Müşteri ve Sipariş Bilgileri -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-user"></i> Müşteri Bilgileri
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-building"></i> Müşteri *
                    </label>
                    <select name="cari_id" id="cari_id" required style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="">Müşteri seçiniz...</option>
                        <?php foreach ($musteriler as $musteri): ?>
                            <option value="<?php echo $musteri['id']; ?>" <?php echo ($ssh['cari_id'] == $musteri['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($musteri['firm_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-shopping-cart"></i> Sipariş No (Opsiyonel)
                    </label>
                    <select name="siparis_id" id="siparis_id" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="">Sipariş seçiniz...</option>
                        <?php if ($ssh['siparis_id']): ?>
                            <option value="<?php echo $ssh['siparis_id']; ?>" selected>Sipariş #<?php echo $ssh['siparis_id']; ?></option>
                        <?php endif; ?>
                    </select>
                    <small style="color: var(--gray);">Müşteri seçildikten sonra siparişler yüklenecek</small>
                </div>
            </div>
        </div>
        
        <!-- Ürün Bilgileri -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-box"></i> Ürün Bilgileri
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-cube"></i> Ürün Seç (Opsiyonel)
                    </label>
                    <select name="urun_id" id="urun_id" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" onchange="fillProductInfo()">
                        <option value="">Ürün seçiniz...</option>
                        <?php foreach ($urunler as $urun): ?>
                            <option value="<?php echo $urun['id']; ?>" <?php echo ($ssh['urun_id'] == $urun['id']) ? 'selected' : ''; ?> 
                                    data-stok-kodu="<?php echo htmlspecialchars($urun['stok_kodu']); ?>" 
                                    data-stok-adi="<?php echo htmlspecialchars($urun['stok_adi']); ?>">
                                <?php echo htmlspecialchars($urun['stok_kodu'] . ' - ' . $urun['stok_adi']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-tag"></i> Ürün Adı *
                    </label>
                    <input type="text" name="urun_adi" id="urun_adi" required value="<?php echo htmlspecialchars($ssh['urun_adi']); ?>" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" placeholder="Ürün adını giriniz">
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-cog"></i> Parça Adı
                    </label>
                    <input type="text" name="parca_adi" value="<?php echo htmlspecialchars($ssh['parca_adi']); ?>" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" placeholder="Parça adını giriniz (opsiyonel)">
                </div>
            </div>
        </div>
        
        <!-- SSH Detayları -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-tools"></i> SSH Detayları
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-exclamation-triangle"></i> SSH Nedeni *
                    </label>
                    <select name="ssh_nedeni" required style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="">Neden seçiniz...</option>
                        <option value="hasar" <?php echo ($ssh['ssh_nedeni'] == 'hasar') ? 'selected' : ''; ?>>Hasar</option>
                        <option value="iade" <?php echo ($ssh['ssh_nedeni'] == 'iade') ? 'selected' : ''; ?>>İade</option>
                        <option value="degisim" <?php echo ($ssh['ssh_nedeni'] == 'degisim') ? 'selected' : ''; ?>>Değişim</option>
                        <option value="garanti" <?php echo ($ssh['ssh_nedeni'] == 'garanti') ? 'selected' : ''; ?>>Garanti</option>
                        <option value="kalite_sorunu" <?php echo ($ssh['ssh_nedeni'] == 'kalite_sorunu') ? 'selected' : ''; ?>>Kalite Sorunu</option>
                        <option value="yanlis_urun" <?php echo ($ssh['ssh_nedeni'] == 'yanlis_urun') ? 'selected' : ''; ?>>Yanlış Ürün</option>
                        <option value="diger" <?php echo ($ssh['ssh_nedeni'] == 'diger') ? 'selected' : ''; ?>>Diğer</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-source"></i> SSH Kaynağı *
                    </label>
                    <select name="ssh_kaynagi" required style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="">Kaynak seçiniz...</option>
                        <option value="musteri" <?php echo ($ssh['ssh_kaynagi'] == 'musteri') ? 'selected' : ''; ?>>Müşteri</option>
                        <option value="kargo" <?php echo ($ssh['ssh_kaynagi'] == 'kargo') ? 'selected' : ''; ?>>Kargo</option>
                        <option value="uretim" <?php echo ($ssh['ssh_kaynagi'] == 'uretim') ? 'selected' : ''; ?>>Üretim</option>
                        <option value="kalite_kontrol" <?php echo ($ssh['ssh_kaynagi'] == 'kalite_kontrol') ? 'selected' : ''; ?>>Kalite Kontrol</option>
                        <option value="diger" <?php echo ($ssh['ssh_kaynagi'] == 'diger') ? 'selected' : ''; ?>>Diğer</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-flag"></i> Durum
                    </label>
                    <select name="durum" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="beklemede" <?php echo ($ssh['durum'] == 'beklemede') ? 'selected' : ''; ?>>Beklemede</option>
                        <option value="inceleniyor" <?php echo ($ssh['durum'] == 'inceleniyor') ? 'selected' : ''; ?>>İnceleniyor</option>
                        <option value="gonderildi" <?php echo ($ssh['durum'] == 'gonderildi') ? 'selected' : ''; ?>>Gönderildi</option>
                        <option value="onaylandi" <?php echo ($ssh['durum'] == 'onaylandi') ? 'selected' : ''; ?>>Onaylandı</option>
                        <option value="reddedildi" <?php echo ($ssh['durum'] == 'reddedildi') ? 'selected' : ''; ?>>Reddedildi</option>
                        <option value="tamamlandi" <?php echo ($ssh['durum'] == 'tamamlandi') ? 'selected' : ''; ?>>Tamamlandı</option>
                        <option value="iptal" <?php echo ($ssh['durum'] == 'iptal') ? 'selected' : ''; ?>>İptal</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Mevcut SSH Resimleri -->
        <?php if (!empty($resimler)): ?>
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-images"></i> Mevcut SSH Resimleri
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <?php foreach ($resimler as $resim): ?>
                <div style="border: 2px solid var(--gray-light); border-radius: 0.5rem; padding: 1rem; text-align: center;">
                    <?php if (file_exists($resim['dosya_yolu'])): ?>
                        <img src="<?php echo $resim['dosya_yolu']; ?>" alt="SSH Resim" style="max-width: 100%; height: 120px; object-fit: cover; border-radius: 0.375rem; margin-bottom: 0.5rem;">
                    <?php else: ?>
                        <div style="height: 120px; background: var(--gray-light); border-radius: 0.375rem; display: flex; align-items: center; justify-content: center; margin-bottom: 0.5rem;">
                            <i class="fas fa-image" style="font-size: 2rem; color: var(--gray);"></i>
                        </div>
                    <?php endif; ?>
                    <small style="color: var(--gray); display: block;"><?php echo htmlspecialchars($resim['dosya_adi']); ?></small>
                    <small style="color: var(--gray);"><?php echo date('d.m.Y H:i', strtotime($resim['yuklenme_tarihi'])); ?></small>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Yeni SSH Resimleri -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-images"></i> Yeni SSH Resimleri Ekle (Opsiyonel)
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-image"></i> Yeni Resim 1
                    </label>
                    <input type="file" name="ssh_resim_1" accept="image/*" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                    <small style="color: var(--gray);">JPG, PNG, GIF formatları desteklenir (Max: 5MB)</small>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-image"></i> Yeni Resim 2
                    </label>
                    <input type="file" name="ssh_resim_2" accept="image/*" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                    <small style="color: var(--gray);">JPG, PNG, GIF formatları desteklenir (Max: 5MB)</small>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-image"></i> Yeni Resim 3
                    </label>
                    <input type="file" name="ssh_resim_3" accept="image/*" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                    <small style="color: var(--gray);">JPG, PNG, GIF formatları desteklenir (Max: 5MB)</small>
                </div>
            </div>
            <div style="background: #e3f2fd; padding: 1rem; border-radius: 0.5rem; margin-top: 1rem;">
                <p style="margin: 0; color: #1976d2; font-size: 0.875rem;">
                    <i class="fas fa-info-circle"></i>
                    <strong>Bilgi:</strong> Yeni resim yüklerseniz, mevcut resimlere ek olarak eklenecektir.
                </p>
            </div>
        </div>
        
        <!-- Açıklama -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-comment"></i> Açıklama
            </h4>
            <div class="form-group">
                <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                    <i class="fas fa-edit"></i> Detaylı Açıklama
                </label>
                <textarea name="aciklama" rows="4" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem; resize: vertical;" placeholder="SSH ile ilgili detaylı açıklama yazınız..."><?php echo htmlspecialchars($ssh['aciklama']); ?></textarea>
            </div>
        </div>
        
        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
            <a href="ssh_detay.php?id=<?php echo $ssh['id']; ?>" class="btn" style="background: var(--gray); text-decoration: none;">
                <i class="fas fa-times"></i>
                İptal
            </a>
            <button type="submit" class="btn">
                <i class="fas fa-save"></i>
                SSH Kaydını Güncelle
            </button>
        </div>
    </form>
</div>

<script>
// Müşteri seçildiğinde siparişleri yükle
document.getElementById('cari_id').addEventListener('change', function() {
    const cariId = this.value;
    const siparisSelect = document.getElementById('siparis_id');
    
    // Sipariş listesini temizle
    siparisSelect.innerHTML = '<option value="">Sipariş seçiniz...</option>';
    
    if (cariId) {
        // AJAX ile siparişleri getir
        fetch('ajax_get_orders.php?cari_id=' + cariId)
            .then(response => response.json())
            .then(data => {
                data.forEach(siparis => {
                    const option = document.createElement('option');
                    option.value = siparis.id;
                    option.textContent = `#${siparis.id} - ${siparis.created_at} (${siparis.toplam_tutar} TL)`;
                    <?php if ($ssh['siparis_id']): ?>
                    if (siparis.id == <?php echo $ssh['siparis_id']; ?>) {
                        option.selected = true;
                    }
                    <?php endif; ?>
                    siparisSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Siparişler yüklenirken hata:', error);
            });
    }
});

// Sayfa yüklendiğinde mevcut müşterinin siparişlerini yükle
if (document.getElementById('cari_id').value) {
    document.getElementById('cari_id').dispatchEvent(new Event('change'));
}

// Ürün seçildiğinde ürün adını otomatik doldur
function fillProductInfo() {
    const urunSelect = document.getElementById('urun_id');
    const selectedOption = urunSelect.options[urunSelect.selectedIndex];
    const urunAdiInput = document.getElementById('urun_adi');
    
    if (selectedOption.value) {
        const stokKodu = selectedOption.getAttribute('data-stok-kodu');
        const stokAdi = selectedOption.getAttribute('data-stok-adi');
        urunAdiInput.value = `${stokKodu} - ${stokAdi}`;
    }
}
</script>

<?php include 'includes/footer.php'; ?> 