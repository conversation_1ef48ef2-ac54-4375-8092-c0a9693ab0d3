<?php
session_start();
include '../config.php';
include 'includes/admin_check.php';

$page_title = "Hero Slide Yönetimi";
$current_page = "hero_slides";

// CRUD İşlemleri
if (isset($_POST['action'])) {
    if ($_POST['action'] == 'add') {
        $baslik = $_POST['baslik'];
        $aciklama = $_POST['aciklama'];
        $link_url = $_POST['link_url'];
        $button_text = $_POST['button_text'];
        $sira = $_POST['sira'];
        $aktif = isset($_POST['aktif']) ? 1 : 0;
        
        // Resim upload işlemi
        $resim_url = '';
        if (isset($_FILES['slider_image']) && $_FILES['slider_image']['error'] == 0) {
            // Dinamik yol belirleme (canlı ortam uyumlu)
            $upload_dir = dirname(__DIR__) . "/uploads/slider/";
            $web_path = "uploads/slider/";
            
            // Klasör yoksa oluştur
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES["slider_image"]["name"], PATHINFO_EXTENSION));
            $file_name = time() . "_" . uniqid() . "." . $file_extension;
            $target_file = $upload_dir . $file_name;
            
            // Dosya türü kontrolü
            $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'webp');
            if (in_array($file_extension, $allowed_types)) {
                // Dosya boyutu kontrolü (5MB max)
                if ($_FILES["slider_image"]["size"] <= 5000000) {
                    if (move_uploaded_file($_FILES["slider_image"]["tmp_name"], $target_file)) {
                        $resim_url = $web_path . $file_name;
                    } else {
                        $error_message = "Dosya yüklenirken hata oluştu!";
                    }
                } else {
                    $error_message = "Dosya boyutu çok büyük! (Max 5MB)";
                }
            } else {
                $error_message = "Sadece JPG, JPEG, PNG, GIF, WebP dosyaları kabul edilir!";
            }
        } else if (!empty($_POST['resim_url'])) {
            $resim_url = $_POST['resim_url'];
        }
        
        if (!isset($error_message) && !empty($resim_url)) {
        $sql = "INSERT INTO b2b_hero_slides (baslik, aciklama, resim_url, link_url, button_text, sira, aktif) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('sssssii', $baslik, $aciklama, $resim_url, $link_url, $button_text, $sira, $aktif);
        $stmt->execute();
        $success_message = "Hero slide başarıyla eklendi!";
        } else if (!isset($error_message)) {
            $error_message = "Lütfen bir resim yükleyin veya resim URL'si girin!";
        }
    }
    
    if ($_POST['action'] == 'edit') {
        $id = $_POST['id'];
        $baslik = $_POST['baslik'];
        $aciklama = $_POST['aciklama'];
        $link_url = $_POST['link_url'];
        $button_text = $_POST['button_text'];
        $sira = $_POST['sira'];
        $aktif = isset($_POST['aktif']) ? 1 : 0;
        
        // Mevcut resim URL'sini al
        $sql = "SELECT resim_url FROM b2b_hero_slides WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $current_slide = $result->fetch_assoc();
        $resim_url = $current_slide['resim_url'];
        
        // Yeni resim upload edildi mi?
        if (isset($_FILES['slider_image']) && $_FILES['slider_image']['error'] == 0) {
            // Dinamik yol belirleme (canlı ortam uyumlu)
            $upload_dir = dirname(__DIR__) . "/uploads/slider/";
            $web_path = "uploads/slider/";
            
            // Klasör yoksa oluştur
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES["slider_image"]["name"], PATHINFO_EXTENSION));
            $file_name = time() . "_" . uniqid() . "." . $file_extension;
            $target_file = $upload_dir . $file_name;
            
            // Dosya türü kontrolü
            $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'webp');
            if (in_array($file_extension, $allowed_types)) {
                // Dosya boyutu kontrolü (5MB max)
                if ($_FILES["slider_image"]["size"] <= 5000000) {
                    if (move_uploaded_file($_FILES["slider_image"]["tmp_name"], $target_file)) {
                        // Eski dosyayı sil (eğer local dosya ise)
                        if (strpos($resim_url, 'uploads/slider/') !== false) {
                            $old_file = dirname(__DIR__) . "/" . $resim_url;
                            if (file_exists($old_file)) {
                                unlink($old_file);
                            }
                        }
                        $resim_url = $web_path . $file_name;
                    } else {
                        $error_message = "Dosya yüklenirken hata oluştu!";
                    }
                } else {
                    $error_message = "Dosya boyutu çok büyük! (Max 5MB)";
                }
            } else {
                $error_message = "Sadece JPG, JPEG, PNG, GIF, WebP dosyaları kabul edilir!";
            }
        } else if (!empty($_POST['resim_url']) && $_POST['resim_url'] != $resim_url) {
            $resim_url = $_POST['resim_url'];
        }
        
        if (!isset($error_message)) {
            $sql = "UPDATE b2b_hero_slides SET baslik=?, aciklama=?, resim_url=?, link_url=?, button_text=?, sira=?, aktif=? WHERE id=?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('sssssiii', $baslik, $aciklama, $resim_url, $link_url, $button_text, $sira, $aktif, $id);
        $stmt->execute();
        $success_message = "Hero slide başarıyla güncellendi!";
        }
    }
    
    if ($_POST['action'] == 'update_order') {
        $order_data = json_decode($_POST['order_data'], true);
        
        if ($order_data && is_array($order_data)) {
            foreach ($order_data as $item) {
                $id = intval($item['id']);
                $order = intval($item['order']);
                
                $sql = "UPDATE b2b_hero_slides SET sira = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('ii', $order, $id);
                $stmt->execute();
            }
            echo "OK"; // AJAX response
            exit;
        }
    }
}

if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    $sql = "DELETE FROM b2b_hero_slides WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $success_message = "Hero slide başarıyla silindi!";
}

// Slide'ları getir
$sql = "SELECT * FROM b2b_hero_slides ORDER BY sira, id";
$result = $conn->query($sql);
$slides = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $slides[] = $row;
    }
}

include 'includes/header.php';
?>

<style>
.slide-preview {
    position: relative;
    height: 200px;
    background-size: cover;
    background-position: center;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.slide-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 1rem;
}

.slide-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-small {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-light);
    border-radius: 0.5rem;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

/* Drag & Drop Sıralama */
.slide-item {
    cursor: move;
    transition: transform 0.2s ease;
    position: relative;
}

.slide-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.slide-item.sortable-ghost {
    opacity: 0.5;
    transform: rotate(2deg);
}

.slide-item.sortable-chosen {
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.drag-handle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    font-weight: 600;
    border-radius: 0.5rem 0.5rem 0 0;
    cursor: grab;
}

.drag-handle:active {
    cursor: grabbing;
}

.drag-handle i {
    font-size: 1.2rem;
    opacity: 0.8;
}

.sort-order {
    background: rgba(255,255,255,0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
}
</style>

<div class="container">
    <div class="page-header">
        <h1><i class="fas fa-images"></i> Hero Slide Yönetimi</h1>
        <button class="btn btn-primary" onclick="openModal('addModal')">
            <i class="fas fa-plus"></i> Yeni Slide Ekle
        </button>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <div class="grid grid-3" id="sortable-slides">
        <?php foreach ($slides as $slide): ?>
            <div class="card slide-item" data-id="<?php echo $slide['id']; ?>">
                <div class="drag-handle">
                    <i class="fas fa-grip-vertical"></i>
                    <span class="sort-order">Sıra: <?php echo $slide['sira']; ?></span>
                </div>
                <?php 
                // Admin paneli için resim yolu düzeltmesi
                $image_url = $slide['resim_url'];
                if (strpos($image_url, 'uploads/slider/') === 0) {
                    $image_url = '../' . $image_url;
                }
                ?>
                <div class="slide-preview" style="background-image: url('<?php echo htmlspecialchars($image_url); ?>');">
                    <div class="slide-overlay">
                        <h3><?php echo htmlspecialchars($slide['baslik']); ?></h3>
                        <p><?php echo htmlspecialchars(substr($slide['aciklama'], 0, 100)); ?>...</p>
                    </div>
                </div>
                
                <div class="card-content">
                    <div class="slide-info">
                        <div class="status-badges">
                            <span class="badge <?php echo $slide['aktif'] ? 'badge-success' : 'badge-danger'; ?>">
                                <?php echo $slide['aktif'] ? 'Aktif' : 'Pasif'; ?>
                            </span>
                            <span class="badge badge-info">Sıra: <?php echo $slide['sira']; ?></span>
                        </div>
                        
                        <div class="slide-details">
                            <p><strong>Link:</strong> <?php echo htmlspecialchars($slide['link_url']); ?></p>
                            <p><strong>Buton:</strong> <?php echo htmlspecialchars($slide['button_text']); ?></p>
                        </div>
                    </div>
                    
                    <div class="slide-actions">
                        <button class="btn btn-warning btn-small" onclick="editSlide(<?php echo htmlspecialchars(json_encode($slide)); ?>)">
                            <i class="fas fa-edit"></i> Düzenle
                        </button>
                        <a href="?delete=<?php echo $slide['id']; ?>" 
                           class="btn btn-danger btn-small"
                           onclick="return confirm('Bu slide\'ı silmek istediğinizden emin misiniz?')">
                            <i class="fas fa-trash"></i> Sil
                        </a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Add Modal -->
<div class="modal" id="addModal">
    <div class="modal-content">
        <h2><i class="fas fa-plus-circle"></i> Yeni Hero Slide Ekle</h2>
        <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" value="add">
            
            <div class="form-group">
                <label>Başlık *</label>
                <input type="text" name="baslik" required>
            </div>
            
            <div class="form-group">
                <label>Açıklama</label>
                <textarea name="aciklama" placeholder="Slide açıklaması..."></textarea>
            </div>
            
            <div class="form-group">
                <label>Resim Yükle (Önerilen: 1920x500px)</label>
                <input type="file" name="slider_image" accept="image/*" onchange="handleFileSelect(this, 'add')">
                <small style="color: #666; font-size: 0.85rem;">JPG, PNG, GIF, WebP (Max 5MB)</small>
            </div>
            
            <div class="form-group">
                <label>Veya Resim URL'si</label>
                <input type="url" name="resim_url" placeholder="https://example.com/image.jpg">
                <small style="color: #666; font-size: 0.85rem;">Dosya yüklemek yerine URL de girebilirsiniz</small>
            </div>
            
            <div class="form-group">
                <label>Link URL</label>
                <input type="text" name="link_url" placeholder="urunler.php?kategori=...">
            </div>
            
            <div class="form-group">
                <label>Buton Metni</label>
                <input type="text" name="button_text" placeholder="İncele">
            </div>
            
            <div class="form-group">
                <label>Sıra</label>
                <input type="number" name="sira" value="1" min="1">
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" name="aktif" id="aktif_add" checked>
                    <label for="aktif_add">Aktif</label>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> Kaydet
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('addModal')">
                    <i class="fas fa-times"></i> İptal
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal" id="editModal">
    <div class="modal-content">
        <h2><i class="fas fa-edit"></i> Hero Slide Düzenle</h2>
        <form method="POST" id="editForm" enctype="multipart/form-data">
            <input type="hidden" name="action" value="edit">
            <input type="hidden" name="id" id="edit_id">
            
            <div class="form-group">
                <label>Başlık *</label>
                <input type="text" name="baslik" id="edit_baslik" required>
            </div>
            
            <div class="form-group">
                <label>Açıklama</label>
                <textarea name="aciklama" id="edit_aciklama"></textarea>
            </div>
            
            <div class="form-group">
                <label>Yeni Resim Yükle (Önerilen: 1920x500px)</label>
                <input type="file" name="slider_image" accept="image/*" onchange="handleFileSelect(this, 'edit')">
                <small style="color: #666; font-size: 0.85rem;">JPG, PNG, GIF, WebP (Max 5MB)</small>
            </div>
            
            <div class="form-group">
                <label>Veya Resim URL'si</label>
                <input type="url" name="resim_url" id="edit_resim_url">
                <small style="color: #666; font-size: 0.85rem;">Dosya yüklemek yerine URL de girebilirsiniz</small>
            </div>
            
            <div class="form-group">
                <label>Link URL</label>
                <input type="text" name="link_url" id="edit_link_url">
            </div>
            
            <div class="form-group">
                <label>Buton Metni</label>
                <input type="text" name="button_text" id="edit_button_text">
            </div>
            
            <div class="form-group">
                <label>Sıra</label>
                <input type="number" name="sira" id="edit_sira" min="1">
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" name="aktif" id="edit_aktif">
                    <label for="edit_aktif">Aktif</label>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> Güncelle
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">
                    <i class="fas fa-times"></i> İptal
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.add('active');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('active');
}

function editSlide(slide) {
    document.getElementById('edit_id').value = slide.id;
    document.getElementById('edit_baslik').value = slide.baslik;
    document.getElementById('edit_aciklama').value = slide.aciklama;
    document.getElementById('edit_resim_url').value = slide.resim_url;
    document.getElementById('edit_link_url').value = slide.link_url;
    document.getElementById('edit_button_text').value = slide.button_text;
    document.getElementById('edit_sira').value = slide.sira;
    document.getElementById('edit_aktif').checked = slide.aktif == 1;
    
    openModal('editModal');
}

// Modal dışına tıklayınca kapat
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.classList.remove('active');
    }
}

// Dosya seçimi işlemi
function handleFileSelect(input, formType) {
    const file = input.files[0];
    if (file) {
        // Dosya boyutu kontrolü (5MB)
        if (file.size > 5000000) {
            alert('Dosya boyutu çok büyük! Maksimum 5MB olmalıdır.');
            input.value = '';
            return;
        }
        
        // Dosya türü kontrolü
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('Sadece JPG, PNG, GIF, WebP dosyaları kabul edilir!');
            input.value = '';
            return;
        }
        
        // Dosya seçilince URL input'unu temizle
        if (formType === 'add') {
            document.querySelector('input[name="resim_url"]').value = '';
        } else {
            document.getElementById('edit_resim_url').value = '';
        }
        
        console.log('Dosya seçildi:', file.name, 'Boyut:', (file.size/1024/1024).toFixed(2) + 'MB');
    }
}

// Drag & Drop Sıralama
document.addEventListener('DOMContentLoaded', function() {
    const sortableList = document.getElementById('sortable-slides');
    
    if (sortableList) {
        // SortableJS kütüphanesini yükle
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js';
        script.onload = function() {
            initSortable();
        };
        document.head.appendChild(script);
    }
});

function initSortable() {
    const sortableList = document.getElementById('sortable-slides');
    
    new Sortable(sortableList, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        handle: '.drag-handle',
        onEnd: function(evt) {
            updateSlideOrder();
        }
    });
}

function updateSlideOrder() {
    const slides = document.querySelectorAll('.slide-item');
    const orderData = [];
    
    slides.forEach((slide, index) => {
        const id = slide.dataset.id;
        const newOrder = index + 1;
        orderData.push({ id: id, order: newOrder });
        
        // Sıra numarasını güncelle
        const sortOrderSpan = slide.querySelector('.sort-order');
        if (sortOrderSpan) {
            sortOrderSpan.textContent = 'Sıra: ' + newOrder;
        }
    });
    
    // AJAX ile veritabanını güncelle
    fetch('hero_slide_yonetimi.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=update_order&order_data=' + encodeURIComponent(JSON.stringify(orderData))
    })
    .then(response => response.text())
    .then(data => {
        console.log('Sıralama güncellendi');
        
        // Başarı mesajı göster
        showNotification('Slide sıralaması güncellendi!', 'success');
    })
    .catch(error => {
        console.error('Hata:', error);
        showNotification('Sıralama güncellenirken hata oluştu!', 'error');
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'}`;
    notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>

<?php include 'includes/footer.php'; ?> 