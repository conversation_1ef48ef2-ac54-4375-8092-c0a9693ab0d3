// 🔧 RECETE DÜZENLE - SORUN ÇÖZÜCÜ JAVASCRIPT
// Bu dosya recete düzenleme sayfasındaki tüm sorunları çözer

console.log('🚀 Recete Düzenle Fixed JavaScript başlatıldı');

// Global değişkenler
let currentReceteId = null;
let currentUrunId = null;
let malzemelerCache = {
    sunta: [],
    pvc: [],
    hirdavat: [],
    koli: []
};

// Debug fonksiyonu
function debugLog(message, type = 'info', data = null) {
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
        'info': 'color: #3498db; background: #ecf0f1; padding: 2px 5px;',
        'success': 'color: #27ae60; background: #d5f4e6; padding: 2px 5px;',
        'warning': 'color: #f39c12; background: #fef9e7; padding: 2px 5px;',
        'error': 'color: #e74c3c; background: #fadbd8; padding: 2px 5px;'
    };
    
    console.log(`%c[${timestamp}] ${message}`, colors[type] || colors.info);
    if (data) {
        console.log('📊 Data:', data);
    }
}

// Ekranda debug mesajı göster
function showDebugMessage(message, type = 'info') {
    const debugDiv = document.getElementById('debug-messages') || createDebugDiv();
    const colors = {
        'success': '#28a745',
        'error': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8'
    };
    
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        background: ${colors[type]}; 
        color: white; 
        padding: 8px; 
        margin: 3px 0; 
        border-radius: 4px; 
        font-family: monospace; 
        font-size: 12px;
    `;
    messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> ${message}`;
    
    debugDiv.appendChild(messageDiv);
    
    // En fazla 10 mesaj göster
    while (debugDiv.children.length > 10) {
        debugDiv.removeChild(debugDiv.firstChild);
    }
}

function createDebugDiv() {
    const debugDiv = document.createElement('div');
    debugDiv.id = 'debug-messages';
    debugDiv.style.cssText = `
        position: fixed; 
        top: 10px; 
        right: 10px; 
        width: 300px; 
        max-height: 400px; 
        overflow-y: auto; 
        z-index: 9999; 
        background: rgba(0,0,0,0.1); 
        border-radius: 5px; 
        padding: 5px;
    `;
    document.body.appendChild(debugDiv);
    return debugDiv;
}

// 1. ÜRÜN YÜKLEME SORUNU ÇÖZÜMÜ
function loadUrunler() {
    debugLog('🏷️ Ürünler yükleniyor...', 'info');
    showDebugMessage('🏷️ Ürünler yükleniyor...');

    fetch('get_urunler.php')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            debugLog('📦 Ürün API yanıtı:', 'info', data);

            if (data.success && data.urunler) {
                const urunSelect = document.getElementById('urun-select');
                if (urunSelect) {
                    urunSelect.innerHTML = '<option value="">Seçiniz...</option>';

                    // Ürünleri kategorilere göre grupla
                    const kategoriler = {
                        'perakende': '📦 PERAKENDE ÜRÜNLER',
                        'toptan_bilesensiz': '🚚 TOPTAN BİLEŞENSİZ',
                        'toptan_bilesenli': '🏭 TOPTAN BİLEŞENLİ',
                        'model_bilesen': '🧩 MODEL BİLEŞENLERİ',
                        'diger': '📋 DİĞER ÜRÜNLER'
                    };

                    // Her kategori için optgroup oluştur
                    Object.keys(kategoriler).forEach(kategoriKey => {
                        const kategoriUrunler = data.urunler.filter(u => u.urun_tipi === kategoriKey);
                        if (kategoriUrunler.length > 0) {
                            const optgroup = document.createElement('optgroup');
                            optgroup.label = kategoriler[kategoriKey];

                            kategoriUrunler.forEach(urun => {
                                const option = document.createElement('option');
                                option.value = urun.id;
                                option.textContent = `${urun.stok_kodu || urun.id} - ${urun.gosterim_adi || urun.stok_adi}`;
                                optgroup.appendChild(option);
                            });

                            urunSelect.appendChild(optgroup);
                        }
                    });

                    // Mevcut ürünü seç
                    if (currentUrunId) {
                        urunSelect.value = currentUrunId;
                        debugLog(`✅ Mevcut ürün seçildi: ${currentUrunId}`, 'success');
                        showDebugMessage(`✅ Mevcut ürün seçildi: ${currentUrunId}`, 'success');
                    }

                    debugLog(`✅ ${data.urunler.length} ürün yüklendi (${data.sistem_tipi} sistem)`, 'success');
                    showDebugMessage(`✅ ${data.urunler.length} ürün yüklendi`, 'success');
                } else {
                    debugLog('❌ urun-select elementi bulunamadı', 'error');
                    showDebugMessage('❌ urun-select elementi bulunamadı', 'error');
                }
            } else {
                debugLog('❌ Ürün API hatası:', 'error', data);
                showDebugMessage('❌ Ürün yükleme hatası', 'error');
            }
        })
        .catch(error => {
            debugLog('❌ Ürün yükleme AJAX hatası:', 'error', error);
            showDebugMessage(`❌ Ürün AJAX hatası: ${error.message}`, 'error');
        });
}

// 2. MALZEME YÜKLEME SORUNU ÇÖZÜMÜ
function loadAllMalzemeler() {
    debugLog('📦 Tüm malzemeler yükleniyor...', 'info');
    showDebugMessage('📦 Tüm malzemeler yükleniyor...');
    
    const kategoriler = ['sunta', 'pvc', 'hirdavat', 'koli'];
    
    kategoriler.forEach(kategori => {
        loadMalzemelerByKategori(kategori);
    });
}

function loadMalzemelerByKategori(kategori) {
    debugLog(`📦 ${kategori.toUpperCase()} malzemeler yükleniyor...`, 'info');
    
    fetch(`malzeme_detay.php?kategori=${kategori}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.malzemeler) {
                malzemelerCache[kategori] = data.malzemeler;
                
                // Select elementlerini doldur
                const selects = document.querySelectorAll(`select[name="${kategori}_malzeme[]"]`);
                selects.forEach(select => {
                    populateMalzemeSelect(select, data.malzemeler, kategori);
                });
                
                debugLog(`✅ ${data.malzemeler.length} ${kategori} malzemesi yüklendi`, 'success');
                showDebugMessage(`✅ ${data.malzemeler.length} ${kategori} malzemesi yüklendi`, 'success');
            } else {
                debugLog(`❌ ${kategori} malzeme yükleme hatası:`, 'error', data);
                showDebugMessage(`❌ ${kategori} malzeme yükleme hatası`, 'error');
            }
        })
        .catch(error => {
            debugLog(`❌ ${kategori} AJAX hatası:`, 'error', error);
            showDebugMessage(`❌ ${kategori} AJAX hatası: ${error.message}`, 'error');
        });
}

function populateMalzemeSelect(select, malzemeler, kategori) {
    if (!select) return;
    
    const currentValue = select.getAttribute('data-selected') || select.value;
    select.innerHTML = '<option value="">Seçiniz...</option>';
    
    malzemeler.forEach(malzeme => {
        const option = document.createElement('option');
        option.value = malzeme.id;
        option.textContent = `${malzeme.malz_kodu || malzeme.id} - ${malzeme.malzeme_adi}`;
        
        // Data attributeları ekle
        option.setAttribute('data-kod', malzeme.malz_kodu || '');
        option.setAttribute('data-ad', malzeme.malzeme_adi || '');
        option.setAttribute('data-birim', malzeme.birim || '');
        option.setAttribute('data-stok', malzeme.stok_adet || malzeme.stok_miktar || 0);
        
        // Kategoriye göre fiyat bilgisi
        if (kategori === 'sunta') {
            option.setAttribute('data-fiyat', malzeme.fiyat_cm2 || 0);
        } else if (kategori === 'koli') {
            // Koli için özel fiyat attribute'ları
            option.setAttribute('data-birim-fiyat-m2', malzeme.birim_fiyat_m2 || malzeme.miktar_m2 || malzeme.fiyat_m2 || 0);
            option.setAttribute('data-birim-fiyat-adet', malzeme.birim_fiyat_adet || malzeme.olcu || malzeme.fiyat_adet || 0);

            // Debug için koli fiyat bilgilerini logla
            debugLog(`📦 Koli malzeme fiyat bilgileri: ${malzeme.malzeme_adi}`, 'info', {
                birim_fiyat_m2: malzeme.birim_fiyat_m2,
                birim_fiyat_adet: malzeme.birim_fiyat_adet,
                miktar_m2: malzeme.miktar_m2,
                olcu: malzeme.olcu,
                fiyat_m2: malzeme.fiyat_m2,
                fiyat_adet: malzeme.fiyat_adet
            });
        } else {
            option.setAttribute('data-fiyat', malzeme.fiyat || 0);
        }
        
        select.appendChild(option);
    });
    
    // Mevcut değeri seç
    if (currentValue) {
        select.value = currentValue;
        // Seçim değişikliğini tetikle
        const event = new Event('change', { bubbles: true });
        select.dispatchEvent(event);
    }
}

// 3. MALZEME DEĞİŞİNCE SATIR DOLDURMA SORUNU ÇÖZÜMÜ
function setupMalzemeChangeListeners() {
    debugLog('🎯 Malzeme change listener\'ları kuruluyor...', 'info');
    
    // Sunta malzeme değişikliği
    document.addEventListener('change', function(e) {
        if (e.target.matches('select[name="sunta_malzeme[]"]')) {
            handleSuntaMalzemeChange(e.target);
        } else if (e.target.matches('select[name="pvc_malzeme[]"]')) {
            handlePvcMalzemeChange(e.target);
        } else if (e.target.matches('select[name="hirdavat_malzeme[]"]')) {
            handleHirdavatMalzemeChange(e.target);
        } else if (e.target.matches('select[name="koli_malzeme[]"]')) {
            handleKoliMalzemeChange(e.target);
        }
    });
    
    debugLog('✅ Malzeme change listener\'ları kuruldu', 'success');
}

function handleSuntaMalzemeChange(select) {
    const row = select.closest('tr');
    if (!row) return;
    
    const selectedOption = select.selectedOptions[0];
    if (!selectedOption || !selectedOption.value) {
        clearRowInputs(row, ['sunta_malzeme_adi[]', 'sunta_birim[]', 'sunta_stok[]', 'sunta_fiyat_cm2[]']);
        return;
    }
    
    // Satır bilgilerini doldur
    fillRowInput(row, 'sunta_malzeme_adi[]', selectedOption.getAttribute('data-ad'));
    fillRowInput(row, 'sunta_birim[]', selectedOption.getAttribute('data-birim'));
    fillRowInput(row, 'sunta_stok[]', selectedOption.getAttribute('data-stok'));
    fillRowInput(row, 'sunta_fiyat_cm2[]', selectedOption.getAttribute('data-fiyat'));
    
    debugLog('✅ Sunta satırı dolduruldu', 'success');
    showDebugMessage('✅ Sunta satırı dolduruldu', 'success');
    
    // Hesaplamayı tetikle
    calculateSuntaRow(row);
}

function handlePvcMalzemeChange(select) {
    const row = select.closest('tr');
    if (!row) return;
    
    const selectedOption = select.selectedOptions[0];
    if (!selectedOption || !selectedOption.value) {
        clearRowInputs(row, ['pvc_malzeme_adi[]', 'pvc_birim[]', 'pvc_stok[]', 'pvc_fiyat[]']);
        return;
    }
    
    fillRowInput(row, 'pvc_malzeme_adi[]', selectedOption.getAttribute('data-ad'));
    fillRowInput(row, 'pvc_birim[]', selectedOption.getAttribute('data-birim'));
    fillRowInput(row, 'pvc_stok[]', selectedOption.getAttribute('data-stok'));
    fillRowInput(row, 'pvc_fiyat[]', selectedOption.getAttribute('data-fiyat'));
    
    debugLog('✅ PVC satırı dolduruldu', 'success');
    showDebugMessage('✅ PVC satırı dolduruldu', 'success');
    
    calculatePvcRow(row);
}

function handleHirdavatMalzemeChange(select) {
    const row = select.closest('tr');
    if (!row) return;
    
    const selectedOption = select.selectedOptions[0];
    if (!selectedOption || !selectedOption.value) {
        clearRowInputs(row, ['hirdavat_malzeme_adi[]', 'hirdavat_birim[]', 'hirdavat_stok[]', 'hirdavat_fiyat[]']);
        return;
    }
    
    fillRowInput(row, 'hirdavat_malzeme_adi[]', selectedOption.getAttribute('data-ad'));
    fillRowInput(row, 'hirdavat_birim[]', selectedOption.getAttribute('data-birim'));
    fillRowInput(row, 'hirdavat_stok[]', selectedOption.getAttribute('data-stok'));
    fillRowInput(row, 'hirdavat_fiyat[]', selectedOption.getAttribute('data-fiyat'));
    
    debugLog('✅ Hırdavat satırı dolduruldu', 'success');
    showDebugMessage('✅ Hırdavat satırı dolduruldu', 'success');
    
    calculateHirdavatRow(row);
}

function handleKoliMalzemeChange(select) {
    const row = select.closest('tr');
    if (!row) return;

    const selectedOption = select.selectedOptions[0];
    if (!selectedOption || !selectedOption.value) {
        clearRowInputs(row, ['koli_malzeme_adi[]', 'koli_birim[]', 'koli_stok[]', 'koli_birim_fiyat_m2[]', 'koli_birim_fiyat_adet[]']);
        return;
    }

    // Koli malzeme bilgilerini doldur
    fillRowInput(row, 'koli_malzeme_adi[]', selectedOption.getAttribute('data-ad'));
    fillRowInput(row, 'koli_birim[]', selectedOption.getAttribute('data-birim'));
    fillRowInput(row, 'koli_stok[]', selectedOption.getAttribute('data-stok'));

    // Birim fiyat bilgilerini doldur
    const birimFiyatM2 = selectedOption.getAttribute('data-birim-fiyat-m2') || '0';
    const birimFiyatAdet = selectedOption.getAttribute('data-birim-fiyat-adet') || '0';

    fillRowInput(row, 'koli_birim_fiyat_m2[]', birimFiyatM2);
    fillRowInput(row, 'koli_birim_fiyat_adet[]', birimFiyatAdet);

    // Debug: Tüm attribute'ları kontrol et
    debugLog(`🔍 Koli malzeme attribute'ları:`, 'info', {
        'data-ad': selectedOption.getAttribute('data-ad'),
        'data-birim': selectedOption.getAttribute('data-birim'),
        'data-stok': selectedOption.getAttribute('data-stok'),
        'data-birim-fiyat-m2': selectedOption.getAttribute('data-birim-fiyat-m2'),
        'data-birim-fiyat-adet': selectedOption.getAttribute('data-birim-fiyat-adet'),
        'innerHTML': selectedOption.innerHTML
    });

    debugLog(`✅ Koli satırı dolduruldu - M2: ${birimFiyatM2}, Adet: ${birimFiyatAdet}`, 'success');
    showDebugMessage(`✅ Koli satırı dolduruldu - M2: ${birimFiyatM2}, Adet: ${birimFiyatAdet}`, 'success');

    calculateKoliRow(row);
}

// Yardımcı fonksiyonlar
function fillRowInput(row, inputName, value) {
    const input = row.querySelector(`input[name="${inputName}"], select[name="${inputName}"]`);
    if (input && value !== null && value !== undefined) {
        input.value = value;
    }
}

function clearRowInputs(row, inputNames) {
    inputNames.forEach(inputName => {
        const input = row.querySelector(`input[name="${inputName}"], select[name="${inputName}"]`);
        if (input) {
            input.value = '';
        }
    });
}

// 4. HESAPLAMA SORUNLARI ÇÖZÜMÜ
function calculateSuntaRow(row) {
    const adet = parseFloat(row.querySelector('input[name="adet[]"]')?.value || 0);
    const boy = parseFloat(row.querySelector('input[name="sunta_kesim_boy[]"]')?.value || 0);
    const en = parseFloat(row.querySelector('input[name="sunta_kesim_en[]"]')?.value || 0);
    const fiyatCm2 = parseFloat(row.querySelector('input[name="sunta_fiyat_cm2[]"]')?.value || 0);
    
    const toplamCm2 = adet * boy * en;
    const toplamMaliyet = toplamCm2 * fiyatCm2;
    
    fillRowInput(row, 'toplam_cm2[]', toplamCm2.toFixed(2));
    fillRowInput(row, 'sunta_toplam_maliyet[]', toplamMaliyet.toFixed(2));
    
    debugLog(`💰 Sunta hesaplama: ${adet} × ${boy} × ${en} × ${fiyatCm2} = ${toplamMaliyet.toFixed(2)}`, 'info');
}

function calculatePvcRow(row) {
    // PVC hesaplama mantığı buraya gelecek
    debugLog('💰 PVC hesaplama yapılıyor...', 'info');
}

function calculateHirdavatRow(row) {
    const adet = parseFloat(row.querySelector('input[name="hirdavat_adet[]"]')?.value || 0);
    const fiyat = parseFloat(row.querySelector('input[name="hirdavat_fiyat[]"]')?.value || 0);
    
    const toplamMaliyet = adet * fiyat;
    fillRowInput(row, 'hirdavat_toplam_maliyet[]', toplamMaliyet.toFixed(2));
    
    debugLog(`💰 Hırdavat hesaplama: ${adet} × ${fiyat} = ${toplamMaliyet.toFixed(2)}`, 'info');
}

function calculateKoliRow(element) {
    // Element'ten row'u bul
    const row = element.closest ? element.closest('tr') : element.parentElement.parentElement;
    if (!row) {
        debugLog('❌ Koli satırı bulunamadı', 'error');
        return;
    }

    // Koli türünü kontrol et
    const koliTuruSelect = row.querySelector('select[name="koli_turu[]"]');
    const koliTuru = koliTuruSelect ? koliTuruSelect.value : '';

    // Önce m2 hesapla (ölçü × cm / 10000)
    const olcuInput = row.querySelector('input[name="koli_olcu[]"]');
    const cmInput = row.querySelector('input[name="koli_cm[]"]');
    const m2Input = row.querySelector('input[name="koli_m2[]"]');

    if (olcuInput && cmInput && m2Input) {
        const olcu = parseFloat(olcuInput.value || 0);
        const cm = parseFloat(cmInput.value || 0);

        if (olcu > 0 && cm > 0) {
            const m2 = (olcu * cm) / 10000;
            m2Input.value = m2.toFixed(4);

            // Koli türüne göre log mesajı
            if (koliTuru === 'OZEL_OLCU' || koliTuru.includes('ÖZEL') || koliTuru.includes('Ã–ZEL') || koliTuru.includes('OZEL')) {
                debugLog(`📐 ÖZEL ÖLÇÜ m2 hesaplama: ${olcu}cm (En) × ${cm}cm (Boy) / 10000 = ${m2.toFixed(4)} m²`, 'info');
            } else {
                debugLog(`📦 Z-KARTON m2 hesaplama: ${olcu} × ${cm} / 10000 = ${m2.toFixed(4)} m²`, 'info');
            }
        }
    }

    // Koli hesaplama için gerekli değerleri al
    const m2 = parseFloat(row.querySelector('input[name="koli_m2[]"]')?.value || 0);
    const birimFiyatM2 = parseFloat(row.querySelector('input[name="koli_birim_fiyat_m2[]"]')?.value || 0);
    const birimFiyatAdet = parseFloat(row.querySelector('input[name="koli_birim_fiyat_adet[]"]')?.value || 0);
    const adetMiktar = parseFloat(row.querySelector('input[name="koli_adet_miktar[]"]')?.value || 1);

    // Debug: Değerleri logla
    debugLog(`📊 Koli hesaplama değerleri:`, 'info', {
        m2: m2,
        birimFiyatM2: birimFiyatM2,
        birimFiyatAdet: birimFiyatAdet,
        adetMiktar: adetMiktar
    });

    let toplamFiyat = 0;

    // Önce m2 bazlı hesaplamayı dene
    if (m2 > 0 && birimFiyatM2 > 0) {
        toplamFiyat = m2 * birimFiyatM2 * adetMiktar;
        debugLog(`💰 Koli M2 hesaplama: ${m2} m² × ${birimFiyatM2} TL/m² × ${adetMiktar} adet = ${toplamFiyat.toFixed(2)} TL`, 'success');
    }
    // Eğer m2 hesaplama yoksa adet bazlı hesapla
    else if (birimFiyatAdet > 0) {
        toplamFiyat = birimFiyatAdet * adetMiktar;
        debugLog(`💰 Koli Adet hesaplama: ${birimFiyatAdet} TL/adet × ${adetMiktar} adet = ${toplamFiyat.toFixed(2)} TL`, 'success');
    }
    // Hiçbiri yoksa 0
    else {
        debugLog(`⚠️ Koli hesaplama: Fiyat bilgisi bulunamadı (M2: ${birimFiyatM2}, Adet: ${birimFiyatAdet})`, 'warning');
        toplamFiyat = 0;
    }

    // Toplam fiyatı güncelle
    const toplamFiyatInput = row.querySelector('input[name="koli_toplam_fiyat[]"]');
    if (toplamFiyatInput) {
        toplamFiyatInput.value = toplamFiyat.toFixed(2);
        debugLog(`✅ Koli toplam fiyat güncellendi: ${toplamFiyat.toFixed(2)} TL`, 'success');
    } else {
        debugLog('❌ Koli toplam fiyat input bulunamadı', 'error');
    }

    showDebugMessage(`💰 Koli hesaplama: ${toplamFiyat.toFixed(2)} TL`, toplamFiyat > 0 ? 'success' : 'warning');
}

// 5. SAYFA YÜKLENDİĞİNDE BAŞLATMA
document.addEventListener('DOMContentLoaded', function() {
    debugLog('🚀 DOM hazır - Sistem başlatılıyor...', 'success');
    showDebugMessage('🚀 Sistem başlatılıyor...', 'info');

    // URL'den reçete ID'sini al
    const urlParams = new URLSearchParams(window.location.search);
    currentReceteId = urlParams.get('id');

    // PHP'den current ürün ID'sini al
    const currentUrunInput = document.querySelector('input[name="current_urun_id"]');
    if (currentUrunInput && currentUrunInput.value) {
        currentUrunId = currentUrunInput.value;
        debugLog(`🎯 Mevcut ürün ID: ${currentUrunId}`, 'info');
    }

    // Sistemleri sırayla başlat
    loadUrunler();

    // Malzemeler yüklendikten sonra mevcut verileri yükle
    setTimeout(() => {
        loadAllMalzemeler();

        // Malzemeler yüklendikten sonra mevcut reçete verilerini yükle
        setTimeout(() => {
            loadMevcutReceteVerileri();
        }, 2000);
    }, 1000);

    setupMalzemeChangeListeners();
    setupCalculationListeners();

    debugLog('✅ Tüm sistemler başlatıldı', 'success');
    showDebugMessage('✅ Tüm sistemler başlatıldı', 'success');
});

// 6. INPUT DEĞİŞİKLİKLERİNDE HESAPLAMA TETİKLEME
function setupCalculationListeners() {
    debugLog('🧮 Hesaplama listener\'ları kuruluyor...', 'info');

    // Sunta hesaplama listener'ları
    document.addEventListener('input', function(e) {
        const row = e.target.closest('tr');
        if (!row) return;

        if (e.target.matches('input[name="adet[]"], input[name="sunta_kesim_boy[]"], input[name="sunta_kesim_en[]"]')) {
            calculateSuntaRow(row);
        } else if (e.target.matches('input[name="pvc_adet[]"], input[name="pvc_bantlama_boy[]"], input[name="pvc_bantlama_en[]"], input[name="pvc_kesim_boy[]"], input[name="pvc_kesim_en[]"]')) {
            calculatePvcRow(row);
        } else if (e.target.matches('input[name="hirdavat_adet[]"]')) {
            calculateHirdavatRow(row);
        } else if (e.target.matches('input[name="koli_olcu[]"], input[name="koli_cm[]"], input[name="koli_adet_miktar[]"]')) {
            calculateKoliRow(row);
        }
    });

    // Koli türü değişikliği
    document.addEventListener('change', function(e) {
        if (e.target.matches('select[name="koli_turu[]"]')) {
            handleKoliTuruChange(e.target);
        }
    });

    debugLog('✅ Hesaplama listener\'ları kuruldu', 'success');
}

function handleKoliTuruChange(select) {
    const row = select.closest('tr');
    if (!row) return;

    const koliTuru = select.value;
    debugLog(`📦 Koli türü değişti: ${koliTuru}`, 'info');

    if (koliTuru === 'ÖZEL ÖLÇÜ') {
        calculateKoliOzelOlcu(row);
    } else if (koliTuru === 'Z-KARTON') {
        calculateKoliZKarton(row);
    }
}

function calculateKoliOzelOlcu(row) {
    const olcu = parseFloat(row.querySelector('input[name="koli_olcu[]"]')?.value || 0);
    const metre = parseFloat(row.querySelector('input[name="koli_cm[]"]')?.value || 0);

    if (olcu > 0 && metre > 0) {
        const metrekare = (olcu / 100) * metre;
        fillRowInput(row, 'koli_m2[]', metrekare.toFixed(4));

        debugLog(`📐 ÖZEL ÖLÇÜ: ${olcu} cm ÷ 100 × ${metre} m = ${metrekare} m²`, 'info');
        calculateKoliRow(row);
    }
}

function calculateKoliZKarton(row) {
    const olcu = parseFloat(row.querySelector('input[name="koli_olcu[]"]')?.value || 0);
    const metre = parseFloat(row.querySelector('input[name="koli_cm[]"]')?.value || 0);

    if (olcu > 0 && metre > 0) {
        const metrekare = (olcu / 100) * metre;
        fillRowInput(row, 'koli_m2[]', metrekare.toFixed(4));

        debugLog(`📐 Z-KARTON: ${olcu} cm ÷ 100 × ${metre} m = ${metrekare} m²`, 'info');
        calculateKoliRow(row);
    }
}

// 7. PVC HESAPLAMA DETAYI
function calculatePvcRow(row) {
    const adet = parseFloat(row.querySelector('input[name="pvc_adet[]"]')?.value || 0);
    const bantlamaBoy = parseFloat(row.querySelector('input[name="pvc_bantlama_boy[]"]')?.value || 0);
    const bantlamaEn = parseFloat(row.querySelector('input[name="pvc_bantlama_en[]"]')?.value || 0);
    const kesimBoy = parseFloat(row.querySelector('input[name="pvc_kesim_boy[]"]')?.value || 0);
    const kesimEn = parseFloat(row.querySelector('input[name="pvc_kesim_en[]"]')?.value || 0);
    const fiyat = parseFloat(row.querySelector('input[name="pvc_fiyat[]"]')?.value || 0);

    // PVC toplam metre hesaplama
    const bantlamaMetre = (bantlamaBoy + bantlamaEn) * 2 / 100; // cm'den m'ye
    const kesimMetre = (kesimBoy + kesimEn) * 2 / 100; // cm'den m'ye
    const toplamMetre = (bantlamaMetre + kesimMetre) * adet;
    const toplamMaliyet = toplamMetre * fiyat;

    fillRowInput(row, 'pvc_toplam_metre[]', toplamMetre.toFixed(2));
    fillRowInput(row, 'pvc_toplam_maliyet[]', toplamMaliyet.toFixed(2));

    debugLog(`💰 PVC hesaplama: ${toplamMetre.toFixed(2)} m × ${fiyat} = ${toplamMaliyet.toFixed(2)}`, 'info');
}

// 8. MEVCUT REÇETE VERİLERİNİ YÜKLEME
function loadMevcutReceteVerileri() {
    if (!currentReceteId) {
        debugLog('❌ Reçete ID bulunamadı', 'error');
        return;
    }

    debugLog(`📋 Mevcut reçete verileri yükleniyor... ID: ${currentReceteId}`, 'info');
    showDebugMessage('📋 Mevcut reçete verileri yükleniyor...');

    // Reçete satırlarını yükle
    fetch(`get_recete_satirlari.php?recete_id=${currentReceteId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.satirlar) {
                populateReceteSatirlari(data.satirlar);
                debugLog(`✅ ${data.satirlar.length} reçete satırı yüklendi`, 'success');
                showDebugMessage(`✅ ${data.satirlar.length} reçete satırı yüklendi`, 'success');
            } else {
                debugLog('❌ Reçete satırları yüklenemedi:', 'error', data);
                showDebugMessage('❌ Reçete satırları yüklenemedi', 'error');
            }
        })
        .catch(error => {
            debugLog('❌ Reçete satırları AJAX hatası:', 'error', error);
            showDebugMessage(`❌ Reçete satırları AJAX hatası: ${error.message}`, 'error');
        });
}

function populateReceteSatirlari(satirlar) {
    const kategoriler = {
        'sunta': '#sunta-table tbody',
        'pvc': '#pvc-table tbody',
        'hirdavat': '#hirdavat-table tbody',
        'koli': '#koli-table tbody'
    };

    // Önce mevcut satırları temizle (ilk satır hariç)
    Object.values(kategoriler).forEach(selector => {
        const tbody = document.querySelector(selector);
        if (tbody) {
            const rows = tbody.querySelectorAll('tr');
            for (let i = 1; i < rows.length; i++) {
                rows[i].remove();
            }
        }
    });

    // Satırları kategorilere göre grupla
    const satirlarByKategori = {};
    satirlar.forEach(satir => {
        if (!satirlarByKategori[satir.kategori]) {
            satirlarByKategori[satir.kategori] = [];
        }
        satirlarByKategori[satir.kategori].push(satir);
    });

    // Her kategori için satırları doldur
    Object.keys(satirlarByKategori).forEach(kategori => {
        const tbody = document.querySelector(kategoriler[kategori]);
        if (tbody) {
            satirlarByKategori[kategori].forEach((satir, index) => {
                if (index === 0) {
                    // İlk satırı doldur
                    fillExistingRow(tbody.querySelector('tr'), satir, kategori);
                } else {
                    // Yeni satır ekle
                    addNewRowWithData(tbody, satir, kategori);
                }
            });
        }
    });
}

function fillExistingRow(row, satirData, kategori) {
    if (!row) return;

    // Malzeme seçimini ayarla
    const malzemeSelect = row.querySelector(`select[name="${kategori}_malzeme[]"]`);
    if (malzemeSelect && satirData.malzeme_id) {
        malzemeSelect.setAttribute('data-selected', satirData.malzeme_id);
        malzemeSelect.value = satirData.malzeme_id;
    }

    // Diğer alanları doldur
    fillRowFromSatirData(row, satirData, kategori);
}

function fillRowFromSatirData(row, satirData, kategori) {
    const fieldMappings = {
        'sunta': {
            'parca_no[]': 'parca_no',
            'renk[]': 'renk',
            'parca_ismi[]': 'parca_ismi',
            'adet[]': 'adet',
            'sunta_kesim_boy[]': 'sunta_kesim_boy',
            'sunta_kesim_en[]': 'sunta_kesim_en'
        },
        'pvc': {
            'pvc_parca_no[]': 'parca_no',
            'pvc_renk[]': 'renk',
            'pvc_parca_ismi[]': 'parca_ismi',
            'pvc_adet[]': 'adet',
            'pvc_bantlama_boy[]': 'pvc_bantlama_boy',
            'pvc_bantlama_en[]': 'pvc_bantlama_en',
            'pvc_kesim_boy[]': 'pvc_kesim_boy',
            'pvc_kesim_en[]': 'pvc_kesim_en'
        },
        'hirdavat': {
            'hirdavat_adi[]': 'hirdavat_adi',
            'hirdavat_adet[]': 'adet'
        },
        'koli': {
            'koli_turu[]': 'koli_turu',
            'koli_olcu[]': 'olcu',
            'koli_cm[]': 'metre',
            'koli_adet_miktar[]': 'adet_miktar'
        }
    };

    const mappings = fieldMappings[kategori] || {};
    Object.keys(mappings).forEach(fieldName => {
        const dbField = mappings[fieldName];
        if (satirData[dbField] !== undefined && satirData[dbField] !== null) {
            fillRowInput(row, fieldName, satirData[dbField]);
        }
    });
}

// Global fonksiyonları window'a ekle
window.debugLog = debugLog;
window.showDebugMessage = showDebugMessage;
window.loadUrunler = loadUrunler;
window.loadAllMalzemeler = loadAllMalzemeler;
window.loadMevcutReceteVerileri = loadMevcutReceteVerileri;
window.setupCalculationListeners = setupCalculationListeners;
window.calculateSuntaRow = calculateSuntaRow;
window.calculatePvcRow = calculatePvcRow;
window.calculateHirdavatRow = calculateHirdavatRow;
window.calculateKoliRow = calculateKoliRow;

// Yeni satır ekleme fonksiyonu
function addNewRowWithData(tbody, satir, kategori) {
    // Önce yeni satır ekle
    if (kategori === 'sunta') {
        addSuntaRow();
    } else if (kategori === 'pvc') {
        addPvcRow();
    } else if (kategori === 'hirdavat') {
        addHirdavatRow();
    } else if (kategori === 'koli') {
        addKoliRow();
    }

    // Yeni eklenen satırı bul (son satır - "Satır Ekle" satırından önceki)
    const rows = tbody.querySelectorAll('tr');
    const newRow = rows[rows.length - 2]; // Son satır "Satır Ekle" butonu

    if (newRow) {
        // Satırı verilerle doldur
        fillExistingRow(newRow, satir, kategori);
        debugLog(`✅ Yeni ${kategori} satırı eklendi ve dolduruldu`, 'success');
    }
}

// Global'e ekle
window.addNewRowWithData = addNewRowWithData;
