<?php
/**
 * Veritabanı bağlantı yapılandırması
 */

// UTF-8 encoding ayarları
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');

// Session çağrısı kaldırıldı - recete_duzenle.php'de hallediliyor
// if (session_status() == PHP_SESSION_NONE && php_sapi_name() !== 'cli') {
//     session_start();
// }

// Sunucu tespiti (canlı/local) - CLI desteği ekli
$is_live_server = false;
if (isset($_SERVER['HTTP_HOST'])) {
    $is_live_server = (
        strpos($_SERVER['HTTP_HOST'], 'otris.com.tr') !== false ||
        strpos($_SERVER['HTTP_HOST'], 'otris') !== false ||
        !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', '::1'])
    );
} else {
    // CLI modunda localhost kabul et
    $is_live_server = false;
}

if ($is_live_server) {
    // CANLI SUNUCU AYARLARI - Bu bilgileri hosting panelindeki bilgilerle güncelleyin
    if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
    if (!defined('DB_USER')) define('DB_USER', 'otriscom_stok');     // ✅ Doğru kullanıcı
    if (!defined('DB_PASS')) define('DB_PASS', '190303Ymn#');             // ✅ Doğru şifre
    if (!defined('DB_NAME')) define('DB_NAME', 'otriscom_stok');        // ✅ Doğru veritabanı
} else {
    // LOCAL SUNUCU AYARLARI (XAMPP/WAMP)
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', '');
if (!defined('DB_NAME')) define('DB_NAME', 'barkod_stok');
}

// Veritabanı bağlantı fonksiyonu
function connectDatabase() {
    // MySQLi veritabanı bağlantısı
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

    // Bağlantı hatası kontrolü
    if ($conn->connect_error) {
        error_log("Veritabanı bağlantı hatası: " . $conn->connect_error);
        return false;
    }

    // Karakter seti
    $conn->set_charset("utf8");
    
    return $conn;
}

// Global bağlantı nesnesi
$conn = connectDatabase();

// Bağlantı hatası kontrolü
if (!$conn) {
    die("Veritabanı bağlantı hatası: Bağlantı kurulamadı");
}

// Veritabanı kontrolü ve oluşturma
$db_check = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '" . DB_NAME . "'");
if ($db_check->num_rows == 0) {
    // Veritabanı yoksa oluştur
    $conn->query("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8 COLLATE utf8_turkish_ci");
}

// Veritabanı seçimi
$conn->select_db(DB_NAME);

// Veritabanı dosyalarının içe aktarılması 
function import_db_tables() {
    global $conn;
    
    $sql_file = file_get_contents(__DIR__ . '/../database/create_tables.sql');
    if ($sql_file) {
        $queries = explode(';', $sql_file);
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $conn->query($query);
            }
        }
    }
}

// Tabloların varlığını kontrol et
$result = $conn->query("SHOW TABLES LIKE 'urunler'");
if ($result->num_rows == 0) {
    // Tablolar yoksa içe aktar
    import_db_tables();
} 