-- <PERSON><PERSON> başvuru tablosuna nakliye alanları ekleme (basit versiyon)

-- <PERSON><PERSON> başvuru tablosuna nakliye alanlar<PERSON> ekle (varsa atla)
ALTER TABLE b2b_bayi_basvurulari 
ADD COLUMN IF NOT EXISTS nakliye_firma VARCHAR(255) NULL COMMENT 'Çalıştığımız Nakliye Firması';

ALTER TABLE b2b_bayi_basvurulari 
ADD COLUMN IF NOT EXISTS nakliye_telefon VARCHAR(20) NULL COMMENT 'Nakliye Firması Telefonu';

-- <PERSON><PERSON> firmalar tablosuna da ekle (varsa atla)
ALTER TABLE cari_firmalar 
ADD COLUMN IF NOT EXISTS nakliye_firma VARCHAR(255) NULL COMMENT 'Çalıştığımız Nakliye Firması';

ALTER TABLE cari_firmalar 
ADD COLUMN IF NOT EXISTS nakliye_telefon VARCHAR(20) NULL COMMENT 'Nakliye Firması Telefonu';

-- Bayi başvuru tablosuna eksik sütunları ekle
ALTER TABLE b2b_bayi_basvurulari 
ADD COLUMN IF NOT EXISTS sifre VARCHAR(255) NULL COMMENT 'Bayi Şifresi (Hash)';

ALTER TABLE b2b_bayi_basvurulari 
ADD COLUMN IF NOT EXISTS adres TEXT NULL COMMENT 'Firma Adresi';

-- Cari firmalar tablosuna da eksik sütunları ekle
ALTER TABLE cari_firmalar 
ADD COLUMN IF NOT EXISTS sifre VARCHAR(255) NULL COMMENT 'Bayi Şifresi (Hash)';

ALTER TABLE cari_firmalar 
ADD COLUMN IF NOT EXISTS adres TEXT NULL COMMENT 'Firma Adresi';

-- Kontrol için hangi sütunların var olduğunu göster
SELECT 
    'b2b_bayi_basvurulari' as tablo_adi,
    COLUMN_NAME as sutun_adi,
    DATA_TYPE as veri_tipi,
    IS_NULLABLE as bos_olabilir,
    COLUMN_COMMENT as aciklama
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'b2b_bayi_basvurulari' 
AND COLUMN_NAME IN ('nakliye_firma', 'nakliye_telefon')
UNION ALL
SELECT 
    'cari_firmalar' as tablo_adi,
    COLUMN_NAME as sutun_adi,
    DATA_TYPE as veri_tipi,
    IS_NULLABLE as bos_olabilir,
    COLUMN_COMMENT as aciklama
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'cari_firmalar' 
AND COLUMN_NAME IN ('nakliye_firma', 'nakliye_telefon'); 