<?php
session_start();

// Admin önceki session bilgilerini geri yükle
if (isset($_SESSION['admin_onceki_session'])) {
    $admin_session = $_SESSION['admin_onceki_session'];
    
    // B2B müşteri session'ını temizle
    unset($_SESSION['b2b_cari_id']);
    unset($_SESSION['b2b_firma_adi']);
    unset($_SESSION['admin_adina_siparis']);
    unset($_SESSION['admin_siparis_mesaj']);
    
    // Admin session'ını geri yükle
    $_SESSION['b2b_admin_id'] = $admin_session['b2b_admin_id'];
    $_SESSION['b2b_admin_kullanici'] = $admin_session['b2b_admin_kullanici'];
    $_SESSION['b2b_admin_ad_soyad'] = $admin_session['b2b_admin_ad_soyad'];
    
    // Önceki session bilgilerini temizle
    unset($_SESSION['admin_onceki_session']);
    
    // Başarı mesajı
    $_SESSION['admin_mesaj'] = "Müşteri adına sipariş verme işlemi tamamlandı.";
    $_SESSION['admin_mesaj_tipi'] = "success";
}

// Admin paneline yönlendir
header('Location: musteriler.php');
exit;
?> 