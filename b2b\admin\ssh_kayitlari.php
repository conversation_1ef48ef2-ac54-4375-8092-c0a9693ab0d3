<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "SSH Kayıtları - B2B Admin";
$current_page = "ssh_kayitlari";

$mesaj = '';
$mesaj_tur = '';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];
$admin_kullanici = $_SESSION['b2b_admin_kullanici'];
$admin_ad_soyad = $_SESSION['b2b_admin_ad_soyad'] ?? $admin_kullanici;

// SSH durumu güncelleme
if (isset($_POST['durum_guncelle']) && isset($_POST['ssh_id'])) {
    $ssh_id = intval($_POST['ssh_id']);
    $yeni_durum = $_POST['yeni_durum'];
    $admin_notu = trim($_POST['admin_notu']);
    
    // Mevcut durumu al
    $eski_durum_sql = "SELECT durum FROM b2b_ssh_kayitlari WHERE id = ?";
    $eski_durum_stmt = $conn->prepare($eski_durum_sql);
    $eski_durum_stmt->bind_param('i', $ssh_id);
    $eski_durum_stmt->execute();
    $eski_durum_result = $eski_durum_stmt->get_result();
    $eski_durum_row = $eski_durum_result->fetch_assoc();
    $eski_durum = $eski_durum_row['durum'];
    
    // SSH kaydını güncelle
    $update_sql = "UPDATE b2b_ssh_kayitlari SET durum = ?, admin_notu = ?, islem_yapan_admin_id = ?, updated_at = NOW()";
    $params = [$yeni_durum, $admin_notu, $admin_id];
    $types = "ssi";
    
    // Eğer durum tamamlandı ise çıkış tarihini de güncelle
    if ($yeni_durum == 'tamamlandi') {
        $update_sql .= ", cikis_tarihi = NOW()";
    }
    
    $update_sql .= " WHERE id = ?";
    $params[] = $ssh_id;
    $types .= "i";
    
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param($types, ...$params);
    
    if ($update_stmt->execute()) {
        // Durum geçmişine kaydet
        $history_sql = "INSERT INTO b2b_ssh_durum_gecmisi (ssh_id, eski_durum, yeni_durum, degisiklik_notu, degistiren_admin_id) VALUES (?, ?, ?, ?, ?)";
        $history_stmt = $conn->prepare($history_sql);
        $history_stmt->bind_param('isssi', $ssh_id, $eski_durum, $yeni_durum, $admin_notu, $admin_id);
        $history_stmt->execute();
        
        $mesaj = "SSH kaydı durumu başarıyla güncellendi.";
        $mesaj_tur = "success";
    } else {
        $mesaj = "SSH kaydı güncellenirken hata oluştu.";
        $mesaj_tur = "danger";
    }
}

// SSH kayıtlarını listele
$ssh_kayitlari = [];
$durum_filter = $_GET['durum'] ?? 'tumu';
$nedeni_filter = $_GET['nedeni'] ?? 'tumu';

$where_conditions = [];
$params = [];
$types = "";

if ($durum_filter !== 'tumu') {
    $where_conditions[] = "s.durum = ?";
    $params[] = $durum_filter;
    $types .= "s";
}

if ($nedeni_filter !== 'tumu') {
    $where_conditions[] = "s.ssh_nedeni = ?";
    $params[] = $nedeni_filter;
    $types .= "s";
}

$where_clause = "";
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

$sql = "SELECT s.*, 
        cf.firm_name as musteri_adi,
        bs.id as siparis_no,
        u.stok_kodu, u.stok_adi,
        ba.kullanici_adi as islem_yapan_admin
        FROM b2b_ssh_kayitlari s
        LEFT JOIN cari_firmalar cf ON s.cari_id = cf.id
        LEFT JOIN b2b_siparisler bs ON s.siparis_id = bs.id
        LEFT JOIN urunler u ON s.urun_id = u.id
        LEFT JOIN b2b_admin ba ON s.islem_yapan_admin_id = ba.id
        $where_clause
        ORDER BY s.giris_tarihi DESC";

if (!empty($params)) {
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $conn->query($sql);
}

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $ssh_kayitlari[] = $row;
    }
}

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-tools"></i>
            SSH Kayıtları (Satış Sonrası Hizmet)
        </h1>
        <a href="ssh_ekle.php" class="btn">
            <i class="fas fa-plus"></i>
            Yeni SSH Kaydı
        </a>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<!-- Filtreler -->
<div class="content-card" style="margin-bottom: 1rem;">
    <h3 style="margin-bottom: 1rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-filter"></i>
        Filtreler
    </h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
        <div>
            <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">Durum</label>
            <select style="width: 100%; padding: 0.75rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" onchange="updateFilter('durum', this.value)">
                <option value="tumu" <?php echo ($durum_filter == 'tumu') ? 'selected' : ''; ?>>Tüm Durumlar</option>
                <option value="beklemede" <?php echo ($durum_filter == 'beklemede') ? 'selected' : ''; ?>>Beklemede</option>
                <option value="inceleniyor" <?php echo ($durum_filter == 'inceleniyor') ? 'selected' : ''; ?>>İnceleniyor</option>
                <option value="gonderildi" <?php echo ($durum_filter == 'gonderildi') ? 'selected' : ''; ?>>Gönderildi</option>
                <option value="onaylandi" <?php echo ($durum_filter == 'onaylandi') ? 'selected' : ''; ?>>Onaylandı</option>
                <option value="reddedildi" <?php echo ($durum_filter == 'reddedildi') ? 'selected' : ''; ?>>Reddedildi</option>
                <option value="tamamlandi" <?php echo ($durum_filter == 'tamamlandi') ? 'selected' : ''; ?>>Tamamlandı</option>
                <option value="iptal" <?php echo ($durum_filter == 'iptal') ? 'selected' : ''; ?>>İptal</option>
            </select>
        </div>
        
        <div>
            <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">SSH Nedeni</label>
            <select style="width: 100%; padding: 0.75rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" onchange="updateFilter('nedeni', this.value)">
                <option value="tumu" <?php echo ($nedeni_filter == 'tumu') ? 'selected' : ''; ?>>Tüm Nedenler</option>
                <option value="hasar" <?php echo ($nedeni_filter == 'hasar') ? 'selected' : ''; ?>>Hasar</option>
                <option value="iade" <?php echo ($nedeni_filter == 'iade') ? 'selected' : ''; ?>>İade</option>
                <option value="degisim" <?php echo ($nedeni_filter == 'degisim') ? 'selected' : ''; ?>>Değişim</option>
                <option value="garanti" <?php echo ($nedeni_filter == 'garanti') ? 'selected' : ''; ?>>Garanti</option>
                <option value="kalite_sorunu" <?php echo ($nedeni_filter == 'kalite_sorunu') ? 'selected' : ''; ?>>Kalite Sorunu</option>
                <option value="yanlis_urun" <?php echo ($nedeni_filter == 'yanlis_urun') ? 'selected' : ''; ?>>Yanlış Ürün</option>
                <option value="diger" <?php echo ($nedeni_filter == 'diger') ? 'selected' : ''; ?>>Diğer</option>
            </select>
        </div>
    </div>
</div>

<div class="content-card">
    <?php if (empty($ssh_kayitlari)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--gray);">
            <i class="fas fa-tools" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem;">Henüz SSH kaydı bulunmuyor</h3>
            <p>Yeni SSH kaydı eklemek için yukarıdaki "Yeni SSH Kaydı" butonunu kullanabilirsiniz.</p>
        </div>
    <?php else: ?>
        <style>
            .ssh-table {
                width: 100%;
                border-collapse: collapse;
                border-radius: 0.75rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            }
            .ssh-table th {
                background: linear-gradient(135deg, var(--primary), var(--primary-dark));
                color: white;
                font-weight: 600;
                padding: 1rem;
                text-align: left;
                font-size: 0.875rem;
            }
            .ssh-table td {
                padding: 1rem;
                border-bottom: 1px solid var(--gray-light);
                vertical-align: middle;
            }
            .ssh-table tr:hover {
                background-color: rgba(249, 115, 22, 0.05);
            }
            .ssh-table tr:last-child td {
                border-bottom: none;
            }
            .status-badge {
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                font-size: 0.75rem;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.375rem;
            }
            .status-beklemede {
                background-color: rgba(245, 158, 11, 0.1);
                color: var(--warning);
                border: 1px solid rgba(245, 158, 11, 0.2);
            }
            .status-inceleniyor {
                background-color: rgba(59, 130, 246, 0.1);
                color: #1e40af;
                border: 1px solid rgba(59, 130, 246, 0.2);
            }
            .status-onaylandi {
                background-color: rgba(16, 185, 129, 0.1);
                color: var(--success);
                border: 1px solid rgba(16, 185, 129, 0.2);
            }
            .status-reddedildi, .status-iptal {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger);
                border: 1px solid rgba(239, 68, 68, 0.2);
            }
            .status-tamamlandi {
                background-color: rgba(34, 197, 94, 0.1);
                color: #15803d;
                border: 1px solid rgba(34, 197, 94, 0.2);
            }
            .reason-badge {
                padding: 0.25rem 0.5rem;
                border-radius: 0.375rem;
                font-size: 0.7rem;
                font-weight: 600;
                background-color: rgba(107, 114, 128, 0.1);
                color: var(--gray);
                border: 1px solid rgba(107, 114, 128, 0.2);
            }
            .action-links {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                align-items: center;
            }
            .action-btn {
                padding: 0.5rem 0.875rem;
                border-radius: 0.5rem;
                text-decoration: none;
                font-size: 0.8rem;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.375rem;
                transition: all 0.3s ease;
                border: 1px solid transparent;
            }
            .action-btn:hover {
                transform: translateY(-1px);
            }
            .action-view {
                background-color: rgba(59, 130, 246, 0.1);
                color: #1e40af;
                border-color: rgba(59, 130, 246, 0.2);
            }
            .action-view:hover {
                background-color: #3b82f6;
                color: white;
            }
            .action-edit {
                background-color: rgba(249, 115, 22, 0.1);
                color: var(--secondary);
                border-color: rgba(249, 115, 22, 0.2);
            }
            .action-edit:hover {
                background-color: var(--secondary);
                color: white;
            }
        </style>
        
        <div style="overflow-x: auto;">
            <table class="ssh-table">
                <thead>
                    <tr>
                        <th>SSH No</th>
                        <th>Müşteri</th>
                        <th>Ürün</th>
                        <th>Parça</th>
                        <th>SSH Nedeni</th>
                        <th>Kaynak</th>
                        <th>Durum</th>
                        <th>Giriş Tarihi</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($ssh_kayitlari as $ssh): ?>
                    <tr>
                        <td style="font-weight: 600;">#SSH<?php echo $ssh['id']; ?></td>
                        <td>
                            <div style="font-weight: 600;"><?php echo htmlspecialchars($ssh['musteri_adi']); ?></div>
                            <?php if ($ssh['siparis_no']): ?>
                                <small style="color: var(--gray);">Sipariş: #<?php echo $ssh['siparis_no']; ?></small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div style="font-weight: 600;"><?php echo htmlspecialchars($ssh['urun_adi']); ?></div>
                            <?php if ($ssh['stok_kodu']): ?>
                                <small style="color: var(--gray);"><?php echo htmlspecialchars($ssh['stok_kodu']); ?></small>
                            <?php endif; ?>
                        </td>
                        <td><?php echo htmlspecialchars($ssh['parca_adi'] ?: '-'); ?></td>
                        <td>
                            <span class="reason-badge">
                                <?php 
                                $nedeni_labels = [
                                    'hasar' => 'Hasar',
                                    'iade' => 'İade',
                                    'degisim' => 'Değişim',
                                    'garanti' => 'Garanti',
                                    'kalite_sorunu' => 'Kalite Sorunu',
                                    'yanlis_urun' => 'Yanlış Ürün',
                                    'diger' => 'Diğer'
                                ];
                                echo $nedeni_labels[$ssh['ssh_nedeni']] ?? $ssh['ssh_nedeni'];
                                ?>
                            </span>
                            <br><small style="color: var(--gray);"><?php echo ucfirst($ssh['ssh_kaynagi']); ?></small>
                        </td>
                        <td><?php echo ucfirst($ssh['ssh_kaynagi']); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo $ssh['durum']; ?>">
                                <?php 
                                $durum_icons = [
                                    'beklemede' => 'clock',
                                    'inceleniyor' => 'search',
                                    'onaylandi' => 'check',
                                    'reddedildi' => 'times',
                                    'tamamlandi' => 'check-double',
                                    'iptal' => 'ban'
                                ];
                                $durum_labels = [
                                    'beklemede' => 'Beklemede',
                                    'inceleniyor' => 'İnceleniyor',
                                    'onaylandi' => 'Onaylandı',
                                    'reddedildi' => 'Reddedildi',
                                    'tamamlandi' => 'Tamamlandı',
                                    'iptal' => 'İptal'
                                ];
                                ?>
                                <i class="fas fa-<?php echo $durum_icons[$ssh['durum']] ?? 'question'; ?>"></i>
                                <?php echo $durum_labels[$ssh['durum']] ?? $ssh['durum']; ?>
                            </span>
                        </td>
                        <td>
                            <div style="font-weight: 600;"><?php echo date('d.m.Y', strtotime($ssh['giris_tarihi'])); ?></div>
                            <small style="color: var(--gray);"><?php echo date('H:i', strtotime($ssh['giris_tarihi'])); ?></small>
                        </td>
                        <td>
                            <div class="action-links">
                                <a href="ssh_detay.php?id=<?php echo $ssh['id']; ?>" class="action-btn action-view">
                                    <i class="fas fa-eye"></i>
                                    Detay
                                </a>
                                <a href="ssh_duzenle.php?id=<?php echo $ssh['id']; ?>" class="action-btn action-edit">
                                    <i class="fas fa-edit"></i>
                                    Düzenle
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<script>
function updateFilter(filterType, value) {
    const url = new URL(window.location);
    url.searchParams.set(filterType, value);
    window.location.href = url.toString();
}
</script>

<?php include 'includes/footer.php'; ?> 