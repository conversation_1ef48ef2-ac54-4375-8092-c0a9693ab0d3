<?php
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Geçersiz istek']);
    exit;
}

// Form verilerini al
$plan_id = isset($_POST['plan_id']) ? (int)$_POST['plan_id'] : 0;
$miktar = isset($_POST['miktar']) ? (int)$_POST['miktar'] : 0;
$baslangic_tarihi = $_POST['baslangic_tarihi'] ?? '';
$teslim_tarihi = $_POST['teslim_tarihi'] ?? '';
$durum = $_POST['durum'] ?? 'bekliyor';
$oncelik = $_POST['oncelik'] ?? 'orta';
$notlar = $_POST['notlar'] ?? '';

// Validasyon
if (empty($plan_id)) {
    echo json_encode(['success' => false, 'message' => 'Plan ID gerekli']);
    exit;
}

if (empty($miktar) || $miktar <= 0) {
    echo json_encode(['success' => false, 'message' => 'Geçerli bir miktar giriniz']);
    exit;
}

if (empty($baslangic_tarihi)) {
    echo json_encode(['success' => false, 'message' => 'Başlangıç tarihi gerekli']);
    exit;
}

if (empty($teslim_tarihi)) {
    echo json_encode(['success' => false, 'message' => 'Teslim tarihi gerekli']);
    exit;
}

// Tarih kontrolü
if (strtotime($teslim_tarihi) < strtotime($baslangic_tarihi)) {
    echo json_encode(['success' => false, 'message' => 'Teslim tarihi başlangıç tarihinden önce olamaz']);
    exit;
}

try {
    // Plan var mı kontrol et
    $check_stmt = $conn->prepare("SELECT id FROM uretim_planlari WHERE id = ?");
    $check_stmt->bind_param("i", $plan_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Plan bulunamadı']);
        exit;
    }
    
    // Öncelik değerlerini dönüştür (dusuk,orta,yuksek -> 3,2,1)
    switch($oncelik) {
        case 'yuksek': $oncelik_db = '1'; break;
        case 'orta': $oncelik_db = '2'; break;
        case 'dusuk': $oncelik_db = '3'; break;
        default: $oncelik_db = '2';
    }
    
    // Durum değerlerini dönüştür
    switch($durum) {
        case 'bekliyor': $durum_db = 'planlandi'; break;
        case 'devam_ediyor': $durum_db = 'devam_ediyor'; break;
        case 'tamamlandi': $durum_db = 'tamamlandi'; break;
        case 'iptal': $durum_db = 'iptal'; break;
        default: $durum_db = 'planlandi';
    }
    
    // Planı güncelle (bitis_tarihi kullan, teslim_tarihi değil)
    $stmt = $conn->prepare("UPDATE uretim_planlari SET miktar = ?, baslangic_tarihi = ?, bitis_tarihi = ?, durum = ?, oncelik = ?, notlar = ? WHERE id = ?");
    $stmt->bind_param("isssssi", $miktar, $baslangic_tarihi, $teslim_tarihi, $durum_db, $oncelik_db, $notlar, $plan_id);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Plan başarıyla güncellendi'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Plan güncellenirken hata oluştu']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()]);
}
?> 