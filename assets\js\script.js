/**
 * Stok & Barkod Takip Sistemi - Ana JavaScript Dosyası
 */

// Sayfa yüklendiğinde çalışacak kodlar
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap tooltips'i etkinleştir
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Uyarı mesajlarını otomatik kapat
    var alertList = document.querySelectorAll('.alert-dismissible');
    alertList.forEach(function(alert) {
        setTimeout(function() {
            var closeButton = alert.querySelector('.btn-close');
            if (closeButton) {
                closeButton.click();
            }
        }, 5000); // 5 saniye sonra kapat
    });
    
    // <PERSON><PERSON><PERSON> alan<PERSON>nı bugünün tarihiyle doldur (boşsa)
    var dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(function(input) {
        if (!input.value) {
            var today = new Date().toISOString().split('T')[0];
            if (input.id === 'start_date') {
                // Başlangıç tarihi için 30 gün öncesini ayarla
                var thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                input.value = thirtyDaysAgo.toISOString().split('T')[0];
            } else if (input.id === 'end_date') {
                // Bitiş tarihi için bugünü ayarla
                input.value = today;
            }
        }
    });
    
    // Tablo satırlarına tıklanabilirlik ekle
    var clickableRows = document.querySelectorAll('.table-clickable tr[data-href]');
    clickableRows.forEach(function(row) {
        row.style.cursor = 'pointer';
        row.addEventListener('click', function() {
            window.location.href = this.dataset.href;
        });
    });

    // Ürün sayfasındaki arama alanını kontrol et
    var searchInput = document.getElementById('search');
    var searchForm = document.querySelector('form');
    
    if (searchInput && window.location.pathname.includes('urunler.php')) {
        console.log('Form araması etkinleştirildi.');
        
        // Form submit olduğunda normal arama yapılacak
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                var searchTerm = searchInput.value.trim();
                if (searchTerm.length === 0) {
                    e.preventDefault();
                    window.location.href = 'urunler.php';
                }
            });
        }
    }
});

/**
 * Barkod tarayıcı fonksiyonları
 */

// Barkod tarayıcıyı başlat
function initBarcodeScanner(containerId, inputId, autoSubmit = false, formId = null) {
    if (!Quagga) {
        console.error('Quagga kütüphanesi yüklenemedi!');
        return;
    }
    
    Quagga.init({
        inputStream: {
            name: "Live",
            type: "LiveStream",
            target: document.getElementById(containerId),
            constraints: {
                width: 640,
                height: 480,
                facingMode: "environment"
            }
        },
        locator: {
            patchSize: "medium",
            halfSample: true
        },
        numOfWorkers: navigator.hardwareConcurrency || 4,
        decoder: {
            readers: ["code_128_reader", "ean_reader", "ean_8_reader", "code_39_reader", "code_39_vin_reader", "codabar_reader", "upc_reader", "upc_e_reader"]
        },
        locate: true
    }, function(err) {
        if (err) {
            console.error('Barkod tarayıcı başlatılamadı:', err);
            return;
        }
        
        console.log('Barkod tarayıcı başlatıldı');
        Quagga.start();
    });
    
    Quagga.onDetected(function(result) {
        var code = result.codeResult.code;
        console.log('Barkod algılandı:', code);
        
        // Barkodu input alanına yerleştir
        document.getElementById(inputId).value = code;
        
        // Tarayıcıyı durdur
        Quagga.stop();
        
        // Otomatik form gönderimi
        if (autoSubmit && formId) {
            document.getElementById(formId).submit();
        }
    });
}

// Barkod tarayıcıyı durdur
function stopBarcodeScanner() {
    if (Quagga) {
        Quagga.stop();
    }
}

/**
 * Yardımcı fonksiyonlar
 */

// Barkod oluştur
function generateBarcode(selector, value, options = {}) {
    if (!JsBarcode) {
        console.error('JsBarcode kütüphanesi yüklenemedi!');
        return;
    }
    
    var defaultOptions = {
        format: "CODE128",
        lineColor: "#000",
        width: 2,
        height: 100,
        displayValue: true,
        fontSize: 20,
        margin: 10
    };
    
    // Varsayılan ayarları kullanıcı ayarlarıyla birleştir
    var mergedOptions = Object.assign({}, defaultOptions, options);
    
    // Barkodu oluştur
    JsBarcode(selector, value, mergedOptions);
}

// Sayfa yükleniyor göstergesi
function showLoading(selector) {
    var element = document.querySelector(selector);
    if (element) {
        element.innerHTML = '<div class="text-center p-5"><div class="loading"></div><p class="mt-3">Yükleniyor...</p></div>';
    }
}

// Sayfa yükleniyor göstergesini kaldır
function hideLoading(selector) {
    var element = document.querySelector(selector);
    if (element) {
        element.innerHTML = '';
    }
}

// Ondalık sayı formatı
function formatNumber(number, decimals = 2) {
    return parseFloat(number).toFixed(decimals).replace('.', ',').replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

// Tarih formatı
function formatDate(dateString) {
    var date = new Date(dateString);
    return date.toLocaleDateString('tr-TR');
}

// Tarih ve saat formatı
function formatDateTime(dateString) {
    var date = new Date(dateString);
    return date.toLocaleDateString('tr-TR') + ' ' + date.toLocaleTimeString('tr-TR');
}

/**
 * Gerçek Zamanlı Ürün Araması
 */

// Gerçek zamanlı arama fonksiyonu
function performLiveSearch(searchTerm) {
    // Arama tablosunu bul
    var tableBody = document.querySelector('.table tbody');
    if (!tableBody) return;
    
    // Durum filtresini al
    var statusFilter = document.getElementById('status').value;
    
    // Yükleniyor göstergesi
    tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3">Aranıyor...</p></td></tr>';
    
    // AJAX isteği
    fetch('ajax_urun_arama.php?search=' + encodeURIComponent(searchTerm) + '&status=' + encodeURIComponent(statusFilter))
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            // Tabloyu temizle
            tableBody.innerHTML = '';
            
            if (data.length === 0) {
                // Sonuç yoksa bilgi mesajı göster
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-5">Arama kriterlerine uygun sonuç bulunamadı.</td></tr>';
            } else {
                // Sonuçları tabloya ekle
                data.forEach(function(item, index) {
                    var stockStatus = '';
                    
                    if (item.stokta_paket == 0) {
                        stockStatus = '<span class="badge bg-danger">Stokta Yok (0)</span>';
                    } else if (item.stokta_paket < item.paket_sayisi) {
                        stockStatus = '<div><span class="badge bg-warning">' + item.stokta_paket + ' / ' + item.paket_sayisi + '</span><br><small class="text-muted">' + item.stokta_paket + ' adet stokta</small></div>';
                    } else {
                        stockStatus = '<div><span class="badge bg-success">Tam</span><br><small class="text-muted">' + item.stokta_paket + ' adet stokta</small></div>';
                    }
                    
                    var row = `
                        <tr>
                            <td>${item.id}</td>
                            <td>${item.stok_adi}</td>
                            <td>${item.stok_kodu}</td>
                            <td>${item.renk || '-'}</td>
                            <td>${item.paket_sayisi}</td>
                            <td>${stockStatus}</td>
                            <td>${item.ekleme_tarihi}</td>
                            <td>
                                <a href="urun_detay.php?id=${item.id}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-info-circle"></i>
                                </a>
                            </td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;
                });
            }
        })
        .catch(function(error) {
            console.error('Arama hatası:', error);
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-5 text-danger">Arama sırasında bir hata oluştu.</td></tr>';
        });
} 