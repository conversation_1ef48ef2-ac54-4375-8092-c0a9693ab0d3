<?php
session_start();

// Hata ayıklama için
error_reporting(E_ALL);
ini_set('display_errors', 1);

// DB bağlantısı - farklı yolları dene
if (file_exists('../../includes/db.php')) {
    include '../../includes/db.php';
} elseif (file_exists('../config.php')) {
    include '../config.php';
} elseif (file_exists('../../config.php')) {
    include '../../config.php';
} else {
    die('Database connection file not found');
}

header('Content-Type: text/html; charset=utf-8');

// Kullanıcı oturum kontrolü
if (!isset($_SESSION['b2b_cari_id'])) {
    echo '<div class="alert alert-danger">Oturumunuz sona ermiş. Lütfen tekrar giriş yapın.</div>';
    exit;
}

$user_id = intval($_SESSION['b2b_cari_id']);

// POST ile gelen veriler
$siparis_id = isset($_POST['siparis_id']) ? intval($_POST['siparis_id']) : 0;
$urun_id = isset($_POST['urun_id']) ? intval($_POST['urun_id']) : 0;
$aciklama = trim($_POST['aciklama'] ?? '');

if (!$siparis_id || !$urun_id || !$aciklama) {
    echo '<div class="alert alert-warning">Lütfen tüm zorunlu alanları doldurun.</div>';
    exit;
}

// DB bağlantısı kontrolü
if (!isset($conn) || !$conn) {
    echo '<div class="alert alert-danger">Veritabanı bağlantısı kurulamadı.</div>';
    exit;
}

// Siparişin bu kullanıcıya ait olduğunu kontrol et
$siparis_kontrol = $conn->prepare("SELECT id FROM b2b_siparisler WHERE id = ? AND cari_id = ? LIMIT 1");
if (!$siparis_kontrol) {
    echo '<div class="alert alert-danger">Sorgu hazırlanamadı: ' . $conn->error . '</div>';
    exit;
}
$siparis_kontrol->bind_param('ii', $siparis_id, $user_id);
$siparis_kontrol->execute();
$siparis_kontrol->store_result();
if ($siparis_kontrol->num_rows === 0) {
    echo '<div class="alert alert-danger">Bu sipariş size ait değil!</div>';
    exit;
}
$siparis_kontrol->close();

// Ürün adı çekiliyor (güvenlik için DB'den)
$urun_adi = '';
$stmt = $conn->prepare('SELECT stok_adi FROM urunler WHERE id = ?');
if (!$stmt) {
    echo '<div class="alert alert-danger">Ürün sorgusu hazırlanamadı: ' . $conn->error . '</div>';
    exit;
}
$stmt->bind_param('i', $urun_id);
$stmt->execute();
$stmt->bind_result($urun_adi);
$stmt->fetch();
$stmt->close();
if (!$urun_adi) {
    echo '<div class="alert alert-danger">Ürün bulunamadı.</div>';
    exit;
}

try {
    $conn->begin_transaction();
    
    // talep_turu sütunu var mı kontrol et
    $check_column = $conn->query("SHOW COLUMNS FROM b2b_ssh_kayitlari LIKE 'talep_turu'");
    $has_talep_turu = ($check_column && $check_column->num_rows > 0);
    
    if ($has_talep_turu) {
        // SSH kaydı ekle - talep_turu ile
        $sql = "INSERT INTO b2b_ssh_kayitlari (cari_id, siparis_id, urun_id, urun_adi, ssh_nedeni, ssh_kaynagi, durum, talep_turu, aciklama) VALUES (?, ?, ?, ?, 'diger', 'musteri', 'beklemede', 'musteri', ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiiis', $user_id, $siparis_id, $urun_id, $urun_adi, $aciklama);
    } else {
        // SSH kaydı ekle - talep_turu olmadan
        $sql = "INSERT INTO b2b_ssh_kayitlari (cari_id, siparis_id, urun_id, urun_adi, ssh_nedeni, ssh_kaynagi, durum, aciklama) VALUES (?, ?, ?, ?, 'diger', 'musteri', 'beklemede', ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iiiis', $user_id, $siparis_id, $urun_id, $urun_adi, $aciklama);
    }
    
    if (!$stmt->execute()) {
        throw new Exception('SSH kaydı eklenemedi: ' . $conn->error);
    }
    $ssh_id = $conn->insert_id;

    // Resim yükleme (isteğe bağlı)
    if (isset($_FILES['resim']) && $_FILES['resim']['error'] == 0) {
        $file = $_FILES['resim'];
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($file_extension, $allowed_extensions)) {
            throw new Exception('Desteklenmeyen resim formatı.');
        }
        if ($file['size'] > 5 * 1024 * 1024) {
            throw new Exception('Resim çok büyük. Maksimum 5MB olmalı.');
        }
        
        $upload_dir = '../../uploads/ssh/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $new_filename = 'ssh_' . $ssh_id . '_' . time() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;
        
        if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
            throw new Exception('Resim yüklenemedi.');
        }
        
        // Dosya kaydı
        $file_sql = "INSERT INTO b2b_ssh_dosyalar (ssh_id, dosya_adi, dosya_yolu, dosya_tipi, dosya_boyutu) VALUES (?, ?, ?, 'resim', ?)";
        $file_stmt = $conn->prepare($file_sql);
        $file_stmt->bind_param('issi', $ssh_id, $file['name'], $upload_path, $file['size']);
        $file_stmt->execute();
    }

    // Durum geçmişi - sadece yeni_durum ve degisiklik_notu
    $history_sql = "INSERT INTO b2b_ssh_durum_gecmisi (ssh_id, yeni_durum, degisiklik_notu) VALUES (?, 'beklemede', 'Müşteri SSH talebi oluşturdu')";
    $history_stmt = $conn->prepare($history_sql);
    $history_stmt->bind_param('i', $ssh_id);
    $history_stmt->execute();

    $conn->commit();
    echo '<div class="alert alert-success">SSH talebiniz başarıyla oluşturuldu!</div>';
} catch (Exception $e) {
    $conn->rollback();
    echo '<div class="alert alert-danger">Hata: ' . htmlspecialchars($e->getMessage()) . '</div>';
} 