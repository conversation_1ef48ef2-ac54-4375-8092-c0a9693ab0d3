<?php
header('Content-Type: application/json; charset=utf-8');

// Debug log fonksiyonu
function debugLog($message, $data = null) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message";
    if ($data !== null) {
        $logMessage .= " | Data: " . print_r($data, true);
    }
    file_put_contents('debug_malzeme_detay.log', $logMessage . "\n", FILE_APPEND);
}

$kategori = isset($_GET['kategori']) ? strtolower($_GET['kategori']) : '';
$id       = isset($_GET['id']) ? intval($_GET['id']) : 0;

debugLog("🔍 API Çağrısı", ['kategori' => $kategori, 'id' => $id]);

$tablo_map = [
    'sunta'    => 'uretim_malzemeler_sunta',
    'pvc'      => 'uretim_malzemeler_pvc',
    'hirdavat' => 'uretim_malzemeler_hirdavat',
    'koli'     => 'uretim_malzemeler_koli',
    'urunler'  => 'urunler',
];

// Ürünler kategorisi için özel işlem
if ($kategori === 'urunler') {
    debugLog("🏷️ Ürünler kategorisi işleniyor");

    try {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $sql = "SELECT id, urun_adi, stok_kodu FROM urunler ORDER BY urun_adi";
        $stmt = $db->query($sql);
        $urunler = $stmt->fetchAll(PDO::FETCH_ASSOC);

        debugLog("✅ Ürünler bulundu", ['count' => count($urunler)]);
        echo json_encode(['success' => true, 'malzemeler' => $urunler]);
        exit;

    } catch (PDOException $e) {
        debugLog("❌ Ürünler PDO HATASI", $e->getMessage());
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

if (!isset($tablo_map[$kategori])) {
    debugLog("❌ Geçersiz kategori", $kategori);
    echo json_encode(['success' => false, 'message' => 'Geçersiz kategori']);
    exit;
}

require_once __DIR__ . '/config/database.php';
try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $tablo = $tablo_map[$kategori];
    debugLog("📊 Tablo", $tablo);

    /* --------------------------------------------------
       TEK BİR MALZEME DETAYI (id parametresi varsa)
    --------------------------------------------------*/
    if ($id > 0) {
        debugLog("🔍 Tek malzeme detayı isteniyor", $id);
        
        // Kategoriye özel detay sorgusu
        switch ($kategori) {
            case 'koli':
                $sql_query = "
                    SELECT
                        id,
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi,
                        COALESCE(koli_turu, '') as koli_turu,
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(birim_fiyat_m2, 0) as birim_fiyat_m2,
                        COALESCE(birim_fiyat_adet, 0) as birim_fiyat_adet,
                        COALESCE(olcu, 0) as olcu,
                        COALESCE(metre, 0) as metre,
                        COALESCE(miktar_m2, 0) as miktar_m2,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm
                    FROM {$tablo} WHERE id = ? LIMIT 1
                ";
                debugLog("📦 Koli SQL sorgusu", $sql_query);
                $detay = $db->prepare($sql_query);
                break;
            case 'sunta':
                $detay = $db->prepare("
                    SELECT 
                        id, malz_kodu, malzeme_adi, birim,
                        COALESCE(fiyat_cm2, 0) as fiyat,
                        COALESCE(stok_miktar, 0) as stok_miktar
                    FROM {$tablo} WHERE id = ? LIMIT 1
                ");
                break;
            case 'pvc':
            case 'hirdavat':
                $detay = $db->prepare("
                    SELECT 
                        id, malz_kodu, malzeme_adi, birim,
                        COALESCE(fiyat, 0) as fiyat,
                        COALESCE(stok_miktar, 0) as stok_miktar
                    FROM {$tablo} WHERE id = ? LIMIT 1
                ");
                break;
            default:
                $detay = $db->prepare("SELECT * FROM {$tablo} WHERE id = ? LIMIT 1");
                break;
        }
        
        debugLog("🚀 SQL Execute ediliyor", $id);
        $detay->execute([$id]);
        debugLog("✅ SQL Execute başarılı");
        
        $malzeme = $detay->fetch(PDO::FETCH_ASSOC);
        debugLog("📋 Fetch sonucu", $malzeme);
        
        if ($malzeme) {
            debugLog("✅ Malzeme bulundu, JSON döndürülüyor");
            echo json_encode(['success' => true, 'malzeme' => $malzeme]);
        } else {
            debugLog("❌ Malzeme bulunamadı");
            echo json_encode(['success' => false, 'message' => 'Malzeme bulunamadı']);
        }
        exit;
    }

    /* --------------------------------------------------
       GÜVENLI SORGU - MEVCUT SÜTUNLARI KONTROL ET
    --------------------------------------------------*/
    // Önce tablo yapısını öğren
    $columns_result = $db->query("DESCRIBE {$tablo}");
    $available_columns = [];
    while ($row = $columns_result->fetch(PDO::FETCH_ASSOC)) {
        $available_columns[] = $row['Field'];
    }

    // Kategoriye göre güvenli sorgu oluştur
    switch ($kategori) {
        case 'sunta':
            $select_fields = [
                'id',
                in_array('malz_kodu', $available_columns) ? 'malz_kodu' : 'CONCAT("S",id) as malz_kodu',
                in_array('malzeme_adi', $available_columns) ? 'malzeme_adi' : '"Sunta Malzeme" as malzeme_adi',
                in_array('birim', $available_columns) ? 'birim' : '"cm²" as birim',
                in_array('stok_miktar', $available_columns) ? 'stok_miktar' : '0 as stok_miktar'
            ];
            
            // Fiyat için mevcut sütunları kontrol et
            $fiyat_columns = ['fiyat_cm2', 'fiyat', 'toplam_fiyat'];
            $fiyat_found = false;
            foreach ($fiyat_columns as $fcol) {
                if (in_array($fcol, $available_columns)) {
                    $select_fields[] = "$fcol as fiyat_cm2";
                    $fiyat_found = true;
                    break;
                }
            }
            if (!$fiyat_found) {
                $select_fields[] = '0 as fiyat_cm2';
            }
            
            $sql = "SELECT " . implode(', ', $select_fields) . " FROM {$tablo} ORDER BY id";
            break;

        case 'pvc':
            $sql = "SELECT id,
                           COALESCE(malz_kodu, CONCAT('P',id)) AS malz_kodu,
                           COALESCE(malzeme_adi,'PVC Malzeme') AS malzeme_adi,
                           COALESCE(birim,'metre')             AS birim,
                           COALESCE(stok_miktar,0)             AS stok_miktar,
                           COALESCE(fiyat,0)                   AS fiyat
                    FROM {$tablo} ORDER BY id";
            break;

        case 'hirdavat':
            $select_fields = [
                'id',
                in_array('malz_kodu', $available_columns) ? 'malz_kodu' : 'CONCAT("H",id) as malz_kodu',
                in_array('malzeme_adi', $available_columns) ? 'malzeme_adi' : '"Hırdavat" as malzeme_adi',
                in_array('birim', $available_columns) ? 'birim' : '"adet" as birim',
                in_array('stok_miktar', $available_columns) ? 'stok_miktar' : '0 as stok_miktar'
            ];
            
            // Fiyat için mevcut sütunları kontrol et
            $fiyat_columns = ['fiyat', 'birim_fiyat', 'birim_fiyat_adet', 'toplam_fiyat'];
            $fiyat_found = false;
            foreach ($fiyat_columns as $fcol) {
                if (in_array($fcol, $available_columns)) {
                    $select_fields[] = "$fcol as fiyat";
                    $fiyat_found = true;
                    break;
                }
            }
            if (!$fiyat_found) {
                $select_fields[] = '0 as fiyat';
            }
            
            $sql = "SELECT " . implode(', ', $select_fields) . " FROM {$tablo} ORDER BY id";
            break;

        case 'koli':
            // Koli kategorisi için doğru fiyat sütunlarını çek
            $select_fields = [
                'id',
                in_array('malz_kodu', $available_columns) ? 'malz_kodu' : 'CONCAT("K",id) as malz_kodu',
                in_array('malzeme_adi', $available_columns) ? 'malzeme_adi' : '"Koli Malzeme" as malzeme_adi',
                in_array('koli_turu', $available_columns) ? 'koli_turu' : '"" as koli_turu',
                in_array('birim', $available_columns) ? 'birim' : '"adet" as birim',
                in_array('stok_miktar', $available_columns) ? 'stok_miktar' : '0 as stok_miktar',
                in_array('stok_adet', $available_columns) ? 'stok_adet' : '0 as stok_adet'
            ];

            // DOĞRU FIYAT SÜTUNLARI: birim_fiyat_m2, birim_fiyat_adet
            if (in_array('birim_fiyat_m2', $available_columns)) {
                $select_fields[] = 'birim_fiyat_m2';
            } else {
                $select_fields[] = '0 as birim_fiyat_m2';
            }

            if (in_array('birim_fiyat_adet', $available_columns)) {
                $select_fields[] = 'birim_fiyat_adet';
            } else {
                $select_fields[] = '0 as birim_fiyat_adet';
            }

            // Diğer gerekli sütunlar
            if (in_array('olcu', $available_columns)) {
                $select_fields[] = 'olcu';
            } else {
                $select_fields[] = '0 as olcu';
            }

            if (in_array('metre', $available_columns)) {
                $select_fields[] = 'metre';
            } else {
                $select_fields[] = '0 as metre';
            }

            if (in_array('miktar_m2', $available_columns)) {
                $select_fields[] = 'miktar_m2';
            } else {
                $select_fields[] = '0 as miktar_m2';
            }

            $sql = "SELECT " . implode(', ', $select_fields) . " FROM {$tablo} ORDER BY id";
            debugLog("📦 Koli liste SQL", $sql);
            break;
            
        default:
            $sql = "SELECT id, malz_kodu, malzeme_adi FROM {$tablo} ORDER BY id";
            break;
    }

    $stmt       = $db->query($sql);
    $malzemeler = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode(['success' => true, 'malzemeler' => $malzemeler]);
} catch (PDOException $e) {
    debugLog("❌ PDO HATASI", [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'kategori' => $kategori,
        'id' => $id
    ]);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 
?> 