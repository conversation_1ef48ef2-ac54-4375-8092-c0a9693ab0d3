<?php
// Hata raporlamayı aç (geliştirme için)
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
include __DIR__ . '/../config.php';

// Eğer zaten giriş yapılmışsa dashboard'a yönlendir
if (isset($_SESSION['b2b_admin_id'])) {
    header('Location: dashboard.php');
    exit;
}

$hata = '';

// Form gönderildi mi kontrol et
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $kullanici_adi = $_POST['kullanici_adi'] ?? '';
    $sifre = $_POST['sifre'] ?? '';
    
    if (empty($kullanici_adi) || empty($sifre)) {
        $hata = 'Kullanıcı adı ve şifre gereklidir.';
    } else {
        // Admin bilgilerini sorgula
        $stmt = $conn->prepare("SELECT id, kullanici_adi, sifre, ad_soyad, yetki_seviyesi 
                               FROM b2b_admin 
                               WHERE kullanici_adi = ? AND aktif = 1");
        $stmt->bind_param("s", $kullanici_adi);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 1) {
            $admin = $result->fetch_assoc();
            
            // Şifre doğrulama
            if (password_verify($sifre, $admin['sifre']) || $sifre === $admin['sifre']) {
                // Oturum bilgilerini ayarla
                $_SESSION['b2b_admin_id'] = $admin['id'];
                $_SESSION['b2b_admin_kullanici'] = $admin['kullanici_adi'];
                $_SESSION['b2b_admin_ad_soyad'] = $admin['ad_soyad'];
                $_SESSION['b2b_admin_yetki'] = $admin['yetki_seviyesi'];
                
                // Son giriş zamanını güncelle
                $conn->query("UPDATE b2b_admin SET son_giris = NOW() WHERE id = " . $admin['id']);
                
                // Dashboard'a yönlendir
                header('Location: dashboard.php');
                exit;
            } else {
                $hata = 'Kullanıcı adı veya şifre hatalı.';
            }
        } else {
            $hata = 'Kullanıcı adı veya şifre hatalı.';
        }
        
        $stmt->close();
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B2B Admin Girişi</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 100%;
            max-width: 400px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .error {
            color: #f44336;
            margin-bottom: 20px;
            text-align: center;
        }
        .info {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>B2B Admin Girişi</h1>
        
        <?php if (!empty($hata)): ?>
            <div class="error"><?php echo $hata; ?></div>
        <?php endif; ?>
        
        <form method="post">
            <div class="form-group">
                <label for="kullanici_adi">Kullanıcı Adı</label>
                <input type="text" id="kullanici_adi" name="kullanici_adi" required>
            </div>
            
            <div class="form-group">
                <label for="sifre">Şifre</label>
                <input type="password" id="sifre" name="sifre" required>
            </div>
            
            <button type="submit" class="btn">Giriş Yap</button>
        </form>
        
        <div class="info">
            <p>B2B Admin Paneli</p>
            <p><a href="../index.php">B2B Ana Sayfaya Dön</a></p>
        </div>
    </div>
</body>
</html> 