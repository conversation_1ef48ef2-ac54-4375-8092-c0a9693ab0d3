<?php
// ajax/uretim_malzeme_listele.php - Reçete Malzemelerini Listele
require_once '../includes/db.php';

header('Content-Type: application/json');

if (!isset($_POST['recete_id']) || !isset($_POST['adet'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Reçete ID ve adet gerekli'
    ]);
    exit;
}

$recete_id = (int)$_POST['recete_id'];
$adet = (int)$_POST['adet'];

if ($recete_id <= 0 || $adet <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz reçete ID veya adet'
    ]);
    exit;
}

try {
    // BASIT YAKLAŞIM: Reçete odaklı, sadece recete_satirlari tablosunu kullan
    // Malzeme bilgileri zaten reçetede kayıtlı
    $stmt = $db->prepare("
        SELECT 
            rs.malzeme_id,
            rs.adet as birim_miktar,
            CASE 
                WHEN rs.parca_ismi IS NOT NULL AND rs.parca_ismi != '' AND rs.parca_ismi != '0' THEN rs.parca_ismi
                WHEN rs.kategori = 'sunta' AND rs.malzeme_id IS NOT NULL THEN CONCAT('Sunta Levha (ID:', rs.malzeme_id, ')')
                WHEN rs.kategori = 'pvc' AND rs.parca_no IS NOT NULL THEN CONCAT('PVC-', rs.parca_no)
                WHEN rs.kategori = 'koli' AND rs.malzeme_id IS NOT NULL THEN CONCAT('Koli Malzeme (ID:', rs.malzeme_id, ')')
                WHEN rs.kategori = 'hirdavat' AND rs.malzeme_id IS NOT NULL THEN CONCAT('Hırdavat (ID:', rs.malzeme_id, ')')
                WHEN rs.parca_no IS NOT NULL THEN rs.parca_no
                ELSE CONCAT('Malzeme-', rs.malzeme_id)
            END as malzeme_adi,
            rs.parca_no as malzeme_kodu,
            rs.parca_ismi,
            rs.birim,
            rs.kategori,
            0 as stok_miktari
        FROM recete_satirlari rs
        WHERE rs.recete_id = ?
        ORDER BY rs.kategori, rs.id
    ");
    
    $stmt->execute([$recete_id]);
    $recete = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($recete)) {
        echo json_encode([
            'success' => false,
            'message' => 'Bu reçetenin malzemeleri bulunamadı'
        ]);
        exit;
    }

    $malzemeler = [];
    
    foreach ($recete as $item) {
        $toplam_gereken = $item['birim_miktar'] * $adet;
        $stok_miktari = (float)$item['stok_miktari'];
        $eksik = max(0, $toplam_gereken - $stok_miktari);
        
        $malzemeler[] = [
            'malzeme_id' => $item['malzeme_id'],
            'malzeme_adi' => $item['malzeme_adi'] ?: $item['malzeme_kodu'] ?: 'Bilinmeyen Malzeme',
            'malzeme_kodu' => $item['malzeme_kodu'] ?: '',
            'birim' => $item['birim'] ?: 'adet',
            'birim_miktar' => $item['birim_miktar'],
            'toplam_gereken' => $toplam_gereken,
            'stok_miktari' => $stok_miktari,
            'eksik' => $eksik
        ];
    }

    echo json_encode([
        'success' => true,
        'malzemeler' => $malzemeler,
        'recete_id' => $recete_id,
        'adet' => $adet
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Malzeme listesi alınamadı: ' . $e->getMessage()
    ]);
}
?> 