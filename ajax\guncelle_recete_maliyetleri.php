<?php
header('Content-Type: application/json');
require_once '../config/database.php';

try {
    $guncellenen_satir = 0;
    $hata_sayisi = 0;
    $detay_rapor = [];
    
    // Tüm reçete satırlarını kategorilere göre güncelle
    
    // 1. SUNTA kategorisi
    $sql_sunta = "
        UPDATE recete_satirlari rs
        JOIN uretim_malzemeler_sunta ums ON rs.malzeme_id = ums.id
        SET rs.toplam_maliyet = rs.miktar * ums.fiyat_cm2
        WHERE rs.kategori = 'sunta'
        AND (rs.toplam_maliyet != (rs.miktar * ums.fiyat_cm2) OR rs.toplam_maliyet IS NULL)
    ";
    
    $stmt = $conn->prepare($sql_sunta);
    $stmt->execute();
    $sunta_guncellenen = $stmt->affected_rows;
    $guncellenen_satir += $sunta_guncellenen;
    
    // 2. PVC kategorisi
    $sql_pvc = "
        UPDATE recete_satirlari rs
        JOIN uretim_malzemeler_pvc ump ON rs.malzeme_id = ump.id
        SET rs.toplam_maliyet = rs.miktar * ump.fiyat
        WHERE rs.kategori = 'pvc'
        AND (rs.toplam_maliyet != (rs.miktar * ump.fiyat) OR rs.toplam_maliyet IS NULL)
    ";
    
    $stmt = $conn->prepare($sql_pvc);
    $stmt->execute();
    $pvc_guncellenen = $stmt->affected_rows;
    $guncellenen_satir += $pvc_guncellenen;
    
    // 3. HIRDAVAT kategorisi
    $sql_hirdavat = "
        UPDATE recete_satirlari rs
        JOIN uretim_malzemeler_hirdavat umh ON rs.malzeme_id = umh.id
        SET rs.toplam_maliyet = rs.miktar * umh.fiyat
        WHERE rs.kategori = 'hirdavat'
        AND (rs.toplam_maliyet != (rs.miktar * umh.fiyat) OR rs.toplam_maliyet IS NULL)
    ";
    
    $stmt = $conn->prepare($sql_hirdavat);
    $stmt->execute();
    $hirdavat_guncellenen = $stmt->affected_rows;
    $guncellenen_satir += $hirdavat_guncellenen;
    
    // 4. KOLI kategorisi
    $sql_koli = "
        UPDATE recete_satirlari rs
        JOIN uretim_malzemeler_koli umk ON rs.malzeme_id = umk.id
        SET rs.toplam_maliyet = rs.miktar * umk.birim_fiyat_adet
        WHERE rs.kategori = 'koli'
        AND (rs.toplam_maliyet != (rs.miktar * umk.birim_fiyat_adet) OR rs.toplam_maliyet IS NULL)
    ";
    
    $stmt = $conn->prepare($sql_koli);
    $stmt->execute();
    $koli_guncellenen = $stmt->affected_rows;
    $guncellenen_satir += $koli_guncellenen;
    
    // Detay rapor hazırla
    $detay_rapor = [
        'sunta' => $sunta_guncellenen,
        'pvc' => $pvc_guncellenen,
        'hirdavat' => $hirdavat_guncellenen,
        'koli' => $koli_guncellenen
    ];
    
    // Log kaydet
    $log_sql = "INSERT INTO malzeme_fiyat_guncelleme_log 
                (malzeme_id, eski_fiyat, yeni_fiyat, guncelleme_tarihi, etkilenen_recete_sayisi) 
                VALUES (0, 0, 0, NOW(), ?)";
    $stmt = $conn->prepare($log_sql);
    $stmt->bind_param('i', $guncellenen_satir);
    $stmt->execute();
    
    echo json_encode([
        'success' => true,
        'guncellenen_satir' => $guncellenen_satir,
        'detay' => $detay_rapor,
        'message' => "Başarıyla güncellendi!",
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Hata: ' . $e->getMessage(),
        'guncellenen_satir' => 0
    ]);
}
?> 