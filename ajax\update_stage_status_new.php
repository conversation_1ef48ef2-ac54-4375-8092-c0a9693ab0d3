<?php
// ajax/update_stage_status_new.php - Aşama Durumu Gü<PERSON>
require_once '../includes/db.php';

header('Content-Type: application/json');

// JSON verisini al
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['stage_id']) || !isset($input['new_status']) || !isset($input['plan_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Eksik parametreler'
    ]);
    exit;
}

$stage_id = $input['stage_id'];
$new_status = $input['new_status'];
$notes = $input['notes'] ?? '';
$tip = $input['tip'] ?? '';
$plan_id = (int)$input['plan_id'];

try {
    // Stage ID'den asama bilgilerini çıkar
    // stage_id formatı: oto_1, man_3 gibi
    if (strpos($stage_id, 'oto_') === 0) {
        $asama_tip = 'otomasyon';
        $asama_sira = (int)str_replace('oto_', '', $stage_id);
        $otomasyon_asamalar = ['Hazırlık', 'Kesim', 'Bant-Delik', 'Kalite Kontrol', 'Paket'];
        $asama_adi = $otomasyon_asamalar[$asama_sira - 1] ?? 'Bilinmeyen';
    } elseif (strpos($stage_id, 'man_') === 0) {
        $asama_tip = 'manuel';
        $asama_sira = (int)str_replace('man_', '', $stage_id);
        $manuel_asamalar = ['Hazırlık', 'Kesim', 'Bant', 'Delik', 'Kalite Kontrol-Otomasyon Birleşim'];
        $asama_adi = $manuel_asamalar[$asama_sira - 1] ?? 'Bilinmeyen';
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Geçersiz aşama ID formatı'
        ]);
        exit;
    }

    // Önce bu aşamanın var olup olmadığını kontrol et
    $stmt = $db->prepare("
        SELECT id FROM uretim_asamalari 
        WHERE plan_id = ? AND asama_adi = ? AND tip = ?
    ");
    $stmt->execute([$plan_id, $asama_adi, $asama_tip]);
    $existing = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existing) {
        // Varsa güncelle
        $stmt = $db->prepare("
            UPDATE uretim_asamalari 
            SET durum = ?, notlar = ?, guncelleme_tarihi = NOW()
            WHERE plan_id = ? AND asama_adi = ? AND tip = ?
        ");
        $stmt->execute([$new_status, $notes, $plan_id, $asama_adi, $asama_tip]);
    } else {
        // Yoksa oluştur
        $stmt = $db->prepare("
            INSERT INTO uretim_asamalari (plan_id, asama_adi, durum, notlar, tip, olusturma_tarihi, guncelleme_tarihi)
            VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([$plan_id, $asama_adi, $new_status, $notes, $asama_tip]);
    }

    // Planın genel durumunu güncelle
    $stmt = $db->prepare("
        SELECT COUNT(*) as toplam_asama,
               SUM(CASE WHEN durum = 'tamamlandi' THEN 1 ELSE 0 END) as tamamlanan
        FROM uretim_asamalari 
        WHERE plan_id = ?
    ");
    $stmt->execute([$plan_id]);
    $asama_stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // Plan durumunu otomatik güncelle
    $plan_durum = 'planlandi';
    if ($asama_stats['tamamlanan'] > 0) {
        if ($asama_stats['tamamlanan'] == $asama_stats['toplam_asama']) {
            $plan_durum = 'tamamlandi';
        } else {
            $plan_durum = 'devam_ediyor';
        }
    }

    $stmt = $db->prepare("UPDATE uretim_planlari SET durum = ? WHERE id = ?");
    $stmt->execute([$plan_durum, $plan_id]);

    echo json_encode([
        'success' => true,
        'message' => 'Aşama durumu güncellendi',
        'stage_name' => $asama_adi,
        'stage_type' => $asama_tip,
        'new_status' => $new_status,
        'plan_status' => $plan_durum
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Güncelleme hatası: ' . $e->getMessage()
    ]);
}
?> 