<?php
// Session kontrolü - eğer başlatılmamışsa başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// UTF-8 Encoding zorla
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
// SÜPER DEBUG - CANLIDA HATALARI GÖR!
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);

require_once 'config/database.php';

// MySQL hatalarÄ±nÄ± gÃ¶ster
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);


// echo "ğŸ”¥ SÃœPER DEBUG AKTÄ°F - TÃœMER HATALAR BURADA GÃ–RÃœNECEK!<br>";
// echo "ğŸ“ PHP Versiyon: " . phpversion() . "<br>";
// echo "ğŸ“ Error Reporting: " . error_reporting() . "<br>";
// echo "ğŸ“ Display Errors: " . ini_get('display_errors') . "<br>";
// echo "ğŸ• Dosya ZamanÄ±: " . date('Y-m-d H:i:s', filemtime(__FILE__)) . "<br>";
// echo "ğŸ”¥ Version: 2.0 - TOPLAM UPDATE VAR<br>";
// echo "</div>";


// echo "âœ… Database include edildi<br>";
// echo "ğŸ”— BaÄŸlantÄ± durumu: " . (isset($conn) ? "MEVCUT (" . get_class($conn) . ")" : "YOK") . "<br>";
// echo "ğŸ“‹ GET ID: " . ($_GET['id'] ?? 'YOK') . "<br>";
// echo "ğŸ“Š POST verisi: " . ($_SERVER['REQUEST_METHOD'] === 'POST' ? "VAR" : "YOK") . "<br>";
// echo "</div>";

$id = intval($_GET['id'] ?? 0);
if ($id <= 0) { 
    echo 'GeÃ§ersiz reÃ§ete ID'; 
    exit; 
}

// Debug sistemi
$debug_messages = [];
$has_error = false;

// GÃ¼ncelleme iÅŸlemi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // echo "<div style='background: blue; color: white; padding: 5px; margin: 5px;'>";
    // echo "ğŸš€ POST REQUEST BAÅLADI<br>";
    // echo "ğŸ“‹ POST Data Count: " . count($_POST) . "<br>";
    // echo "ğŸ¯ ReÃ§ete ID: {$id}<br>";
    // echo "</div>";
    
    // Tablo kolonlarÄ±nÄ± kontrol et
    // echo "<div style='background: purple; color: white; padding: 10px; margin: 5px;'>";
    // echo "ğŸ“Š <strong>RECETE_SATIRLARI TABLO YAPISI:</strong><br>";
    $result = $conn->query("DESCRIBE recete_satirlari");
    $kolonlar = [];
    while ($row = $result->fetch_assoc()) {
        $kolonlar[] = $row['Field'];
        // echo "   â€¢ {$row['Field']} - {$row['Type']}<br>";
    }
    // echo "<br><strong>Toplam Kolon:</strong> " . count($kolonlar) . "<br>";
    // echo "</div>";
    
    // KOLÄ° POST VERÄ°LERÄ°NÄ° DETAYLI GÃ–STER
    // echo "<div style='background: orange; color: white; padding: 10px; margin: 5px;'>";
    // echo "ğŸ“¦ <strong>KOLÄ° POST VERÄ°LERÄ°:</strong><br>";
    if (isset($_POST['koli_malzeme']) && is_array($_POST['koli_malzeme'])) {
        foreach ($_POST['koli_malzeme'] as $index => $malzeme_id) {
            if (!empty($malzeme_id)) {
                $koli_adet = $_POST['koli_adet_miktar'][$index] ?? 'YOK';
                $koli_fiyat = $_POST['koli_toplam_fiyat'][$index] ?? 'YOK';
                // echo "   â€¢ Satır {$index}: Malzeme={$malzeme_id}, Adet={$koli_adet}, Fiyat={$koli_fiyat}<br>";
            }
        }
    } else {
        // echo "   âŒ Koli verisi YOK!<br>";
    }
    // echo "</div>";
    
    try {
        // echo "<div style='background: orange; color: white; padding: 5px; margin: 5px;'>ğŸ”§ TRY BLOÄU BAÅLADI</div>";
        $debug_messages[] = "ğŸ”„ Ä°ÅŸlem baÅŸlatÄ±ldÄ±";
        $conn->begin_transaction();
        
        // Ana reÃ§ete bilgilerini gÃ¼ncelle
        $recete_kodu = trim($_POST['recete_kodu']);
        $versiyon = trim($_POST['versiyon']);
        $urun_id = intval($_POST['urun']);
        
        $debug_messages[] = "ğŸ“ Ana reÃ§ete bilgileri: Kod={$recete_kodu}, Versiyon={$versiyon}, ÃœrÃ¼n={$urun_id}";
        
        try {
            $stmt = $conn->prepare("UPDATE receteler SET recete_kodu = ?, versiyon = ?, urun_id = ? WHERE id = ?");
            $stmt->bind_param('ssii', $recete_kodu, $versiyon, $urun_id, $id);
            $stmt->execute();
            $debug_messages[] = "âœ… Ana reÃ§ete gÃ¼ncellendi - Etkilenen Satır: " . $stmt->affected_rows;
        } catch (Exception $e) {
            $debug_messages[] = "âŒ Ana reÃ§ete gÃ¼ncelleme HATASI: " . $e->getMessage();
            throw $e;
        }
        
        // Mevcut reÃ§ete detaylarÄ±nÄ± sil
        try {
            $stmt = $conn->prepare("DELETE FROM recete_satirlari WHERE recete_id = ?");
            $stmt->bind_param('i', $id);
            $stmt->execute();
            $silinen_satir = $stmt->affected_rows;
            $debug_messages[] = "ğŸ—‘ï¸ Eski reÃ§ete SatırlarÄ± silindi - Silinen: {$silinen_satir} Satır";
        } catch (Exception $e) {
            $debug_messages[] = "âŒ DELETE hatasÄ±: " . $e->getMessage();
            throw $e;
        }
        
        // Yeni reÃ§ete detaylarÄ±nÄ± ekle
        $kategoriler = ['sunta', 'pvc', 'hirdavat', 'koli'];
        $toplam_satir = 0;
        
        foreach ($kategoriler as $kategori) {
            $malzeme_field = $kategori . '_malzeme';
            $kategori_satir = 0;
            
            if (isset($_POST[$malzeme_field]) && is_array($_POST[$malzeme_field])) {
                $debug_messages[] = "ğŸ“¦ {$kategori} kategorisi iÅŸleniyor...";
                
                foreach ($_POST[$malzeme_field] as $index => $malzeme_id) {
                    if (!empty($malzeme_id)) {
                        // Her kategori iÃ§in Ã¶zel INSERT sorgularÄ± (GÃœVENLI FALLBACK)
                        try {
                            if ($kategori == 'sunta') {
                            $parca_no = $_POST['parca_no'][$index] ?? '';
                            $renk = $_POST['renk'][$index] ?? '';
                            $parca_ismi = $_POST['parca_ismi'][$index] ?? '';
                            $adet = floatval($_POST['adet'][$index] ?? 1);
                            $sunta_kesim_boy = floatval($_POST['sunta_kesim_boy'][$index] ?? 0);
                            $sunta_kesim_en = floatval($_POST['sunta_kesim_en'][$index] ?? 0);
                            $toplam_cm2 = floatval($_POST['toplam_cm2'][$index] ?? 0);
                            $toplam_maliyet = floatval($_POST['sunta_toplam_maliyet'][$index] ?? 0);
                            
                            $debug_messages[] = "ğŸ” SUNTA SQL: Malzeme={$malzeme_id}, Boy={$sunta_kesim_boy}, En={$sunta_kesim_en}";
                            
                            try {
                                $stmt = $conn->prepare("INSERT INTO recete_satirlari (recete_id, kategori, malzeme_id, parca_no, renk, parca_ismi, adet, sunta_kesim_boy, sunta_kesim_en, toplam_cm2, toplam_maliyet) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                                if (!$stmt) {
                                    throw new Exception("Prepare hatasÄ±: " . $conn->error);
                                }
                                $stmt->bind_param('ississddddd', $id, $kategori, $malzeme_id, $parca_no, $renk, $parca_ismi, $adet, $sunta_kesim_boy, $sunta_kesim_en, $toplam_cm2, $toplam_maliyet);
                                if (!$stmt->execute()) {
                                    throw new Exception("Execute hatasÄ±: " . $stmt->error);
                                }
                                $debug_messages[] = "âœ… SUNTA INSERT baÅŸarÄ±lÄ± - ID: " . $conn->insert_id;
                            } catch (Exception $e) {
                                $debug_messages[] = "âŒ SUNTA INSERT HATASI: " . $e->getMessage();
                                throw $e;
                            }
                            
                            $debug_messages[] = "   â• SUNTA #{$kategori_satir}: Malzeme={$malzeme_id}, CM2={$toplam_cm2}, Maliyet={$toplam_maliyet}â‚º";
                            
                        } elseif ($kategori == 'pvc') {
                            $parca_no = $_POST['pvc_parca_no'][$index] ?? '';
                            $renk = $_POST['pvc_renk'][$index] ?? '';
                            $parca_ismi = $_POST['pvc_parca_ismi'][$index] ?? '';
                            $adet = floatval($_POST['pvc_adet'][$index] ?? 1);
                            $pvc_bantlama_boy = floatval($_POST['pvc_bantlama_boy'][$index] ?? 0);
                            $pvc_bantlama_en = floatval($_POST['pvc_bantlama_en'][$index] ?? 0);
                            $pvc_kesim_boy = floatval($_POST['pvc_kesim_boy'][$index] ?? 0);
                            $pvc_kesim_en = floatval($_POST['pvc_kesim_en'][$index] ?? 0);
                            $toplam_metre = floatval($_POST['pvc_toplam_metre'][$index] ?? 0);
                            $toplam_maliyet = floatval($_POST['pvc_toplam_maliyet'][$index] ?? 0);
                            
                            $debug_messages[] = "ğŸ” PVC SQL: Malzeme={$malzeme_id}, BantBoy={$pvc_bantlama_boy}, BantEn={$pvc_bantlama_en}, Metre={$toplam_metre}";
                            
                            try {
                                $stmt = $conn->prepare("INSERT INTO recete_satirlari (recete_id, kategori, malzeme_id, parca_no, renk, parca_ismi, adet, pvc_bantlama_boy, pvc_bantlama_en, pvc_kesim_boy, pvc_kesim_en, toplam_metre, toplam_maliyet) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                                if (!$stmt) {
                                    throw new Exception("PVC Prepare hatasÄ±: " . $conn->error);
                                }
                                $stmt->bind_param('ississddddddd', $id, $kategori, $malzeme_id, $parca_no, $renk, $parca_ismi, $adet, $pvc_bantlama_boy, $pvc_bantlama_en, $pvc_kesim_boy, $pvc_kesim_en, $toplam_metre, $toplam_maliyet);
                                if (!$stmt->execute()) {
                                    throw new Exception("PVC Execute hatasÄ±: " . $stmt->error);
                                }
                                $debug_messages[] = "âœ… PVC INSERT baÅŸarÄ±lÄ± - ID: " . $conn->insert_id;
                            } catch (Exception $e) {
                                $debug_messages[] = "âŒ PVC INSERT HATASI: " . $e->getMessage();
                                throw $e;
                            }
                            
                            $debug_messages[] = "   â• PVC #{$kategori_satir}: Malzeme={$malzeme_id}, Metre={$toplam_metre}, Maliyet={$toplam_maliyet}â‚º";
                            
                        } elseif ($kategori == 'hirdavat') {
                            $parca_ismi = $_POST['hirdavat_adi'][$index] ?? '';
                            $adet = floatval($_POST['hirdavat_adet'][$index] ?? 1);
                            $toplam_maliyet = floatval($_POST['hirdavat_toplam_maliyet'][$index] ?? 0);
                            
                            $debug_messages[] = "ğŸ” HIRDAVAT SQL: Malzeme={$malzeme_id}, Adet={$adet}, Maliyet={$toplam_maliyet}";
                            
                            try {
                                $stmt = $conn->prepare("INSERT INTO recete_satirlari (recete_id, kategori, malzeme_id, parca_ismi, adet, toplam_maliyet) VALUES (?, ?, ?, ?, ?, ?)");
                                if (!$stmt) {
                                    throw new Exception("HIRDAVAT Prepare hatasÄ±: " . $conn->error);
                                }
                                $stmt->bind_param('isisdd', $id, $kategori, $malzeme_id, $parca_ismi, $adet, $toplam_maliyet);
                                if (!$stmt->execute()) {
                                    throw new Exception("HIRDAVAT Execute hatasÄ±: " . $stmt->error);
                                }
                                $debug_messages[] = "âœ… HIRDAVAT INSERT baÅŸarÄ±lÄ± - ID: " . $conn->insert_id;
                            } catch (Exception $e) {
                                $debug_messages[] = "âŒ HIRDAVAT INSERT HATASI: " . $e->getMessage();
                                throw $e;
                            }
                            
                            $debug_messages[] = "   â• HIRDAVAT #{$kategori_satir}: Malzeme={$malzeme_id}, Adet={$adet}, Maliyet={$toplam_maliyet}â‚º";
                            
                        } elseif ($kategori == 'koli') {
                            $koli_turu = $_POST['koli_turu'][$index] ?? 'OZEL_OLCU';
                            $olcu = floatval($_POST['koli_olcu'][$index] ?? 0);
                            $metre = floatval($_POST['koli_cm'][$index] ?? 0);
                            $m2 = floatval($_POST['koli_m2'][$index] ?? 0);
                            $adet_miktar = floatval($_POST['koli_adet_miktar'][$index] ?? 1);
                            $toplam_fiyat = floatval($_POST['koli_toplam_fiyat'][$index] ?? 0);
                            
                            $debug_messages[] = "ğŸ” KOLI SQL: Malzeme={$malzeme_id}, TÃ¼r={$koli_turu}, Ã–lÃ§Ã¼={$olcu}, Metre={$metre}, M2={$m2}, Adet={$adet_miktar}, Fiyat={$toplam_fiyat}";
                            
                            try {
                                // KOLI iÃ§in maliyet kolonlarÄ±na yazalÄ±m (koli_toplam_maliyet kolonu yok)
                                $stmt = $conn->prepare("INSERT INTO recete_satirlari (recete_id, kategori, malzeme_id, koli_turu, olcu, metre, m2, adet_miktar, toplam_fiyat, toplam_maliyet) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                                if (!$stmt) {
                                    throw new Exception("KOLI Prepare hatasÄ±: " . $conn->error);
                                }
                                $stmt->bind_param('iisssddddd', $id, $kategori, $malzeme_id, $koli_turu, $olcu, $metre, $m2, $adet_miktar, $toplam_fiyat, $toplam_fiyat);
                                if (!$stmt->execute()) {
                                    throw new Exception("KOLI Execute hatasÄ±: " . $stmt->error);
                                }
                                $debug_messages[] = "âœ… KOLI INSERT baÅŸarÄ±lÄ± - ID: " . $conn->insert_id;
                            } catch (Exception $e) {
                                $debug_messages[] = "âŒ KOLI INSERT HATASI: " . $e->getMessage();
                                throw $e;
                            }
                            
                            $debug_messages[] = "   â• KOLI #{$kategori_satir}: Malzeme={$malzeme_id}, Adet={$adet_miktar}, Fiyat={$toplam_fiyat}â‚º";
                            }
                            
                            // Her kategoride kendi execute'u var
                            $kategori_satir++;
                            $toplam_satir++;
                            
                        } catch (Exception $e) {
                            // GÃœVENLI FALLBACK: Basit INSERT
                            $debug_messages[] = "âš ï¸ {$kategori} Ã¶zel INSERT hatasÄ±: " . $e->getMessage();
                            $debug_messages[] = "ğŸ”„ Basit INSERT kullanÄ±lÄ±yor...";
                            
                            $stmt = $conn->prepare("INSERT INTO recete_satirlari (recete_id, kategori, malzeme_id) VALUES (?, ?, ?)");
                            $stmt->bind_param('isi', $id, $kategori, $malzeme_id);
                            $stmt->execute();
                            
                            $kategori_satir++;
                            $toplam_satir++;
                            $debug_messages[] = "   â• {$kategori} #{$kategori_satir}: Malzeme={$malzeme_id} (BASÄ°T KAYIT)";
                        }
                    }
                }
                $debug_messages[] = "âœ… {$kategori} kategorisi tamamlandÄ± ({$kategori_satir} Satır)";
            } else {
                $debug_messages[] = "â„¹ï¸ {$kategori} kategorisinde veri yok";
            }
        }
        
        $debug_messages[] = "ğŸ‰ Toplam {$toplam_satir} Satır eklendi";
        
        // RECETELER tablosundaki toplamlarÄ± gÃ¼ncelle
        $debug_messages[] = "ğŸ“Š ReÃ§ete toplamlarÄ± hesaplanÄ±yor...";
        
        // Her kategori iÃ§in toplamlarÄ± hesapla - GÃœNCELLENEN SORGULAR
        $sunta_toplam = $conn->query("SELECT COALESCE(SUM(toplam_maliyet), 0) as toplam FROM recete_satirlari WHERE recete_id = $id AND kategori = 'sunta'")->fetch_assoc()['toplam'];
        $pvc_toplam = $conn->query("SELECT COALESCE(SUM(toplam_maliyet), 0) as toplam FROM recete_satirlari WHERE recete_id = $id AND kategori = 'pvc'")->fetch_assoc()['toplam'];
        $hirdavat_toplam = $conn->query("SELECT COALESCE(SUM(toplam_maliyet), 0) as toplam FROM recete_satirlari WHERE recete_id = $id AND kategori = 'hirdavat'")->fetch_assoc()['toplam'];
        
        // KOLÄ° Ä°Ã‡Ä°N Ã–ZEL HESAPLAMA - toplam_fiyat kullan
        $koli_sorgu = "SELECT COALESCE(SUM(COALESCE(toplam_fiyat, toplam_maliyet, 0)), 0) as toplam FROM recete_satirlari WHERE recete_id = $id AND kategori = 'koli'";
        $debug_messages[] = "ğŸ” KOLI SORGUSU: {$koli_sorgu}";
        $koli_result = $conn->query($koli_sorgu);
        $koli_toplam = $koli_result->fetch_assoc()['toplam'];
        $debug_messages[] = "ğŸ“Š KOLÄ° SORGU SONUCU: {$koli_toplam}";
        
        $genel_toplam = $sunta_toplam + $pvc_toplam + $hirdavat_toplam + $koli_toplam;
        
        $debug_messages[] = "ğŸ’° Toplamlar: Sunta={$sunta_toplam}, PVC={$pvc_toplam}, HÄ±rdavat={$hirdavat_toplam}, Koli={$koli_toplam}, GENEL={$genel_toplam}";
        
        // Receteler tablosunu gÃ¼ncelle
        $update_sql = "UPDATE receteler SET 
                       sunta_toplam_maliyet = ?,
                       pvc_toplam_maliyet = ?,
                       hirdavat_toplam_maliyet = ?,
                       koli_toplam_maliyet = ?,
                       genel_toplam_maliyet = ?
                       WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param('dddddi', $sunta_toplam, $pvc_toplam, $hirdavat_toplam, $koli_toplam, $genel_toplam, $id);
        $update_stmt->execute();
        
        $debug_messages[] = "âœ… ReÃ§ete toplamlarÄ± gÃ¼ncellendi - Etkilenen: " . $update_stmt->affected_rows;
        
        $conn->commit();
        $debug_messages[] = "ğŸ’¾ VeritabanÄ± commit edildi";
        
        // âš ï¸ KRÄ°TÄ°K: COMMIT SONRASI KONTROL!
        $kontrol = $conn->prepare("SELECT * FROM recete_satirlari WHERE recete_id = ? AND kategori = 'koli'");
        $kontrol->bind_param('i', $id);
        $kontrol->execute();
        $sonuc = $kontrol->get_result();
        $koli_kontrol_sayisi = 0;
        while ($row = $sonuc->fetch_assoc()) {
            $koli_kontrol_sayisi++;
            $debug_messages[] = "ğŸ“Š KOLI KONTROL #{$koli_kontrol_sayisi} - ID: {$row['id']}, Kategori: {$row['kategori']}, Malzeme: {$row['malzeme_id']}, Adet: {$row['adet_miktar']}, ToplamFiyat: {$row['toplam_fiyat']}, ToplamMaliyet: {$row['toplam_maliyet']}";
        }
        $debug_messages[] = "ğŸ” TOPLAM KOLÄ° SATIR SAYISI (COMMIT SONRASI): {$koli_kontrol_sayisi}";
        
        $debug_messages[] = "ğŸ‰ ReÃ§ete baÅŸarÄ±yla gÃ¼ncellendi!";
        
        // echo "<div style='background: green; color: white; padding: 20px; margin: 10px; font-size: 20px; text-align: center;'>";
        echo "ğŸ‰ BAÅARILI! ReÃ§ete baÅŸarÄ±yla gÃ¼ncellendi!";
        // echo "<br><a href='urun_receteleri.php' style='color: white; text-decoration: underline;'>â† Geri DÃ¶n</a>";
        // echo "</div>";
        // BaÅŸarÄ± durumunda yÃ¶nlendirme yapma, debug mesajlarÄ±nÄ± gÃ¶ster
        // exit;
        
    } catch (Exception $e) {
        // echo "<div style='background: red; color: white; padding: 10px; margin: 5px; border: 3px solid #fff;'>";
        // echo "<h3>ğŸ’¥ HATA YAKALANDI!</h3>";
        // echo "<strong>Hata MesajÄ±:</strong> " . $e->getMessage() . "<br>";
        // echo "<strong>Hata DosyasÄ±:</strong> " . $e->getFile() . "<br>";
        // echo "<strong>Hata SatırÄ±:</strong> " . $e->getLine() . "<br>";
        // echo "<strong>Stack Trace:</strong><br><pre>" . $e->getTraceAsString() . "</pre>";
        // echo "</div>";
        
        $conn->rollback();
        $has_error = true;
        $debug_messages[] = "âŒ HATA: " . $e->getMessage();
        $debug_messages[] = "â†©ï¸ Rollback yapÄ±ldÄ±";
        $debug_messages[] = "ğŸ“‚ Hata dosyasÄ±: " . $e->getFile();
        $debug_messages[] = "ğŸ“ Hata SatırÄ±: " . $e->getLine();
        error_log("ReÃ§ete dÃ¼zenleme hatasÄ±: " . $e->getMessage());
    }
}

// echo "<div style='background: purple; color: white; padding: 5px; margin: 5px;'>ğŸ“‹ MEVCUT REÃ‡ETE VERÄ°LERÄ° Ã‡EKÄ°LÄ°YOR...</div>";

// Mevcut reÃ§ete bilgilerini Ã§ek
$stmt = $conn->prepare("SELECT * FROM receteler WHERE id = ?");
$stmt->bind_param('i', $id);
$stmt->execute();
$recete = $stmt->get_result()->fetch_assoc();

if (!$recete) { 
    echo 'ReÃ§ete bulunamadÄ±.'; 
    exit; 
}

// Mevcut reÃ§ete detaylarÄ±nÄ± Ã§ek - her kategori iÃ§in ayrÄ± sorgu
$detaylar = [];

// Sunta detaylarÄ± - tÃ¼m alanlarÄ± Ã§ek
$stmt = $conn->prepare("SELECT rs.*, m.malzeme_adi, m.malz_kodu, m.birim, m.stok_miktar as stok, m.fiyat_cm2 as fiyat 
                        FROM recete_satirlari rs 
                        LEFT JOIN uretim_malzemeler_sunta m ON rs.malzeme_id = m.id 
                        WHERE rs.recete_id = ? AND rs.kategori = 'sunta'
                        ORDER BY rs.id");
$stmt->bind_param('i', $id);
$stmt->execute();
$sunta_detaylar = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
foreach ($sunta_detaylar as $detay) {
    $detay['kategori'] = 'sunta';
    $detaylar[] = $detay;
}

// PVC detaylarÄ± - tÃ¼m alanlarÄ± Ã§ek
$stmt = $conn->prepare("SELECT rs.*, m.malzeme_adi, m.malz_kodu, m.birim, m.stok_miktar as stok, m.fiyat 
                        FROM recete_satirlari rs 
                        LEFT JOIN uretim_malzemeler_pvc m ON rs.malzeme_id = m.id 
                        WHERE rs.recete_id = ? AND rs.kategori = 'pvc'
                        ORDER BY rs.id");
$stmt->bind_param('i', $id);
$stmt->execute();
$pvc_detaylar = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
foreach ($pvc_detaylar as $detay) {
    $detay['kategori'] = 'pvc';
    $detaylar[] = $detay;
}

// HÄ±rdavat detaylarÄ±
$stmt = $conn->prepare("SELECT rs.*, m.malzeme_adi, m.birim, m.stok_miktar as stok, m.fiyat 
                        FROM recete_satirlari rs 
                        LEFT JOIN uretim_malzemeler_hirdavat m ON rs.malzeme_id = m.id 
                        WHERE rs.recete_id = ? AND rs.kategori = 'hirdavat'
                        ORDER BY rs.id");
$stmt->bind_param('i', $id);
$stmt->execute();
$hirdavat_detaylar = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
foreach ($hirdavat_detaylar as $detay) {
    $detay['kategori'] = 'hirdavat';
    $detaylar[] = $detay;
}

// Koli detaylarÄ± - tÃ¼m alanlarÄ± Ã§ek (m.fiyat kolonu yok, kaldÄ±rÄ±ldÄ±)
$stmt = $conn->prepare("SELECT rs.*, m.malzeme_adi, m.birim, m.stok_miktar as stok,
                               m.birim_fiyat_m2, m.birim_fiyat_adet,
                               rs.koli_turu, rs.olcu as rs_olcu, rs.metre as rs_metre, rs.m2 as rs_m2,
                               rs.adet_miktar, rs.kritik_stok, rs.toplam_fiyat,
                               m.olcu as m_olcu, m.metre as m_metre, m.en_cm, m.boy_cm
                        FROM recete_satirlari rs
                        LEFT JOIN uretim_malzemeler_koli m ON rs.malzeme_id = m.id
                        WHERE rs.recete_id = ? AND rs.kategori = 'koli'
                        ORDER BY rs.id");
$stmt->bind_param('i', $id);
$stmt->execute();
$koli_detaylar = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
foreach ($koli_detaylar as $detay) {
    $detay['kategori'] = 'koli';
    $detaylar[] = $detay;
}

// Kategorilere gÃ¶re grupla
$kategoriDetay = [
    'sunta' => [],
    'pvc' => [],
    'hirdavat' => [],
    'koli' => []
];

foreach ($detaylar as $detay) {
    $kategoriDetay[$detay['kategori']][] = $detay;
}

// DEBUG: Kategorilerin durumunu gÃ¶ster
// echo "<div style='background: blue; color: white; padding: 10px; margin: 5px;'>";
// echo "ğŸ“Š <strong>MEVCUT VERÄ° DURUM RAPORU:</strong><br>";
// echo "   â€¢ Sunta: " . count($kategoriDetay['sunta']) . " Satır<br>";
// echo "   â€¢ PVC: " . count($kategoriDetay['pvc']) . " Satır<br>";
// echo "   â€¢ HÄ±rdavat: " . count($kategoriDetay['hirdavat']) . " Satır<br>";
// echo "   â€¢ Koli: " . count($kategoriDetay['koli']) . " Satır<br>";
if (!empty($kategoriDetay['koli'])) {
    // echo "   ğŸ” Koli detaylarÄ±:<br>";
    foreach ($kategoriDetay['koli'] as $i => $koli) {
        // echo "      #{$i}: ID={$koli['malzeme_id']}, Adet={$koli['adet_miktar']}, Fiyat={$koli['toplam_fiyat']}<br>";
    }
}
// echo "</div>";

// echo "<div style='background: darkgreen; color: white; padding: 5px; margin: 5px;'>ğŸ‰ TÃœM PHP Ä°ÅLEMLERÄ° TAMAMLANDI - HTML BAÅLIYOR</div>";
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Reçete Düzenle - <?= htmlspecialchars($recete['recete_kodu']) ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f5f7fa; }
        .card { margin-bottom: 1.5rem; }
        .section-title { font-size: 1.2rem; font-weight: bold; color: #337ab7; margin-top: 1.5rem; }
        .table-responsive { max-height: 400px; overflow-y: auto; }
        .btn-remove { background: #dc3545; color: white; border: none; padding: 2px 6px; border-radius: 3px; }
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
            <span><i class="fas fa-edit me-2"></i>Reçete Düzenle: <?= htmlspecialchars($recete['recete_kodu']) ?></span>
            <div>
                <a href="urun_receteleri.php" class="btn btn-dark btn-sm me-2">
                    <i class="fas fa-arrow-left"></i> Geri Dön
                </a>
                <a href="urun_receteleri.php" class="btn btn-outline-dark btn-sm">
                    <i class="fas fa-list"></i> Reçete Listesi
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if (!empty($debug_messages)): ?>
            <div class="alert <?= $has_error ? 'alert-danger' : 'alert-success' ?> mb-3">
                <h6><i class="fas fa-bug"></i> Debug Bilgileri:</h6>
                <div style="max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                    <?php foreach ($debug_messages as $msg): ?>
                        <div><?= htmlspecialchars($msg) ?></div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> <strong>Bilgi:</strong> 
                Bu sayfada reçeteyi tamamen düzenleyebilirsiniz. Malzeme ekleyebilir, çıkarabilir ve miktarları değiştirebilirsiniz.
                <br><strong>Not:</strong> Malzeme fiyatları değiştiğinde reçete maliyeti otomatik güncellenir.
            </div>
            
            <form id="recete-form" method="post" action="" onsubmit="return prepareDataForSubmit()">
                                 <div class="row g-3 mb-3">
                     <div class="col-md-6">
                         <label for="urun-select" class="form-label">Ürün</label>
                         <select class="form-select" id="urun-select" name="urun" required>
                             <option value="">Yükleniyor...</option>
                         </select>
                         <input type="hidden" name="current_urun_id" value="<?= $recete['urun_id'] ?>">
                     </div>
                    <div class="col-md-6">
                        <label class="form-label">Reçete Kodu</label>
                        <input type="text" class="form-control" name="recete_kodu" id="recete-kodu" 
                               value="<?= htmlspecialchars($recete['recete_kodu']) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Versiyon</label>
                        <input type="text" class="form-control" name="versiyon" 
                               value="<?= htmlspecialchars($recete['versiyon']) ?>" placeholder="Ã–rn: 1.0">
                    </div>
                </div>

                <!-- SUNTA SEKSÄ°YONU -->
                <div class="section-title">SUNTA</div>
                <div class="table-responsive mb-3">
                    <table class="table table-bordered align-middle" id="sunta-table">
                        <thead class="table-light">
                        <tr>
                            <th>Malz Kodu</th><th>Malzeme Adı</th><th>Birim</th><th>Stok</th><th>Fiyat cm2</th>
                            <th>Parça No</th><th>Renk</th><th>Parça İsmi</th><th>Adet</th>
                            <th colspan="2" class="text-center bg-warning text-dark">Sunta Kesim</th>
                            <th>Toplam cm2</th><th>Toplam Maliyet</th><th>İşlemler</th>
                        </tr>
                        <tr>
                            <th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th>
                            <th>Boy</th><th>En</th><th></th><th></th><th></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if (!empty($kategoriDetay['sunta'])): ?>
                            <?php foreach ($kategoriDetay['sunta'] as $index => $detay): ?>
                            <tr>
                                <td><select class="form-select sunta-malzeme" name="sunta_malzeme[]" data-selected="<?= $detay['malzeme_id'] ?>">
                                    <option value="">YÃ¼kleniyor...</option>
                                </select></td>
                                <td><input type="text" class="form-control" name="sunta_malzeme_adi[]" value="<?= htmlspecialchars($detay['malzeme_adi'] ?? '') ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="sunta_birim[]" value="<?= htmlspecialchars($detay['birim'] ?? '') ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="sunta_stok[]" value="<?= $detay['stok'] ?? '' ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="sunta_fiyat_cm2[]" value="<?= $detay['fiyat'] ?? '' ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="parca_no[]" value="<?= htmlspecialchars($detay['parca_no'] ?? ($index + 1)) ?>" onchange="calculateSuntaRow(this)"></td>
                                <td><input type="text" class="form-control" name="renk[]" value="<?= htmlspecialchars($detay['renk'] ?? '') ?>" onchange="calculateSuntaRow(this)"></td>
                                <td><input type="text" class="form-control" name="parca_ismi[]" value="<?= htmlspecialchars($detay['parca_ismi'] ?? '') ?>" onchange="calculateSuntaRow(this)"></td>
                                <td><input type="number" class="form-control" name="adet[]" value="<?= $detay['adet'] ?? 1 ?>" onchange="calculateSuntaRow(this)"></td>
                                <td><input type="number" class="form-control" name="sunta_kesim_boy[]" value="<?= $detay['sunta_kesim_boy'] ?? 0 ?>" step="0.1" onchange="calculateSuntaRow(this)"></td>
                                <td><input type="number" class="form-control" name="sunta_kesim_en[]" value="<?= $detay['sunta_kesim_en'] ?? 0 ?>" step="0.1" onchange="calculateSuntaRow(this)"></td>
                                <td><input type="number" class="form-control" name="toplam_cm2[]" value="<?= $detay['toplam_cm2'] ?? $detay['miktar'] ?? 0 ?>" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="sunta_toplam_maliyet[]" value="<?= $detay['toplam_maliyet'] ?? $detay['maliyet'] ?? 0 ?>" readonly="readonly"></td>
                                <td><button type="button" class="btn btn-remove" onclick="removeSuntaRow(this)">Sil</button></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td><select class="form-select sunta-malzeme" name="sunta_malzeme[]"></select></td>
                                <td><input type="text" class="form-control" name="sunta_malzeme_adi[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="sunta_birim[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="sunta_stok[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="sunta_fiyat_cm2[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="parca_no[]" value="1"></td>
                                <td><input type="text" class="form-control" name="renk[]"></td>
                                <td><input type="text" class="form-control" name="parca_ismi[]"></td>
                                <td><input type="number" class="form-control" name="adet[]" value="1"></td>
                                <td><input type="number" class="form-control" name="sunta_kesim_boy[]" value="0" step="0.1"></td>
                                <td><input type="number" class="form-control" name="sunta_kesim_en[]" value="0" step="0.1"></td>
                                <td><input type="number" class="form-control" name="toplam_cm2[]" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="sunta_toplam_maliyet[]" readonly="readonly"></td>
                                <td><button type="button" class="btn btn-success" onclick="addSuntaRow()">Satır Ekle</button></td>
                            </tr>
                        <?php endif; ?>
                        </tbody>
                    </table>
                    <div class="mb-3">
                        <button type="button" class="btn btn-success" onclick="addSuntaRow()">
                            <i class="fas fa-plus"></i> Sunta Satır Ekle
                        </button>
                    </div>
                </div>

                <!-- PVC SEKSÄ°YONU -->
                <div class="section-title">PVC Bant</div>
                <div class="table-responsive mb-3">
                    <table class="table table-bordered align-middle" id="pvc-table">
                        <thead class="table-light">
                        <tr>
                            <th>Malz Kodu</th><th>Malzeme Adı</th><th>Birim</th><th>Stok</th><th>Fiyat</th>
                            <th>Parça No</th><th>Renk</th><th>Parça İsmi</th><th>Adet</th>
                            <th colspan="2" class="text-center bg-info text-white">PVC Bantlama</th>
                            <th colspan="2" class="text-center bg-warning text-dark">PVC Kesim</th>
                            <th>Toplam Metre</th><th>Toplam Maliyet</th><th>İşlemler</th>
                        </tr>
                        <tr>
                            <th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th><th></th>
                            <th>Boy</th><th>En</th><th>Boy</th><th>En</th><th></th><th></th><th></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if (!empty($kategoriDetay['pvc'])): ?>
                            <?php foreach ($kategoriDetay['pvc'] as $index => $detay): ?>
                            <tr>
                                <td><select class="form-select pvc-malzeme" name="pvc_malzeme[]" data-selected="<?= $detay['malzeme_id'] ?>">
                                    <option value="">YÃ¼kleniyor...</option>
                                </select></td>
                                <td><input type="text" class="form-control" name="pvc_malzeme_adi[]" value="<?= htmlspecialchars($detay['malzeme_adi'] ?? '') ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="pvc_birim[]" value="<?= htmlspecialchars($detay['birim'] ?? '') ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="pvc_stok[]" value="<?= $detay['stok'] ?? '' ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="pvc_fiyat[]" value="<?= $detay['fiyat'] ?? '' ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="pvc_parca_no[]" value="<?= htmlspecialchars($detay['parca_no'] ?? '') ?>" onchange="calculatePvcRow(this)"></td>
                                <td><input type="text" class="form-control" name="pvc_renk[]" value="<?= htmlspecialchars($detay['renk'] ?? '') ?>" onchange="calculatePvcRow(this)"></td>
                                <td><input type="text" class="form-control" name="pvc_parca_ismi[]" value="<?= htmlspecialchars($detay['parca_ismi'] ?? '') ?>" onchange="calculatePvcRow(this)"></td>
                                <td><input type="number" class="form-control" name="pvc_adet[]" value="<?= $detay['adet'] ?? 1 ?>" onchange="calculatePvcRow(this)"></td>
                                <td><input type="number" class="form-control" name="pvc_bantlama_boy[]" value="<?= $detay['pvc_bantlama_boy'] ?? 0 ?>" step="0.1" onchange="calculatePvcRow(this)"></td>
                                <td><input type="number" class="form-control" name="pvc_bantlama_en[]" value="<?= $detay['pvc_bantlama_en'] ?? 0 ?>" step="0.1" onchange="calculatePvcRow(this)"></td>
                                <td><input type="number" class="form-control" name="pvc_kesim_boy[]" value="<?= $detay['pvc_kesim_boy'] ?? 0 ?>" step="0.1" onchange="calculatePvcRow(this)"></td>
                                <td><input type="number" class="form-control" name="pvc_kesim_en[]" value="<?= $detay['pvc_kesim_en'] ?? 0 ?>" step="0.1" onchange="calculatePvcRow(this)"></td>
                                <td><input type="number" class="form-control" name="pvc_toplam_metre[]" value="<?= $detay['pvc_toplam_metre'] ?? $detay['miktar'] ?? 0 ?>" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="pvc_toplam_maliyet[]" value="<?= $detay['toplam_maliyet'] ?? $detay['maliyet'] ?? 0 ?>" readonly="readonly"></td>
                                <td><button type="button" class="btn btn-remove" onclick="removePvcRow(this)">Sil</button></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td><select class="form-select pvc-malzeme" name="pvc_malzeme[]"></select></td>
                                <td><input type="text" class="form-control" name="pvc_malzeme_adi[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="pvc_birim[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="pvc_stok[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="pvc_fiyat[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="pvc_parca_no[]"></td>
                                <td><input type="text" class="form-control" name="pvc_renk[]"></td>
                                <td><input type="text" class="form-control" name="pvc_parca_ismi[]"></td>
                                <td><input type="number" class="form-control" name="pvc_adet[]" value="1"></td>
                                <td><input type="number" class="form-control" name="pvc_bantlama_boy[]" value="0" step="0.1"></td>
                                <td><input type="number" class="form-control" name="pvc_bantlama_en[]" value="0" step="0.1"></td>
                                <td><input type="number" class="form-control" name="pvc_kesim_boy[]" value="0" step="0.1"></td>
                                <td><input type="number" class="form-control" name="pvc_kesim_en[]" value="0" step="0.1"></td>
                                <td><input type="number" class="form-control" name="pvc_toplam_metre[]" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="pvc_toplam_maliyet[]" readonly="readonly"></td>
                                <td><button type="button" class="btn btn-success" onclick="addPvcRow()">Satır Ekle</button></td>
                            </tr>
                        <?php endif; ?>
                        </tbody>
                    </table>
                    <div class="mb-3">
                        <button type="button" class="btn btn-success" onclick="addPvcRow()">
                            <i class="fas fa-plus"></i> PVC Satır Ekle
                        </button>
                    </div>
                </div>

                <!-- HIRDAVAT SEKSÄ°YONU -->
                <div class="section-title">Hırdavat</div>
                <div class="table-responsive mb-3">
                    <table class="table table-bordered align-middle" id="hirdavat-table">
                        <thead class="table-light">
                        <tr>
                            <th>Malz Kodu</th><th>Malzeme Adı</th><th>Birim</th><th>Stok</th><th>Fiyat</th>
                            <th>Hırdavat Malzeme</th><th>Adet</th><th>Maliyet</th><th>Toplam Maliyet</th><th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if (!empty($kategoriDetay['hirdavat'])): ?>
                            <?php foreach ($kategoriDetay['hirdavat'] as $index => $detay): ?>
                            <tr>
                                <td><select class="form-select hirdavat-malzeme" name="hirdavat_malzeme[]" data-selected="<?= $detay['malzeme_id'] ?>">
                                    <option value="">YÃ¼kleniyor...</option>
                                </select></td>
                                <td><input type="text" class="form-control" name="hirdavat_malzeme_adi[]" value="<?= htmlspecialchars($detay['malzeme_adi']) ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="hirdavat_birim[]" value="<?= htmlspecialchars($detay['birim']) ?>" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="hirdavat_stok[]" value="<?= $detay['stok'] ?>" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="hirdavat_fiyat[]" value="<?= $detay['fiyat'] ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="hirdavat_adi[]" onchange="calculateHirdavatRow(this)"></td>
                                <td><input type="number" class="form-control" name="hirdavat_adet[]" value="<?= $detay['miktar'] ?? $detay['adet'] ?? 1 ?>" onchange="calculateHirdavatRow(this)"></td>
                                <td><input type="number" class="form-control" name="hirdavat_maliyet[]" value="<?= $detay['fiyat'] ?? 0 ?>" onchange="calculateHirdavatRow(this)"></td>
                                <td><input type="number" class="form-control" name="hirdavat_toplam_maliyet[]" value="<?= $detay['maliyet'] ?? $detay['toplam_maliyet'] ?? 0 ?>" readonly="readonly"></td>
                                <td><button type="button" class="btn btn-remove" onclick="removeHirdavatRow(this)">Sil</button></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td><select class="form-select hirdavat-malzeme" name="hirdavat_malzeme[]"></select></td>
                                <td><input type="text" class="form-control" name="hirdavat_malzeme_adi[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="hirdavat_birim[]" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="hirdavat_stok[]" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="hirdavat_fiyat[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="hirdavat_adi[]"></td>
                                <td><input type="number" class="form-control" name="hirdavat_adet[]" value="1"></td>
                                <td><input type="number" class="form-control" name="hirdavat_maliyet[]" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="hirdavat_toplam_maliyet[]" readonly="readonly"></td>
                                <td><button type="button" class="btn btn-success" onclick="addHirdavatRow()">Satır Ekle</button></td>
                            </tr>
                        <?php endif; ?>
                        </tbody>
                    </table>
                    <div class="mb-3">
                        <button type="button" class="btn btn-success" onclick="addHirdavatRow()">
                            <i class="fas fa-plus"></i> Hırdavat Satır Ekle
                        </button>
                    </div>
                </div>

                <!-- KOLÄ° SEKSÄ°YONU -->
                <div class="section-title">Koli</div>
                <div class="table-responsive mb-3">
                    <table class="table table-bordered align-middle" id="koli-table">
                        <thead class="table-light">
                        <tr>
                            <th>Malz Kodu</th><th>Malzeme Adı</th><th>Birim</th><th>Stok</th>
                            <th>Birim Fiyat (₺/m²)</th><th>Birim Fiyat (₺/adet)</th><th>Koli Türü</th>
                            <th>Ölçü</th><th>cm</th><th>m²</th><th>Adet Miktar</th><th>Kritik Stok</th>
                            <th>Toplam Fiyat</th><th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php if (!empty($kategoriDetay['koli'])): ?>
                            <?php foreach ($kategoriDetay['koli'] as $index => $detay): ?>
                            <tr>
                                <td><select class="form-select koli-malzeme" name="koli_malzeme[]" data-selected="<?= $detay['malzeme_id'] ?>">
                                    <option value="">YÃ¼kleniyor...</option>
                                </select></td>
                                <td><input type="text" class="form-control" name="koli_malzeme_adi[]" value="<?= htmlspecialchars($detay['malzeme_adi']) ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_birim[]" value="<?= htmlspecialchars($detay['birim']) ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_stok[]" value="<?= $detay['stok'] ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_birim_fiyat_m2[]" value="<?= $detay['birim_fiyat_m2'] ?? 0 ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_birim_fiyat_adet[]" value="<?= $detay['birim_fiyat_adet'] ?? 0 ?>" readonly="readonly"></td>
                                <td><select class="form-select" name="koli_turu[]" onchange="koliTuruDegisti(this)">
                                    <option value="OZEL_OLCU" <?= ($detay['koli_turu'] ?? '') == 'OZEL_OLCU' ? 'selected' : '' ?>>ÖZEL ÖLÇÜ</option>
                                    <option value="Z-KARTON" <?= ($detay['koli_turu'] ?? '') == 'Z-KARTON' ? 'selected' : '' ?>>Z-KARTON</option>
                                </select></td>
                                <td><input type="number" class="form-control" name="koli_olcu[]" value="<?php 
                                    // Koli tÃ¼rÃ¼ne gÃ¶re doÄŸru alanÄ± gÃ¶ster
                                    if (($detay['koli_turu'] ?? '') == 'OZEL_OLCU') {
                                        echo $detay['rs_olcu'] ?? $detay['en_cm'] ?? 0;
                                    } else {
                                        echo $detay['rs_olcu'] ?? $detay['olcu'] ?? 0;
                                    }
                                ?>" onchange="calculateKoliRow(this)" placeholder="Ã–lÃ§Ã¼"></td>
                                <td><input type="number" class="form-control" name="koli_cm[]" value="<?php 
                                    // Koli tÃ¼rÃ¼ne gÃ¶re doÄŸru alanÄ± gÃ¶ster
                                    if (($detay['koli_turu'] ?? '') == 'OZEL_OLCU') {
                                        echo $detay['rs_metre'] ?? $detay['boy_cm'] ?? 0;
                                    } else {
                                        echo $detay['rs_metre'] ?? $detay['metre'] ?? 0;
                                    }
                                ?>" onchange="calculateKoliRow(this)" placeholder="cm"></td>
                                <td><input type="text" class="form-control" name="koli_m2[]" value="<?= $detay['rs_m2'] ?? $detay['m2'] ?? 0 ?>" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="koli_adet_miktar[]" value="<?= $detay['adet_miktar'] ?? $detay['miktar'] ?? $detay['adet'] ?? 1 ?>" onchange="calculateKoliRow(this)"></td>
                                <td><input type="text" class="form-control" name="koli_kritik_stok[]" value="<?= $detay['kritik_stok'] ?? 0 ?>" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_toplam_fiyat[]" value="<?= $detay['toplam_fiyat'] ?? $detay['maliyet'] ?? $detay['toplam_maliyet'] ?? 0 ?>" readonly="readonly"></td>
                                <td><button type="button" class="btn btn-remove" onclick="removeKoliRow(this)">Sil</button></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td><select class="form-select koli-malzeme" name="koli_malzeme[]"></select></td>
                                <td><input type="text" class="form-control" name="koli_malzeme_adi[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_birim[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_stok[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_birim_fiyat_m2[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_birim_fiyat_adet[]" readonly="readonly"></td>
                                <td><select class="form-select" name="koli_turu[]" onchange="koliTuruDegisti(this)">
                                    <option value="OZEL_OLCU">ÖZEL ÖLÇÜ</option>
                                    <option value="Z-KARTON">Z-KARTON</option>
                                </select></td>
                                <td><input type="number" class="form-control" name="koli_olcu[]" onchange="calculateKoliRow(this)"></td>
                                <td><input type="number" class="form-control" name="koli_cm[]" onchange="calculateKoliRow(this)"></td>
                                <td><input type="text" class="form-control" name="koli_m2[]" readonly="readonly"></td>
                                <td><input type="number" class="form-control" name="koli_adet_miktar[]" value="1" onchange="calculateKoliRow(this)"></td>
                                <td><input type="text" class="form-control" name="koli_kritik_stok[]" readonly="readonly"></td>
                                <td><input type="text" class="form-control" name="koli_toplam_fiyat[]" readonly="readonly"></td>
                                <td><button type="button" class="btn btn-success" onclick="addKoliRow()">Satır Ekle</button></td>
                            </tr>
                        <?php endif; ?>
                        </tbody>
                    </table>
                    <div class="mb-3">
                        <button type="button" class="btn btn-success" onclick="addKoliRow()">
                            <i class="fas fa-plus"></i> Koli Satır Ekle
                        </button>                                          
                    </div>
                </div>

              

                <div class="text-end mt-4">
                    <a href="urun_receteleri.php" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left"></i> İptal
                    </a>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Güncelle
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Maliyet Ã–zeti -->
<div class="card mt-4" id="maliyet-ozet">
    <div class="card-body">
        <h5 class="card-title">Maliyet Özeti</h5>
        <table class="table">
            <tr><th>Sunta Toplam Maliyet</th><td id="sunta-toplam-maliyet-ozet">0,00</td></tr>
            <tr><th>PVC Bant Toplam Maliyet</th><td id="pvc-toplam-maliyet-ozet">0,00</td></tr>
            <tr><th>Hırdavat Toplam Maliyet</th><td id="hirdavat-toplam-maliyet-ozet">0,00</td></tr>
            <tr><th>Koli Toplam Maliyet</th><td id="koli-toplam-maliyet-ozet">0,00</td></tr>
            <tr class="table-warning"><th>Genel Toplam Maliyet</th><td id="genel-toplam-maliyet-ozet"><b>0,00</b></td></tr>
        </table>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="recete_duzenle_fixed.js"></script>
<script>
// Form submit kontrolü
function prepareDataForSubmit() {
    console.log('🚀 Form gönderiliyor...');
    showDebugMessage('🚀 Form gönderiliyor...', 'info');
    return true;
}

// Test fonksiyonları
function testAllFunctions() {
    debugLog('🧪 Tüm fonksiyonlar test ediliyor...', 'warning');
    showDebugMessage('🧪 Tüm fonksiyonlar test ediliyor...', 'warning');

    loadUrunler();
    loadAllMalzemeler();

    setTimeout(() => {
        loadMevcutReceteVerileri();
    }, 3000);
}

function testMalzemeYukleme() {
    debugLog('🔋 Malzeme yükleme test ediliyor...', 'warning');
    showDebugMessage('🔋 Malzeme yükleme test ediliyor...', 'warning');
    loadAllMalzemeler();
}

function testUrunYukleme() {
    debugLog('🏷️ Ürün yükleme test ediliyor...', 'warning');
    showDebugMessage('🏷️ Ürün yükleme test ediliyor...', 'warning');
    loadUrunler();
}

function forceLoadMalzemeler() {
    debugLog('🚨 ZORLA malzeme yükleme...', 'error');
    showDebugMessage('🚨 ZORLA malzeme yükleme...', 'error');

    // Cache'i temizle
    if (typeof malzemelerCache !== 'undefined') {
        malzemelerCache = { sunta: [], pvc: [], hirdavat: [], koli: [] };
    }
    loadAllMalzemeler();
}

// Ürün yükleme fonksiyonu (recete_ekle.php'den kopyalandı)
function loadUrunlerFromAPI() {
    debugLog('🏷️ API\'den ürünler yükleniyor...', 'warning');
    showDebugMessage('🏷️ API\'den ürünler yükleniyor...', 'warning');

    fetch('get_urunler.php')
        .then(response => response.json())
        .then(data => {
            const urunSelect = document.getElementById('urun-select');
            if (!urunSelect) {
                debugLog('❌ urun-select elementi bulunamadı', 'error');
                return;
            }

            urunSelect.innerHTML = '<option value="">Ürün Seçiniz</option>';

            if (data.success && data.urunler) {
                // Ürünleri kategorilere ayır
                const perakendeUrunler = data.urunler.filter(u => u.urun_tipi == 'perakende');
                const toptanBilesensizUrunler = data.urunler.filter(u => u.urun_tipi == 'toptan_bilesensiz');
                const toptanBilesenliUrunler = data.urunler.filter(u => u.urun_tipi == 'toptan_bilesenli');
                const modelBilesenUrunler = data.urunler.filter(u => u.urun_tipi == 'model_bilesen');
                const digerUrunler = data.urunler.filter(u => u.urun_tipi == 'diger');

                // Perakende ürünler
                if (perakendeUrunler.length > 0) {
                    urunSelect.innerHTML += '<optgroup label="📦 PERAKENDE ÜRÜNLER">';
                    perakendeUrunler.forEach(urun => {
                        urunSelect.innerHTML += `<option value="${urun.id}">${urun.gosterim_adi || urun.stok_adi} (${urun.stok_kodu})</option>`;
                    });
                    urunSelect.innerHTML += '</optgroup>';
                }

                // Toptan bileşensiz ürünler
                if (toptanBilesensizUrunler.length > 0) {
                    urunSelect.innerHTML += '<optgroup label="🚚 TOPTAN BİLEŞENSİZ ÜRÜNLER">';
                    toptanBilesensizUrunler.forEach(urun => {
                        urunSelect.innerHTML += `<option value="${urun.id}">${urun.gosterim_adi || urun.stok_adi} (${urun.stok_kodu})</option>`;
                    });
                    urunSelect.innerHTML += '</optgroup>';
                }

                // Toptan bileşenli ürünler
                if (toptanBilesenliUrunler.length > 0) {
                    urunSelect.innerHTML += '<optgroup label="🏭 TOPTAN BİLEŞENLİ ÜRÜNLER">';
                    toptanBilesenliUrunler.forEach(urun => {
                        urunSelect.innerHTML += `<option value="${urun.id}">${urun.gosterim_adi || urun.stok_adi} (${urun.stok_kodu})</option>`;
                    });
                    urunSelect.innerHTML += '</optgroup>';
                }

                // Model bileşenleri
                if (modelBilesenUrunler.length > 0) {
                    urunSelect.innerHTML += '<optgroup label="🧩 BİLEŞENLİ ÜRÜN PARÇALARI">';
                    modelBilesenUrunler.forEach(urun => {
                        urunSelect.innerHTML += `<option value="${urun.id}">${urun.gosterim_adi || urun.stok_adi}</option>`;
                    });
                    urunSelect.innerHTML += '</optgroup>';
                }

                // Diğer ürünler
                if (digerUrunler.length > 0) {
                    urunSelect.innerHTML += '<optgroup label="📋 DİĞER ÜRÜNLER">';
                    digerUrunler.forEach(urun => {
                        urunSelect.innerHTML += `<option value="${urun.id}">${urun.gosterim_adi || urun.stok_adi} (${urun.stok_kodu})</option>`;
                    });
                    urunSelect.innerHTML += '</optgroup>';
                }

                // Mevcut ürünü seç (PHP'den gelen current_urun_id)
                const currentUrunInput = document.querySelector('input[name="current_urun_id"]');
                if (currentUrunInput && currentUrunInput.value) {
                    urunSelect.value = currentUrunInput.value;
                    debugLog(`✅ Mevcut ürün seçildi: ${currentUrunInput.value}`, 'success');
                    showDebugMessage(`✅ Mevcut ürün seçildi: ${currentUrunInput.value}`, 'success');
                }

                debugLog(`✅ ${data.urunler.length} ürün yüklendi`, 'success');
                showDebugMessage(`✅ ${data.urunler.length} ürün yüklendi`, 'success');
            } else {
                urunSelect.innerHTML = '<option value="">Ürün bulunamadı</option>';
                debugLog('❌ Ürün bulunamadı', 'error');
                showDebugMessage('❌ Ürün bulunamadı', 'error');
            }
        })
        .catch(error => {
            debugLog('❌ Ürün yükleme hatası:', 'error', error);
            showDebugMessage(`❌ Ürün yükleme hatası: ${error.message}`, 'error');
            document.getElementById('urun-select').innerHTML = '<option value="">Hata: Ürünler yüklenemedi</option>';
        });
}

// Koli hesaplama test fonksiyonu
function testKoliHesaplama() {
    debugLog('🧪 Koli hesaplama test başlıyor...', 'warning');
    showDebugMessage('🧪 Koli hesaplama test başlıyor...', 'warning');

    // Tüm koli satırlarını bul
    const koliRows = document.querySelectorAll('#koli-table tbody tr');
    debugLog(`📊 Bulunan koli satır sayısı: ${koliRows.length}`, 'info');

    koliRows.forEach((row, index) => {
        debugLog(`🔍 Koli satır ${index + 1} test ediliyor...`, 'info');

        // Satırdaki input değerlerini kontrol et
        const m2Input = row.querySelector('input[name="koli_m2[]"]');
        const birimFiyatM2Input = row.querySelector('input[name="koli_birim_fiyat_m2[]"]');
        const birimFiyatAdetInput = row.querySelector('input[name="koli_birim_fiyat_adet[]"]');
        const adetMiktarInput = row.querySelector('input[name="koli_adet_miktar[]"]');
        const toplamFiyatInput = row.querySelector('input[name="koli_toplam_fiyat[]"]');

        debugLog(`📋 Satır ${index + 1} değerleri:`, 'info', {
            m2: m2Input?.value || 'YOK',
            birimFiyatM2: birimFiyatM2Input?.value || 'YOK',
            birimFiyatAdet: birimFiyatAdetInput?.value || 'YOK',
            adetMiktar: adetMiktarInput?.value || 'YOK',
            toplamFiyat: toplamFiyatInput?.value || 'YOK'
        });

        // Hesaplamayı tetikle
        if (adetMiktarInput) {
            debugLog(`🔄 Satır ${index + 1} hesaplama tetikleniyor...`, 'info');
            calculateKoliRow(adetMiktarInput);
        }
    });

    debugLog('✅ Koli hesaplama test tamamlandı', 'success');
    showDebugMessage('✅ Koli hesaplama test tamamlandı', 'success');
}

// Koli türü değiştiğinde çağrılan fonksiyon
function koliTuruDegisti(selectElement) {
    const row = selectElement.closest('tr');
    if (!row) return;

    const koliTuru = selectElement.value;
    debugLog(`📦 Koli türü değişti: ${koliTuru}`, 'info');

    // Koli türüne göre placeholder'ları güncelle
    const olcuInput = row.querySelector('input[name="koli_olcu[]"]');
    const cmInput = row.querySelector('input[name="koli_cm[]"]');

    // Koli türü kontrolü - encoding sorunları için çoklu kontrol
    if (koliTuru === 'OZEL_OLCU' || koliTuru === 'ÖZEL ÖLÇÜ' || koliTuru === 'Ã–ZEL Ã–LÃ‡Ãœ' || koliTuru.includes('ÖZEL') || koliTuru.includes('Ã–ZEL') || koliTuru.includes('OZEL')) {
        if (olcuInput) olcuInput.placeholder = 'En (cm)';
        if (cmInput) cmInput.placeholder = 'Boy (cm)';
        debugLog('📐 Özel ölçü modu: En/Boy', 'info');
    } else {
        if (olcuInput) olcuInput.placeholder = 'Ölçü';
        if (cmInput) cmInput.placeholder = 'cm';
        debugLog('📦 Standart koli modu (Z-KARTON)', 'info');
    }

    // Hesaplamayı tetikle
    calculateKoliRow(selectElement);
    showDebugMessage(`📦 Koli türü: ${koliTuru}`, 'info');
}

// SATIR SİLME FONKSİYONLARI
function removeSuntaRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
        debugLog('🗑️ Sunta satırı silindi', 'warning');
        showDebugMessage('🗑️ Sunta satırı silindi', 'warning');
    }
}

function removePvcRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
        debugLog('🗑️ PVC satırı silindi', 'warning');
        showDebugMessage('🗑️ PVC satırı silindi', 'warning');
    }
}

function removeHirdavatRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
        debugLog('🗑️ Hırdavat satırı silindi', 'warning');
        showDebugMessage('🗑️ Hırdavat satırı silindi', 'warning');
    }
}

function removeKoliRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
        debugLog('🗑️ Koli satırı silindi', 'warning');
        showDebugMessage('🗑️ Koli satırı silindi', 'warning');
    }
}

// SATIR EKLEME FONKSİYONLARI
function addSuntaRow() {
    const tbody = document.querySelector('#sunta-table tbody');
    if (!tbody) return;

    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td><select class="form-select sunta-malzeme" name="sunta_malzeme[]"><option value="">Seçiniz...</option></select></td>
        <td><input type="text" class="form-control" name="sunta_malzeme_adi[]" readonly></td>
        <td><input type="text" class="form-control" name="sunta_birim[]" readonly></td>
        <td><input type="text" class="form-control" name="sunta_stok[]" readonly></td>
        <td><input type="text" class="form-control" name="sunta_fiyat_cm2[]" readonly></td>
        <td><input type="text" class="form-control" name="parca_no[]" onchange="calculateSuntaRow(this)"></td>
        <td><input type="text" class="form-control" name="renk[]" onchange="calculateSuntaRow(this)"></td>
        <td><input type="text" class="form-control" name="parca_ismi[]" onchange="calculateSuntaRow(this)"></td>
        <td><input type="number" class="form-control" name="adet[]" value="1" onchange="calculateSuntaRow(this)"></td>
        <td><input type="number" class="form-control" name="sunta_kesim_boy[]" step="0.1" onchange="calculateSuntaRow(this)"></td>
        <td><input type="number" class="form-control" name="sunta_kesim_en[]" step="0.1" onchange="calculateSuntaRow(this)"></td>
        <td><input type="number" class="form-control" name="toplam_cm2[]" readonly></td>
        <td><input type="number" class="form-control" name="sunta_toplam_maliyet[]" readonly></td>
        <td><button type="button" class="btn btn-remove" onclick="removeSuntaRow(this)">Sil</button></td>
    `;

    // Son satırdan önce ekle (Satır Ekle butonundan önce)
    const lastRow = tbody.querySelector('tr:last-child');
    tbody.insertBefore(newRow, lastRow);

    debugLog('➕ Yeni sunta satırı eklendi', 'success');
    showDebugMessage('➕ Yeni sunta satırı eklendi', 'success');

    // Malzemeleri yükle - setTimeout ile DOM güncellemesini bekle
    setTimeout(() => {
        loadAllMalzemeler();
    }, 100);
}

function addPvcRow() {
    const tbody = document.querySelector('#pvc-table tbody');
    if (!tbody) return;

    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td><select class="form-select pvc-malzeme" name="pvc_malzeme[]"><option value="">Seçiniz...</option></select></td>
        <td><input type="text" class="form-control" name="pvc_malzeme_adi[]" readonly></td>
        <td><input type="text" class="form-control" name="pvc_birim[]" readonly></td>
        <td><input type="text" class="form-control" name="pvc_stok[]" readonly></td>
        <td><input type="text" class="form-control" name="pvc_fiyat[]" readonly></td>
        <td><input type="text" class="form-control" name="pvc_parca_no[]" onchange="calculatePvcRow(this)"></td>
        <td><input type="text" class="form-control" name="pvc_renk[]" onchange="calculatePvcRow(this)"></td>
        <td><input type="text" class="form-control" name="pvc_parca_ismi[]" onchange="calculatePvcRow(this)"></td>
        <td><input type="number" class="form-control" name="pvc_adet[]" value="1" onchange="calculatePvcRow(this)"></td>
        <td><input type="number" class="form-control" name="pvc_bantlama_boy[]" step="0.1" onchange="calculatePvcRow(this)"></td>
        <td><input type="number" class="form-control" name="pvc_bantlama_en[]" step="0.1" onchange="calculatePvcRow(this)"></td>
        <td><input type="number" class="form-control" name="pvc_kesim_boy[]" step="0.1" onchange="calculatePvcRow(this)"></td>
        <td><input type="number" class="form-control" name="pvc_kesim_en[]" step="0.1" onchange="calculatePvcRow(this)"></td>
        <td><input type="number" class="form-control" name="pvc_toplam_metre[]" readonly></td>
        <td><input type="number" class="form-control" name="pvc_toplam_maliyet[]" readonly></td>
        <td><button type="button" class="btn btn-remove" onclick="removePvcRow(this)">Sil</button></td>
    `;

    const lastRow = tbody.querySelector('tr:last-child');
    tbody.insertBefore(newRow, lastRow);

    debugLog('➕ Yeni PVC satırı eklendi', 'success');
    showDebugMessage('➕ Yeni PVC satırı eklendi', 'success');
    setTimeout(() => {
        loadAllMalzemeler();
    }, 100);
}

function addHirdavatRow() {
    const tbody = document.querySelector('#hirdavat-table tbody');
    if (!tbody) return;

    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td><select class="form-select hirdavat-malzeme" name="hirdavat_malzeme[]"><option value="">Seçiniz...</option></select></td>
        <td><input type="text" class="form-control" name="hirdavat_malzeme_adi[]" readonly></td>
        <td><input type="text" class="form-control" name="hirdavat_birim[]" readonly></td>
        <td><input type="number" class="form-control" name="hirdavat_stok[]" readonly></td>
        <td><input type="number" class="form-control" name="hirdavat_fiyat[]" readonly></td>
        <td><input type="text" class="form-control" name="hirdavat_adi[]" onchange="calculateHirdavatRow(this)"></td>
        <td><input type="number" class="form-control" name="hirdavat_adet[]" value="1" onchange="calculateHirdavatRow(this)"></td>
        <td><input type="number" class="form-control" name="hirdavat_maliyet[]" onchange="calculateHirdavatRow(this)"></td>
        <td><input type="number" class="form-control" name="hirdavat_toplam_maliyet[]" readonly></td>
        <td><button type="button" class="btn btn-remove" onclick="removeHirdavatRow(this)">Sil</button></td>
    `;

    const lastRow = tbody.querySelector('tr:last-child');
    tbody.insertBefore(newRow, lastRow);

    debugLog('➕ Yeni hırdavat satırı eklendi', 'success');
    showDebugMessage('➕ Yeni hırdavat satırı eklendi', 'success');
    setTimeout(() => {
        loadAllMalzemeler();
    }, 100);
}

function addKoliRow() {
    const tbody = document.querySelector('#koli-table tbody');
    if (!tbody) return;

    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td><select class="form-select koli-malzeme" name="koli_malzeme[]"><option value="">Seçiniz...</option></select></td>
        <td><input type="text" class="form-control" name="koli_malzeme_adi[]" readonly></td>
        <td><input type="text" class="form-control" name="koli_birim[]" readonly></td>
        <td><input type="text" class="form-control" name="koli_stok[]" readonly></td>
        <td><input type="text" class="form-control" name="koli_birim_fiyat_m2[]" readonly></td>
        <td><input type="text" class="form-control" name="koli_birim_fiyat_adet[]" readonly></td>
        <td><select class="form-select" name="koli_turu[]" onchange="koliTuruDegisti(this)">
            <option value="OZEL_OLCU">ÖZEL ÖLÇÜ</option>
            <option value="Z-KARTON">Z-KARTON</option>
        </select></td>
        <td><input type="number" class="form-control" name="koli_olcu[]" onchange="calculateKoliRow(this)"></td>
        <td><input type="number" class="form-control" name="koli_cm[]" onchange="calculateKoliRow(this)"></td>
        <td><input type="text" class="form-control" name="koli_m2[]" readonly></td>
        <td><input type="number" class="form-control" name="koli_adet_miktar[]" value="1" onchange="calculateKoliRow(this)"></td>
        <td><input type="text" class="form-control" name="koli_kritik_stok[]" readonly></td>
        <td><input type="text" class="form-control" name="koli_toplam_fiyat[]" readonly></td>
        <td><button type="button" class="btn btn-remove" onclick="removeKoliRow(this)">Sil</button></td>
    `;

    const lastRow = tbody.querySelector('tr:last-child');
    tbody.insertBefore(newRow, lastRow);

    debugLog('➕ Yeni koli satırı eklendi', 'success');
    showDebugMessage('➕ Yeni koli satırı eklendi', 'success');
    setTimeout(() => {
        loadAllMalzemeler();
    }, 100);
}

// HESAPLAMA FONKSİYONLARI
function calculateSuntaRow(element) {
    const row = element.closest('tr');
    if (!row) return;

    const adet = parseFloat(row.querySelector('input[name="adet[]"]')?.value || 0);
    const boy = parseFloat(row.querySelector('input[name="sunta_kesim_boy[]"]')?.value || 0);
    const en = parseFloat(row.querySelector('input[name="sunta_kesim_en[]"]')?.value || 0);
    const fiyatCm2 = parseFloat(row.querySelector('input[name="sunta_fiyat_cm2[]"]')?.value || 0);

    const toplamCm2 = adet * boy * en;
    const toplamMaliyet = toplamCm2 * fiyatCm2;

    row.querySelector('input[name="toplam_cm2[]"]').value = toplamCm2.toFixed(2);
    row.querySelector('input[name="sunta_toplam_maliyet[]"]').value = toplamMaliyet.toFixed(2);

    debugLog(`📐 Sunta hesaplama: ${adet} × ${boy} × ${en} = ${toplamCm2} cm² × ${fiyatCm2} = ${toplamMaliyet.toFixed(2)} TL`, 'info');
}

function calculatePvcRow(element) {
    const row = element.closest('tr');
    if (!row) return;

    const adet = parseFloat(row.querySelector('input[name="pvc_adet[]"]')?.value || 0);
    const bantlamaBoy = parseFloat(row.querySelector('input[name="pvc_bantlama_boy[]"]')?.value || 0);
    const bantlamaEn = parseFloat(row.querySelector('input[name="pvc_bantlama_en[]"]')?.value || 0);
    const kesimBoy = parseFloat(row.querySelector('input[name="pvc_kesim_boy[]"]')?.value || 0);
    const kesimEn = parseFloat(row.querySelector('input[name="pvc_kesim_en[]"]')?.value || 0);
    const fiyat = parseFloat(row.querySelector('input[name="pvc_fiyat[]"]')?.value || 0);

    const toplamMetre = adet * ((bantlamaBoy + bantlamaEn) * 2 + kesimBoy + kesimEn) / 100;
    const toplamMaliyet = toplamMetre * fiyat;

    row.querySelector('input[name="pvc_toplam_metre[]"]').value = toplamMetre.toFixed(2);
    row.querySelector('input[name="pvc_toplam_maliyet[]"]').value = toplamMaliyet.toFixed(2);

    debugLog(`📏 PVC hesaplama: ${toplamMetre.toFixed(2)} metre × ${fiyat} = ${toplamMaliyet.toFixed(2)} TL`, 'info');
}

function calculateHirdavatRow(element) {
    const row = element.closest('tr');
    if (!row) return;

    const adet = parseFloat(row.querySelector('input[name="hirdavat_adet[]"]')?.value || 0);
    const maliyet = parseFloat(row.querySelector('input[name="hirdavat_maliyet[]"]')?.value || 0);

    const toplamMaliyet = adet * maliyet;
    row.querySelector('input[name="hirdavat_toplam_maliyet[]"]').value = toplamMaliyet.toFixed(2);

    debugLog(`🔧 Hırdavat hesaplama: ${adet} × ${maliyet} = ${toplamMaliyet.toFixed(2)} TL`, 'info');
}

// Sayfa yüklendiğinde mevcut koli satırları için koli türü ayarlarını uygula
document.addEventListener('DOMContentLoaded', function() {
    // Mevcut koli satırlarındaki select'leri bul ve koliTuruDegisti fonksiyonunu çağır
    const koliSelects = document.querySelectorAll('select[name="koli_turu[]"]');
    koliSelects.forEach(function(select) {
        // Eğer bir değer seçiliyse, koliTuruDegisti fonksiyonunu çağır
        if (select.value) {
            koliTuruDegisti(select);
        }
    });

    debugLog('📦 Sayfa yüklendi - Mevcut koli satırları için türü ayarları uygulandı', 'success');
});
</script>
</body>
</html>
