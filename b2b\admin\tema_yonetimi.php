<?php
session_start();
include '../config.php';
include 'includes/admin_check.php';

$page_title = "Tema & Tasarım Yönetimi";
$current_page = "tema";

// Tema ayarları tablosunu kontrol et ve oluştur
$check_table = "SHOW TABLES LIKE 'b2b_tema_ayarlari'";
$table_result = $conn->query($check_table);

if ($table_result->num_rows == 0) {
    $create_table = "CREATE TABLE b2b_tema_ayarlari (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ayar_adi VARCHAR(100) NOT NULL UNIQUE,
        ayar_degeri TEXT,
        aciklama TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->query($create_table);
    
    // Varsayılan tema ayarları
    $varsayilan_ayarlar = [
        ['dark_mode', '0', 'Dark mode aktif/pasif durumu'],
        ['primary_color', '#1f2937', '<PERSON> renk'],
        ['secondary_color', '#f97316', 'İkincil renk'],
        ['accent_color', '#ea580c', 'Vurgu rengi'],
        ['hero_style', 'gradient', 'Hero section stili (gradient/solid/image)'],
        ['animation_speed', 'normal', 'Animasyon hızı (slow/normal/fast)'],
        ['border_radius', 'normal', 'Köşe yuvarlama (sharp/normal/rounded)'],
        ['site_logo', '', 'Site logo dosya adı'],
        ['logo_position', 'left', 'Logo pozisyonu (left/center/right)'],
        ['logo_size', 'normal', 'Logo boyutu (small/normal/large)']
    ];
    
    foreach ($varsayilan_ayarlar as $ayar) {
        $stmt = $conn->prepare("INSERT INTO b2b_tema_ayarlari (ayar_adi, ayar_degeri, aciklama) VALUES (?, ?, ?)");
        $stmt->bind_param('sss', $ayar[0], $ayar[1], $ayar[2]);
        $stmt->execute();
    }
}

// Logo upload dizinini oluştur
$logo_dir = '../uploads/logos/';
if (!file_exists($logo_dir)) {
    mkdir($logo_dir, 0777, true);
}

// CRUD İşlemleri
if (isset($_POST['action'])) {
    if ($_POST['action'] == 'update_tema') {
        foreach ($_POST['ayarlar'] as $ayar_adi => $ayar_degeri) {
            $stmt = $conn->prepare("UPDATE b2b_tema_ayarlari SET ayar_degeri = ? WHERE ayar_adi = ?");
            $stmt->bind_param('ss', $ayar_degeri, $ayar_adi);
            $stmt->execute();
        }
        $success_message = "Tema ayarları başarıyla güncellendi!";
    }
    
    // Logo upload işlemi
    if ($_POST['action'] == 'upload_logo' && isset($_FILES['logo'])) {
        $file = $_FILES['logo'];
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
        
        if (in_array($file['type'], $allowed_types)) {
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'logo_' . time() . '.' . $extension;
            $upload_path = $logo_dir . $filename;
            
            if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                // Eski logoyu sil
                $old_logo_result = $conn->query("SELECT ayar_degeri FROM b2b_tema_ayarlari WHERE ayar_adi = 'site_logo'");
                if ($old_logo_result && $old_logo_result->num_rows > 0) {
                    $old_logo = $old_logo_result->fetch_assoc()['ayar_degeri'];
                    if (!empty($old_logo) && file_exists($logo_dir . $old_logo)) {
                        unlink($logo_dir . $old_logo);
                    }
                }
                
                // Yeni logoyu kaydet
                $stmt = $conn->prepare("UPDATE b2b_tema_ayarlari SET ayar_degeri = ? WHERE ayar_adi = 'site_logo'");
                $stmt->bind_param('s', $filename);
                $stmt->execute();
                
                $success_message = "Logo başarıyla yüklendi!";
            } else {
                $error_message = "Logo yüklenirken hata oluştu!";
            }
        } else {
            $error_message = "Geçersiz dosya formatı! Sadece JPG, PNG, GIF ve SVG dosyaları kabul edilir.";
        }
    }
    
    // Logo silme işlemi
    if ($_POST['action'] == 'delete_logo') {
        $logo_result = $conn->query("SELECT ayar_degeri FROM b2b_tema_ayarlari WHERE ayar_adi = 'site_logo'");
        if ($logo_result && $logo_result->num_rows > 0) {
            $logo = $logo_result->fetch_assoc()['ayar_degeri'];
            if (!empty($logo) && file_exists($logo_dir . $logo)) {
                unlink($logo_dir . $logo);
            }
        }
        
        $stmt = $conn->prepare("UPDATE b2b_tema_ayarlari SET ayar_degeri = '' WHERE ayar_adi = 'site_logo'");
        $stmt->execute();
        
        $success_message = "Logo başarıyla silindi!";
    }
}

// Mevcut ayarları getir
$ayarlar = [];
$sql = "SELECT * FROM b2b_tema_ayarlari ORDER BY ayar_adi";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $ayarlar[$row['ayar_adi']] = $row['ayar_degeri'];
    }
}

include 'includes/header.php';
?>

<style>
.tema-preview {
    border: 2px solid var(--gray-light);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
}

.tema-preview:hover {
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.preview-header {
    padding: 1rem;
    background: var(--primary);
    color: white;
    font-size: 0.875rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.preview-logo {
    max-height: 40px;
    max-width: 150px;
    object-fit: contain;
}

.preview-content {
    padding: 1.5rem;
    min-height: 150px;
}

.preview-hero {
    background: linear-gradient(135deg, #f97316, #ea580c);
    color: white;
    padding: 2rem;
    text-align: center;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.preview-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.preview-card {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
    border: 1px solid #e5e7eb;
}

.color-picker-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.color-display {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    border: 2px solid var(--gray-light);
    cursor: pointer;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--secondary);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.theme-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

/* Logo Yönetimi Stilleri */
.logo-upload-area {
    border: 2px dashed var(--gray-light);
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.logo-upload-area:hover {
    border-color: var(--secondary);
    background-color: rgba(249, 115, 22, 0.05);
}

.logo-upload-area.dragover {
    border-color: var(--secondary);
    background-color: rgba(249, 115, 22, 0.1);
}

.current-logo {
    max-width: 200px;
    max-height: 100px;
    object-fit: contain;
    border: 1px solid var(--gray-light);
    border-radius: 0.5rem;
    padding: 0.5rem;
    background: white;
}

.logo-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .grid-2, .grid-3 {
        grid-template-columns: 1fr;
    }
    
    .color-picker-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .logo-actions {
        flex-direction: column;
    }
}
</style>

<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-palette"></i> Tema & Tasarım Yönetimi</h1>
        <button class="btn btn-primary" onclick="document.getElementById('themeForm').submit()">
            <i class="fas fa-save"></i> Değişiklikleri Kaydet
        </button>
    </div>
</div>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<form method="POST" id="themeForm">
    <input type="hidden" name="action" value="update_tema">
    
    <div class="grid-2">
        <!-- Sol Taraf: Ayarlar -->
        <div>
            <!-- Logo Yönetimi -->
            <div class="theme-section">
                <h3><i class="fas fa-image"></i> Logo Yönetimi</h3>
                
                <?php if (!empty($ayarlar['site_logo']) && file_exists($logo_dir . $ayarlar['site_logo'])): ?>
                    <div class="form-group">
                        <label>Mevcut Logo</label>
                        <div style="text-align: center;">
                            <img src="../uploads/logos/<?php echo htmlspecialchars($ayarlar['site_logo']); ?>" 
                                 alt="Site Logo" class="current-logo">
                            <div class="logo-actions">
                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteLogo()">
                                    <i class="fas fa-trash"></i> Logoyu Sil
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="form-group">
                    <label>Logo Yükle</label>
                    <div class="logo-upload-area" onclick="document.getElementById('logoFile').click()">
                        <i class="fas fa-cloud-upload-alt fa-3x" style="color: var(--gray);"></i>
                        <p style="margin: 1rem 0 0 0; color: var(--gray);">
                            Dosya seçmek için tıklayın veya sürükleyip bırakın<br>
                            <small>JPG, PNG, GIF, SVG formatları desteklenir (Max: 2MB)</small>
                        </p>
                    </div>
                    <input type="file" id="logoFile" name="logo" accept="image/*" style="display: none;" onchange="uploadLogo()">
                </div>
                
                <div class="form-group">
                    <label>Logo Pozisyonu</label>
                    <select name="ayarlar[logo_position]" class="form-control">
                        <option value="left" <?php echo ($ayarlar['logo_position'] ?? '') == 'left' ? 'selected' : ''; ?>>Sol</option>
                        <option value="center" <?php echo ($ayarlar['logo_position'] ?? '') == 'center' ? 'selected' : ''; ?>>Orta</option>
                        <option value="right" <?php echo ($ayarlar['logo_position'] ?? '') == 'right' ? 'selected' : ''; ?>>Sağ</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Logo Boyutu</label>
                    <select name="ayarlar[logo_size]" class="form-control">
                        <option value="small" <?php echo ($ayarlar['logo_size'] ?? '') == 'small' ? 'selected' : ''; ?>>Küçük</option>
                        <option value="normal" <?php echo ($ayarlar['logo_size'] ?? '') == 'normal' ? 'selected' : ''; ?>>Normal</option>
                        <option value="large" <?php echo ($ayarlar['logo_size'] ?? '') == 'large' ? 'selected' : ''; ?>>Büyük</option>
                    </select>
                </div>
            </div>
            
            <!-- Genel Ayarlar -->
            <div class="theme-section">
                <h3><i class="fas fa-cogs"></i> Genel Ayarlar</h3>
                
                <div class="form-group">
                    <label>
                        <i class="fas fa-moon"></i> Dark Mode
                    </label>
                    <label class="switch">
                        <input type="checkbox" name="ayarlar[dark_mode]" value="1" 
                               <?php echo ($ayarlar['dark_mode'] ?? '0') == '1' ? 'checked' : ''; ?>>
                        <span class="slider"></span>
                    </label>
                    <small>Koyu tema modunu aktif/pasif yapar</small>
                </div>
                
                <div class="form-group">
                    <label>Animasyon Hızı</label>
                    <select name="ayarlar[animation_speed]" class="form-control">
                        <option value="slow" <?php echo ($ayarlar['animation_speed'] ?? '') == 'slow' ? 'selected' : ''; ?>>Yavaş</option>
                        <option value="normal" <?php echo ($ayarlar['animation_speed'] ?? '') == 'normal' ? 'selected' : ''; ?>>Normal</option>
                        <option value="fast" <?php echo ($ayarlar['animation_speed'] ?? '') == 'fast' ? 'selected' : ''; ?>>Hızlı</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Köşe Yuvarlama</label>
                    <select name="ayarlar[border_radius]" class="form-control">
                        <option value="sharp" <?php echo ($ayarlar['border_radius'] ?? '') == 'sharp' ? 'selected' : ''; ?>>Keskin</option>
                        <option value="normal" <?php echo ($ayarlar['border_radius'] ?? '') == 'normal' ? 'selected' : ''; ?>>Normal</option>
                        <option value="rounded" <?php echo ($ayarlar['border_radius'] ?? '') == 'rounded' ? 'selected' : ''; ?>>Yuvarlak</option>
                    </select>
                </div>
            </div>
            
            <!-- Renk Ayarları -->
            <div class="theme-section">
                <h3><i class="fas fa-palette"></i> Renk Ayarları</h3>
                
                <div class="form-group">
                    <label>Ana Renk (Primary)</label>
                    <div class="color-picker-group">
                        <input type="color" name="ayarlar[primary_color]" 
                               value="<?php echo htmlspecialchars($ayarlar['primary_color'] ?? '#1f2937'); ?>"
                               class="form-control" style="width: 80px;">
                        <div class="color-display" style="background-color: <?php echo htmlspecialchars($ayarlar['primary_color'] ?? '#1f2937'); ?>"></div>
                        <span><?php echo htmlspecialchars($ayarlar['primary_color'] ?? '#1f2937'); ?></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>İkincil Renk (Secondary)</label>
                    <div class="color-picker-group">
                        <input type="color" name="ayarlar[secondary_color]" 
                               value="<?php echo htmlspecialchars($ayarlar['secondary_color'] ?? '#f97316'); ?>"
                               class="form-control" style="width: 80px;">
                        <div class="color-display" style="background-color: <?php echo htmlspecialchars($ayarlar['secondary_color'] ?? '#f97316'); ?>"></div>
                        <span><?php echo htmlspecialchars($ayarlar['secondary_color'] ?? '#f97316'); ?></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Vurgu Rengi (Accent)</label>
                    <div class="color-picker-group">
                        <input type="color" name="ayarlar[accent_color]" 
                               value="<?php echo htmlspecialchars($ayarlar['accent_color'] ?? '#ea580c'); ?>"
                               class="form-control" style="width: 80px;">
                        <div class="color-display" style="background-color: <?php echo htmlspecialchars($ayarlar['accent_color'] ?? '#ea580c'); ?>"></div>
                        <span><?php echo htmlspecialchars($ayarlar['accent_color'] ?? '#ea580c'); ?></span>
                    </div>
                </div>
            </div>
            
            <!-- Hero Section Ayarları -->
            <div class="theme-section">
                <h3><i class="fas fa-image"></i> Hero Section</h3>
                
                <div class="form-group">
                    <label>Hero Stili</label>
                    <select name="ayarlar[hero_style]" class="form-control">
                        <option value="gradient" <?php echo ($ayarlar['hero_style'] ?? '') == 'gradient' ? 'selected' : ''; ?>>Gradient</option>
                        <option value="solid" <?php echo ($ayarlar['hero_style'] ?? '') == 'solid' ? 'selected' : ''; ?>>Düz Renk</option>
                        <option value="image" <?php echo ($ayarlar['hero_style'] ?? '') == 'image' ? 'selected' : ''; ?>>Arka Plan Resmi</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Sağ Taraf: Önizleme -->
        <div>
            <div class="theme-section">
                <h3><i class="fas fa-eye"></i> Canlı Önizleme</h3>
                
                <div class="tema-preview" id="themePreview">
                    <div class="preview-header">
                        <?php if (!empty($ayarlar['site_logo']) && file_exists($logo_dir . $ayarlar['site_logo'])): ?>
                            <img src="../uploads/logos/<?php echo htmlspecialchars($ayarlar['site_logo']); ?>" 
                                 alt="Logo" class="preview-logo" id="previewLogo">
                        <?php else: ?>
                            <span id="previewLogoText">Otris B2B</span>
                        <?php endif; ?>
                        <span>B2B Portal Önizleme</span>
                        <div></div>
                    </div>
                    <div class="preview-content">
                        <div class="preview-hero">
                            <h3>Premium Mobilya Koleksiyonu</h3>
                            <p>En kaliteli ürünlerle yaşam alanlarınızı dönüştürün</p>
                            <button style="background: white; color: #f97316; padding: 0.5rem 1rem; border: none; border-radius: 0.25rem;">
                                İncele
                            </button>
                        </div>
                        
                        <div class="preview-cards">
                            <div class="preview-card">
                                <i class="fas fa-truck" style="color: #f97316; font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                                <h4>Hızlı Teslimat</h4>
                                <p>Güvenli kargo</p>
                            </div>
                            <div class="preview-card">
                                <i class="fas fa-shield-alt" style="color: #f97316; font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                                <h4>Kalite Garantisi</h4>
                                <p>%100 orijinal</p>
                            </div>
                            <div class="preview-card">
                                <i class="fas fa-headset" style="color: #f97316; font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                                <h4>7/24 Destek</h4>
                                <p>Müşteri hizmetleri</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="margin-top: 1rem;">
                    <small><i class="fas fa-info-circle"></i> Değişiklikler gerçek zamanlı olarak önizlemede görünür.</small>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Logo Upload Form (Gizli) -->
<form id="logoUploadForm" method="POST" enctype="multipart/form-data" style="display: none;">
    <input type="hidden" name="action" value="upload_logo">
</form>

<!-- Logo Delete Form (Gizli) -->
<form id="logoDeleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete_logo">
</form>

<script>
// Logo upload işlemi
function uploadLogo() {
    const fileInput = document.getElementById('logoFile');
    const file = fileInput.files[0];
    
    if (file) {
        // Dosya boyutu kontrolü (2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('Dosya boyutu 2MB\'dan büyük olamaz!');
            return;
        }
        
        // Dosya tipini kontrol et
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
        if (!allowedTypes.includes(file.type)) {
            alert('Geçersiz dosya formatı! Sadece JPG, PNG, GIF ve SVG dosyaları kabul edilir.');
            return;
        }
        
        // FormData oluştur ve dosyayı upload et
        const formData = new FormData();
        formData.append('logo', file);
        formData.append('action', 'upload_logo');
        
        // Loading göster
        const uploadArea = document.querySelector('.logo-upload-area');
        uploadArea.innerHTML = '<i class="fas fa-spinner fa-spin fa-2x"></i><p>Logo yükleniyor...</p>';
        
        // Ajax ile upload
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            // Sayfayı yenile
            window.location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Logo yüklenirken hata oluştu!');
            window.location.reload();
        });
    }
}

// Logo silme işlemi
function deleteLogo() {
    if (confirm('Logoyu silmek istediğinizden emin misiniz?')) {
        document.getElementById('logoDeleteForm').submit();
    }
}

// Drag & Drop işlevi
const logoUploadArea = document.querySelector('.logo-upload-area');

logoUploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    logoUploadArea.classList.add('dragover');
});

logoUploadArea.addEventListener('dragleave', () => {
    logoUploadArea.classList.remove('dragover');
});

logoUploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    logoUploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        document.getElementById('logoFile').files = files;
        uploadLogo();
    }
});

// Canlı önizleme güncelleme
function updatePreview() {
    const primaryColor = document.querySelector('input[name="ayarlar[primary_color]"]').value;
    const secondaryColor = document.querySelector('input[name="ayarlar[secondary_color]"]').value;
    const accentColor = document.querySelector('input[name="ayarlar[accent_color]"]').value;
    const darkMode = document.querySelector('input[name="ayarlar[dark_mode]"]').checked;
    const logoPosition = document.querySelector('select[name="ayarlar[logo_position]"]').value;
    const logoSize = document.querySelector('select[name="ayarlar[logo_size]"]').value;
    
    const preview = document.getElementById('themePreview');
    const heroSection = preview.querySelector('.preview-hero');
    const cards = preview.querySelectorAll('.preview-card');
    const previewHeader = preview.querySelector('.preview-header');
    const previewLogo = document.getElementById('previewLogo');
    
    // Logo pozisyonu
    if (logoPosition === 'center') {
        previewHeader.style.justifyContent = 'center';
    } else if (logoPosition === 'right') {
        previewHeader.style.justifyContent = 'flex-end';
    } else {
        previewHeader.style.justifyContent = 'space-between';
    }
    
    // Logo boyutu
    if (previewLogo) {
        let logoHeight = '40px';
        if (logoSize === 'small') logoHeight = '30px';
        else if (logoSize === 'large') logoHeight = '50px';
        previewLogo.style.maxHeight = logoHeight;
    }
    
    // Hero section güncelle
    if (darkMode) {
        heroSection.style.background = `linear-gradient(135deg, ${primaryColor}, ${accentColor})`;
        preview.style.background = '#374151';
        preview.style.color = 'white';
        cards.forEach(card => {
            card.style.background = '#4B5563';
            card.style.color = 'white';
        });
    } else {
        heroSection.style.background = `linear-gradient(135deg, ${secondaryColor}, ${accentColor})`;
        preview.style.background = 'white';
        preview.style.color = '#111827';
        cards.forEach(card => {
            card.style.background = '#f8fafc';
            card.style.color = '#111827';
        });
    }
    
    // Renk displaylerini güncelle
    document.querySelectorAll('.color-display').forEach((display, index) => {
        const colors = [primaryColor, secondaryColor, accentColor];
        if (colors[index]) {
            display.style.backgroundColor = colors[index];
            display.nextElementSibling.textContent = colors[index];
        }
    });
}

// Event listeners
document.querySelectorAll('input[type="color"], input[type="checkbox"], select').forEach(input => {
    input.addEventListener('change', updatePreview);
});

// Sayfa yüklendiğinde önizlemeyi güncelle
document.addEventListener('DOMContentLoaded', updatePreview);
</script>

<?php include 'includes/footer.php'; ?> 