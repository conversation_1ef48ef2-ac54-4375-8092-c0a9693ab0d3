<?php
// Admin panel için ortak header dosyası
if (!isset($page_title)) {
    $page_title = "B2B Admin Paneli";
}
if (!isset($current_page)) {
    $current_page = "";
}

// Admin giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header("Location: index.php");
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];
$admin_kullanici = $_SESSION['b2b_admin_kullanici'];
$admin_ad_soyad = $_SESSION['b2b_admin_ad_soyad'] ?? $admin_kullanici;

// Logo ayarlarını veritabanından al
$site_logo = '';
$logo_position = 'left';
$logo_size = 'normal';

// Tema ayarları tablosunu kontrol et ve logo ayarlarını al
$logo_sql = "SELECT ayar_adi, ayar_degeri FROM b2b_tema_ayarlari WHERE ayar_adi IN ('site_logo', 'logo_position', 'logo_size')";
$logo_result = $conn->query($logo_sql);
if ($logo_result && $logo_result->num_rows > 0) {
    while ($row = $logo_result->fetch_assoc()) {
        switch($row['ayar_adi']) {
            case 'site_logo':
                $site_logo = $row['ayar_degeri'];
                break;
            case 'logo_position':
                $logo_position = $row['ayar_degeri'];
                break;
            case 'logo_size':
                $logo_size = $row['ayar_degeri'];
                break;
        }
    }
}

// Logo URL'sini belirle
$logo_url = '../../images/otris-logo.png'; // Varsayılan logo
if (!empty($site_logo)) {
    $logo_path = '../uploads/logos/' . $site_logo;
    
    // Dosya kontrolü için mutlak path kullan
    $absolute_check_path = __DIR__ . '/../uploads/logos/' . $site_logo;
    
    if (file_exists($absolute_check_path)) {
        $logo_url = $logo_path;
    }
    // Alternatif path kontrolü
    elseif (file_exists(dirname(__FILE__) . '/../uploads/logos/' . $site_logo)) {
        $logo_url = $logo_path;
    }
    // Son çare: doğrudan uploads klasörü kontrolü  
    elseif (file_exists('../uploads/logos/' . $site_logo)) {
        $logo_url = '../uploads/logos/' . $site_logo;
    }
}

// Logo boyutunu belirle
$logo_height = '40px';
switch($logo_size) {
    case 'small':
        $logo_height = '30px';
        break;
    case 'large':
        $logo_height = '50px';
        break;
    default:
        $logo_height = '40px';
        break;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #374151;
            --primary-dark: #1f2937;
            --secondary: #f97316;
            --accent: #ea580c;
            --dark: #111827;
            --light: #f8fafc;
            --gray: #6b7280;
            --gray-light: #e5e7eb;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%);
            min-height: 100vh;
            color: var(--dark);
            line-height: 1.6;
        }
        
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1.5rem;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            overflow: visible;
        }
        
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .mobile-menu-toggle:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary);
        }
        
        .brand {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark);
            text-decoration: none;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }
        
        .brand:hover {
            color: var(--secondary);
            transform: translateY(-1px);
        }
        
        .brand-logo {
            height: <?php echo $logo_height; ?>;
            width: auto;
            max-width: 200px;
            transition: all 0.3s ease;
            object-fit: contain;
        }
        
        .brand:hover .brand-logo {
            transform: scale(1.05);
        }
        
        .nav-links {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            flex-wrap: nowrap;
            overflow: visible;
        }
        
        .nav-links a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            padding: 0.75rem 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            white-space: nowrap;
            min-height: 2.5rem;
            box-sizing: border-box;
            font-size: 0.8rem;
            flex-shrink: 0;
        }
        
        .nav-links a:hover {
            color: var(--secondary);
            background-color: rgba(249, 115, 22, 0.1);
            transform: translateY(-1px);
        }
        
        .nav-links a.active {
            color: var(--secondary);
            background-color: rgba(249, 115, 22, 0.1);
        }
        
        .nav-icon {
            width: 14px;
            height: 14px;
            flex-shrink: 0;
        }
        
        .admin-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            flex-shrink: 0;
            margin-left: 0.5rem;
        }
        
        .admin-name {
            font-weight: 600;
            color: var(--dark);
            font-size: 0.75rem;
            text-align: center;
            white-space: nowrap;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: var(--primary);
            color: white !important;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-transform: none;
            letter-spacing: 0.3px;
            font-size: 0.75rem;
            z-index: 1;
            min-height: 2rem;
            box-sizing: border-box;
            gap: 0.25rem;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            background: var(--secondary);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(249, 115, 22, 0.3);
        }
        
        .btn-danger {
            background: var(--danger);
        }
        
        .btn-danger:hover {
            background: #dc2626;
            box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .page-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .page-title h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }
        
        .alert {
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: #065f46;
            border-color: rgba(16, 185, 129, 0.2);
        }
        
        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #991b1b;
            border-color: rgba(239, 68, 68, 0.2);
        }
        
        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #92400e;
            border-color: rgba(245, 158, 11, 0.2);
        }
        
        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            color: #1e40af;
            border-color: rgba(59, 130, 246, 0.2);
        }
        
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }
            
            .nav-links {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(10px);
                flex-direction: column;
                padding: 1rem;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                border-radius: 0 0 1rem 1rem;
                transform: translateY(-100%);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                gap: 0.5rem;
            }
            
            .nav-links.active {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }
            
            .nav-links a {
                width: 100%;
                justify-content: flex-start;
                padding: 1rem;
                border-radius: 0.75rem;
            }
            
            .admin-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
                padding: 1rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .page-header {
                padding: 1.5rem;
            }
            
            .content-card {
                padding: 1.5rem;
            }
            
            /* Mobilde Dropdown */
            .dropdown-menu {
                position: static !important;
                background: rgba(249, 115, 22, 0.05) !important;
                box-shadow: none !important;
                border: 1px solid rgba(249, 115, 22, 0.1) !important;
                margin: 0.5rem 0 !important;
                border-radius: 0.5rem !important;
                opacity: 1 !important;
                visibility: visible !important;
                transform: none !important;
                min-width: auto !important;
            }
            
            .dropdown-menu a {
                margin: 0.1rem !important;
                padding: 0.5rem 1rem !important;
            }
        }
        
        /* Dropdown Stilleri */
        .dropdown {
            position: relative;
            display: inline-block;
        }
        
        .dropdown-toggle {
            position: relative;
            padding-right: 1.5rem !important;
        }
        
        .dropdown-arrow {
            position: absolute;
            right: 0.25rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.65rem;
            transition: transform 0.3s ease;
        }
        
        .dropdown-toggle.active .dropdown-arrow {
            transform: translateY(-50%) rotate(180deg);
        }
        
        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(-10px);
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-radius: 0.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 180px;
            white-space: nowrap;
        }
        
        .dropdown.active .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0);
        }
        
        .dropdown-menu a {
            display: block;
            padding: 0.5rem 0.75rem;
            color: var(--dark);
            text-decoration: none;
            border-radius: 0.375rem;
            margin: 0.125rem;
            transition: all 0.3s ease;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .dropdown-menu a:hover {
            background: rgba(249, 115, 22, 0.1);
            color: var(--secondary);
            transform: translateX(3px);
        }
        
        .dropdown-menu a.active {
            background: rgba(249, 115, 22, 0.15);
            color: var(--secondary);
            font-weight: 600;
        }
        
        .dropdown-menu .nav-icon {
            width: 12px;
            height: 12px;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <header>
        <div class="navbar">
            <a href="dashboard.php" class="brand">
                <img src="<?php echo $logo_url; ?>" alt="Otris" class="brand-logo">
                <span>B2B Admin</span>
            </a>
            
            <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                <i class="fas fa-bars"></i>
            </button>
            
            <div class="nav-links" id="navLinks">
                <a href="dashboard.php" class="<?php echo ($current_page == 'dashboard') ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt nav-icon"></i>
                    Dashboard
                </a>
                <a href="musteriler.php" class="<?php echo ($current_page == 'musteriler') ? 'active' : ''; ?>">
                    <i class="fas fa-users nav-icon"></i>
                    Müşteriler
                </a>
                <a href="bayi_basvurulari.php" class="<?php echo ($current_page == 'bayi_basvurulari') ? 'active' : ''; ?>">
                    <i class="fas fa-user-plus nav-icon"></i>
                    Bayi Başvuruları
                </a>
                <a href="urunler.php" class="<?php echo ($current_page == 'urunler') ? 'active' : ''; ?>">
                    <i class="fas fa-box nav-icon"></i>
                    Ürünler
                </a>
                <a href="siparisler.php" class="<?php echo ($current_page == 'siparisler') ? 'active' : ''; ?>">
                    <i class="fas fa-shopping-cart nav-icon"></i>
                    Siparişler
                </a>
                <a href="kategoriler.php" class="<?php echo ($current_page == 'kategoriler') ? 'active' : ''; ?>">
                    <i class="fas fa-tags nav-icon"></i>
                    Kategoriler
                </a>
                
                <!-- Yönetim Dropdown -->
                <div class="dropdown">
                    <a href="#" class="dropdown-toggle <?php echo (in_array($current_page, ['hero_slides', 'banners', 'tema', 'ayarlar', 'bildirimler'])) ? 'active' : ''; ?>" onclick="toggleDropdown(event)">
                        <i class="fas fa-cogs nav-icon"></i>
                        Yönetim
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </a>
                    <div class="dropdown-menu">
                        <a href="hero_slide_yonetimi.php" class="<?php echo ($current_page == 'hero_slides') ? 'active' : ''; ?>">
                            <i class="fas fa-images nav-icon"></i>
                            Hero Slider
                        </a>
                        <a href="banner_yonetimi.php" class="<?php echo ($current_page == 'banners') ? 'active' : ''; ?>">
                            <i class="fas fa-image nav-icon"></i>
                            Banner Yönetimi
                        </a>
                        <a href="tema_yonetimi.php" class="<?php echo ($current_page == 'tema') ? 'active' : ''; ?>">
                            <i class="fas fa-palette nav-icon"></i>
                            Tema & Tasarım
                        </a>
                        <a href="bildirim_yonetimi.php" class="<?php echo ($current_page == 'bildirimler') ? 'active' : ''; ?>">
                            <i class="fas fa-bell nav-icon"></i>
                            Bildirimler
                        </a>
                        <a href="ayarlar.php" class="<?php echo ($current_page == 'ayarlar') ? 'active' : ''; ?>">
                            <i class="fas fa-cog nav-icon"></i>
                            Ayarlar
                        </a>
                    </div>
                </div>
                
                <!-- Modüller Dropdown -->
                <div class="dropdown">
                    <a href="#" class="dropdown-toggle <?php echo (in_array($current_page, ['ayin_urunleri'])) ? 'active' : ''; ?>" onclick="toggleDropdown(event)">
                        <i class="fas fa-puzzle-piece nav-icon"></i>
                        Modüller
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </a>
                    <div class="dropdown-menu">
                        <a href="ayin_urunleri.php" class="<?php echo ($current_page == 'ayin_urunleri') ? 'active' : ''; ?>">
                            <i class="fas fa-star nav-icon"></i>
                            Ayın Ürünleri
                        </a>
                    </div>
                </div>
                
                <a href="ssh_kayitlari.php" class="<?php echo ($current_page == 'ssh_kayitlari') ? 'active' : ''; ?>">
                    <i class="fas fa-tools nav-icon"></i>
                    SSH Kayıtları
                </a>
                
                <div class="admin-info">
                    <span class="admin-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($admin_ad_soyad); ?>
                    </span>
                    <div class="admin-actions">
                        <a href="dashboard.php?cikis=1" class="btn btn-logout" title="Çıkış Yap">
                            <i class="fas fa-sign-out-alt"></i>
                            Çıkış Yap
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <div class="container">
    
    <script>
        function toggleMobileMenu() {
            const navLinks = document.getElementById('navLinks');
            navLinks.classList.toggle('active');
        }
        
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = event.target.closest('.dropdown');
            const isActive = dropdown.classList.contains('active');
            
            // Diğer tüm dropdown'ları kapat
            document.querySelectorAll('.dropdown').forEach(d => d.classList.remove('active'));
            
            // Bu dropdown'ı aç/kapat
            if (!isActive) {
                dropdown.classList.add('active');
            }
        }
        
        // Mobil menüyü dışarı tıklandığında kapat
        document.addEventListener('click', function(event) {
            const navLinks = document.getElementById('navLinks');
            const menuToggle = document.querySelector('.mobile-menu-toggle');
            
            if (!navLinks.contains(event.target) && !menuToggle.contains(event.target)) {
                navLinks.classList.remove('active');
            }
            
            // Dropdown'ları dışarı tıklandığında kapat
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown').forEach(d => d.classList.remove('active'));
            }
        });
        
        // Ekran boyutu değişince mobil menüyü kapat
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('navLinks').classList.remove('active');
            }
        });
    </script>
</body>
</html> 