<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "Müşteri Ekle - B2B Admin";
$current_page = "musteri_ekle";

$mesaj = '';
$mesaj_tur = '';

// <PERSON><PERSON>ş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];
$admin_kullanici = $_SESSION['b2b_admin_kullanici'];
$admin_ad_soyad = $_SESSION['b2b_admin_ad_soyad'] ?? $admin_kullanici;

// Kullanıcı adı oluşturma fonksiyonu
function generateUsername($firm_name) {
    $username = strtolower(trim($firm_name));
    $username = str_replace(['ş', 'ğ', 'ü', 'ö', 'ç', 'ı'], ['s', 'g', 'u', 'o', 'c', 'i'], $username);
    $username = preg_replace('/[^a-z0-9]/', '', $username);
    $username = substr($username, 0, 15);
    
    if (strlen($username) < 3) {
        $username = 'firma' . rand(100, 999);
    }
    
    return $username;
}

function generatePassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%';
    $password = '';
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $password;
}

// Form gönderildi mi kontrol et
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['musteri_ekle'])) {
    $firma_adi = trim($_POST['firma_adi']);
    $email = trim($_POST['email']);
    $telefon = trim($_POST['telefon']);
    $adres = trim($_POST['adres']);
    $vergi_dairesi = trim($_POST['vergi_dairesi']);
    $vergi_no = trim($_POST['vergi_no']);
    $otris_yetkisi = isset($_POST['otris_yetkisi']) ? 1 : 0;
    $avmpark_yetkisi = isset($_POST['avmpark_yetkisi']) ? 1 : 0;
    
    // Validation
    if (empty($firma_adi)) {
        $mesaj = "Firma adı gereklidir.";
        $mesaj_tur = "danger";
    } elseif (empty($email)) {
        $mesaj = "E-posta adresi gereklidir.";
        $mesaj_tur = "danger";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $mesaj = "Geçersiz e-posta formatı.";
        $mesaj_tur = "danger";
    } else {
        // Email kontrolü
        $check_email = $conn->prepare("SELECT id FROM cari_firmalar WHERE email = ?");
        $check_email->bind_param('s', $email);
        $check_email->execute();
        $email_result = $check_email->get_result();
        
        if ($email_result->num_rows > 0) {
            $mesaj = "Bu e-posta adresi zaten kullanılıyor.";
            $mesaj_tur = "danger";
        } else {
            // Şifre oluştur
            $password = generatePassword(10);
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
                         // Cari firmayı ekle (admin bilgisi ile)
             $cari_sql = "INSERT INTO cari_firmalar (firm_name, email, telefon, adres, vergi_dairesi, vergi_no, b2b_aktif, otris_yetkisi, avmpark_yetkisi, ekleyen_admin_id, ekleyen_admin_adi) VALUES (?, ?, ?, ?, ?, ?, 1, ?, ?, ?, ?)";
             $cari_stmt = $conn->prepare($cari_sql);
             $cari_stmt->bind_param('ssssssiiis', $firma_adi, $email, $telefon, $adres, $vergi_dairesi, $vergi_no, $otris_yetkisi, $avmpark_yetkisi, $admin_id, $admin_ad_soyad);
            
            if ($cari_stmt->execute()) {
                $cari_id = $conn->insert_id;
                
                // Kullanıcı adı oluştur
                $base_username = generateUsername($firma_adi);
                $username = $base_username;
                $counter = 1;
                
                // Benzersiz kullanıcı adı bul
                while (true) {
                    $check_sql = "SELECT id FROM b2b_cari_giris WHERE kullanici_adi = ?";
                    $check_stmt = $conn->prepare($check_sql);
                    $check_stmt->bind_param('s', $username);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();
                    
                    if ($check_result->num_rows == 0) {
                        break;
                    }
                    
                    $username = $base_username . $counter;
                    $counter++;
                }
                
                // B2B giriş bilgilerini ekle
                $giris_sql = "INSERT INTO b2b_cari_giris (cari_id, kullanici_adi, sifre, email) VALUES (?, ?, ?, ?)";
                $giris_stmt = $conn->prepare($giris_sql);
                $giris_stmt->bind_param('isss', $cari_id, $username, $hashed_password, $email);
                
                if ($giris_stmt->execute()) {
                    $_SESSION['yeni_musteri'] = [
                        'firma_adi' => $firma_adi,
                        'kullanici_adi' => $username,
                        'sifre' => $password,
                        'email' => $email
                    ];
                    $mesaj = "Müşteri başarıyla eklendi!";
                    $mesaj_tur = "success";
                } else {
                    $mesaj = "Giriş bilgileri oluşturulurken hata oluştu: " . $conn->error;
                    $mesaj_tur = "danger";
                }
            } else {
                $mesaj = "Müşteri eklenirken hata oluştu: " . $conn->error;
                $mesaj_tur = "danger";
            }
        }
    }
}

include 'includes/header.php';
?>

<style>
    .container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .page-header {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .page-header h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 700;
    }
    
    .form-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--dark);
    }
    
    .form-control {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 1px solid var(--gray-light);
        border-radius: 0.5rem;
        font-family: inherit;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: white;
    }
    
    .form-control:focus {
        outline: none;
        border-color: var(--secondary);
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
    }
    
    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .form-check-input {
        width: 1.2rem;
        height: 1.2rem;
    }
    
    .form-check-label {
        font-weight: 500;
        color: var(--dark);
    }
    
    .btn {
        padding: 0.875rem 2rem;
        background: var(--secondary);
        color: white;
        border: none;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn:hover {
        background: var(--accent);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
    }
    
    .btn-secondary {
        background: var(--gray);
        color: white;
    }
    
    .btn-secondary:hover {
        background: var(--dark);
    }
    
    .alert {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    .alert-success {
        background-color: #f0fdf4;
        border: 1px solid #bbf7d0;
        color: #16a34a;
    }
    
    .alert-danger {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }
    
    .row {
        display: flex;
        gap: 1rem;
        margin: 0 -0.5rem;
    }
    
    .col-md-6 {
        flex: 1;
        padding: 0 0.5rem;
    }
    
    .back-link {
        margin-bottom: 2rem;
    }
    
    .credentials-card {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.2);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
    }
    
    .credentials-card h4 {
        color: var(--success);
        margin-bottom: 1rem;
    }
    
    .credential-item {
        background: white;
        padding: 0.75rem;
        border-radius: 0.5rem;
        margin-bottom: 0.5rem;
        border: 1px solid rgba(16, 185, 129, 0.1);
    }
    
    .credential-item strong {
        color: var(--dark);
    }
</style>

<div class="container">
    <div class="back-link">
        <a href="musteriler.php" class="btn btn-secondary">
            ← Müşteri Listesine Dön
        </a>
    </div>
    
    <div class="page-header">
        <h1>🏢 Yeni Müşteri Ekle</h1>
        <p>B2B sistemine yeni müşteri ekleyin</p>
    </div>
    
    <?php if (!empty($mesaj)): ?>
        <div class="alert alert-<?php echo $mesaj_tur; ?>">
            <?php echo $mesaj; ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['yeni_musteri'])): ?>
        <div class="credentials-card">
            <h4>✅ Müşteri Başarıyla Eklendi!</h4>
            <p><strong>Firma:</strong> <?php echo htmlspecialchars($_SESSION['yeni_musteri']['firma_adi']); ?></p>
            
            <h5 style="margin-top: 1rem; margin-bottom: 0.5rem;">🔐 Giriş Bilgileri:</h5>
            <div class="credential-item">
                <strong>Kullanıcı Adı:</strong> <?php echo htmlspecialchars($_SESSION['yeni_musteri']['kullanici_adi']); ?>
            </div>
            <div class="credential-item">
                <strong>Şifre:</strong> <?php echo htmlspecialchars($_SESSION['yeni_musteri']['sifre']); ?>
            </div>
            <div class="credential-item">
                <strong>E-posta:</strong> <?php echo htmlspecialchars($_SESSION['yeni_musteri']['email']); ?>
            </div>
            
            <p style="margin-top: 1rem; font-size: 0.875rem; color: var(--gray);">
                Bu bilgileri müşteriye iletin. Müşteri bu bilgilerle B2B sistemine giriş yapabilir.
            </p>
        </div>
        <?php unset($_SESSION['yeni_musteri']); ?>
    <?php endif; ?>
    
    <div class="form-card">
        <!-- Admin Bilgisi -->
        <div style="background: rgba(249, 115, 22, 0.1); border: 1px solid rgba(249, 115, 22, 0.2); border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 2rem;">
            <h4 style="margin: 0 0 0.5rem 0; color: var(--secondary); display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-user-shield"></i>
                Müşteriyi Ekleyen Admin
            </h4>
            <div style="background: white; padding: 1rem; border-radius: 0.5rem; border: 1px solid rgba(249, 115, 22, 0.1);">
                <strong>👤 Admin:</strong> <?php echo htmlspecialchars($admin_ad_soyad); ?><br>
                <strong>🆔 Admin ID:</strong> <?php echo $admin_id; ?><br>
                <strong>📅 Ekleme Tarihi:</strong> <?php echo date('d.m.Y H:i'); ?>
            </div>
        </div>
        
        <form method="post" action="musteri_ekle.php">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="firma_adi" class="form-label">Firma Adı *</label>
                        <input type="text" class="form-control" id="firma_adi" name="firma_adi" 
                               placeholder="Firma adını girin" required 
                               value="<?php echo isset($_POST['firma_adi']) ? htmlspecialchars($_POST['firma_adi']) : ''; ?>">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email" class="form-label">E-posta Adresi *</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="<EMAIL>" required 
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="telefon" class="form-label">Telefon</label>
                        <input type="tel" class="form-control" id="telefon" name="telefon" 
                               placeholder="0532 123 45 67" 
                               value="<?php echo isset($_POST['telefon']) ? htmlspecialchars($_POST['telefon']) : ''; ?>">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="vergi_no" class="form-label">Vergi No</label>
                        <input type="text" class="form-control" id="vergi_no" name="vergi_no" 
                               placeholder="1234567890" 
                               value="<?php echo isset($_POST['vergi_no']) ? htmlspecialchars($_POST['vergi_no']) : ''; ?>">
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="vergi_dairesi" class="form-label">Vergi Dairesi</label>
                <input type="text" class="form-control" id="vergi_dairesi" name="vergi_dairesi" 
                       placeholder="Vergi dairesi adı" 
                       value="<?php echo isset($_POST['vergi_dairesi']) ? htmlspecialchars($_POST['vergi_dairesi']) : ''; ?>">
            </div>
            
            <div class="form-group">
                <label for="adres" class="form-label">Adres</label>
                <textarea class="form-control" id="adres" name="adres" rows="3" 
                          placeholder="Firma adresi"><?php echo isset($_POST['adres']) ? htmlspecialchars($_POST['adres']) : ''; ?></textarea>
            </div>
            
            <div class="form-group">
                <label class="form-label">Yetkiler</label>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="otris_yetkisi" name="otris_yetkisi" 
                           <?php echo (isset($_POST['otris_yetkisi']) ? 'checked' : 'checked'); ?>>
                    <label class="form-check-label" for="otris_yetkisi">
                        Otris Yetkisi (Toptan Satış)
                    </label>
                </div>
                
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="avmpark_yetkisi" name="avmpark_yetkisi" 
                           <?php echo (isset($_POST['avmpark_yetkisi']) ? 'checked' : ''); ?>>
                    <label class="form-check-label" for="avmpark_yetkisi">
                        AVMPARK Yetkisi (Perakende Satış)
                    </label>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem;">
                <button type="submit" name="musteri_ekle" class="btn">
                    🏢 Müşteriyi Ekle
                </button>
            </div>
        </form>
    </div>
</div>

<?php include 'includes/footer.php'; ?>