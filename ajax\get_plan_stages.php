<?php
// ajax/get_plan_stages.php - Plan Aşamalarını Getir
require_once '../includes/db.php';

header('Content-Type: application/json');

if (!isset($_POST['plan_id']) || !is_numeric($_POST['plan_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz plan ID'
    ]);
    exit;
}

$plan_id = (int)$_POST['plan_id'];

try {
    // Önce plan bilgilerini alalım
    $stmt = $db->prepare("
        SELECT 
            up.id,
            up.miktar,
            up.baslangic_tarihi,
            up.bitis_tarihi as teslim_tarihi,
            up.durum as plan_durum,
            u.stok_adi,
            u.stok_kodu
        FROM uretim_planlari up
        LEFT JOIN urunler u ON up.urun_id = u.id
        WHERE up.id = ?
    ");
    $stmt->execute([$plan_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$plan) {
        echo json_encode([
            'success' => false,
            'message' => 'Plan bulunamadı'
        ]);
        exit;
    }

    // Aşamaları al (eğer varsa)
    $stmt = $db->prepare("
        SELECT 
            asama_adi,
            durum,
            notlar,
            tip,
            guncelleme_tarihi
        FROM uretim_asamalari 
        WHERE plan_id = ?
        ORDER BY id ASC
    ");
    $stmt->execute([$plan_id]);
    $asamalar = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Eğer aşama yoksa, varsayılan aşamaları oluştur
    if (empty($asamalar)) {
        // Otomasyon aşamaları
        $varsayilan_asamalar = [
            ['asama_adi' => 'Hazırlık', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
            ['asama_adi' => 'Kesim', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
            ['asama_adi' => 'Bant-Delik', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
            ['asama_adi' => 'Kalite Kontrol', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
            ['asama_adi' => 'Paket', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
            // Manuel hat aşamaları
            ['asama_adi' => 'Hazırlık', 'tip' => 'manuel', 'durum' => 'bekliyor'],
            ['asama_adi' => 'Kesim', 'tip' => 'manuel', 'durum' => 'bekliyor'],
            ['asama_adi' => 'Bant', 'tip' => 'manuel', 'durum' => 'bekliyor'],
            ['asama_adi' => 'Delik', 'tip' => 'manuel', 'durum' => 'bekliyor'],
            ['asama_adi' => 'Kalite Kontrol-Otomasyon Birleşim', 'tip' => 'manuel', 'durum' => 'bekliyor']
        ];

        foreach ($varsayilan_asamalar as $asama) {
            $stmt = $db->prepare("
                INSERT INTO uretim_asamalari (plan_id, asama_adi, durum, tip, olusturma_tarihi)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$plan_id, $asama['asama_adi'], $asama['durum'], $asama['tip']]);
        }

        // Yeni oluşturulan aşamaları tekrar al
        $stmt = $db->prepare("
            SELECT 
                asama_adi,
                durum,
                notlar,
                tip,
                guncelleme_tarihi
            FROM uretim_asamalari 
            WHERE plan_id = ?
            ORDER BY id ASC
        ");
        $stmt->execute([$plan_id]);
        $asamalar = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    echo json_encode([
        'success' => true,
        'plan' => $plan,
        'stages' => $asamalar
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Aşama bilgileri alınamadı: ' . $e->getMessage()
    ]);
}
?> 