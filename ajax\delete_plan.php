<?php
// ajax/delete_plan.php - Plan Sil
require_once '../includes/db.php';

header('Content-Type: application/json');

// JSON verisini al
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['plan_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Plan ID gerekli'
    ]);
    exit;
}

$plan_id = (int)$input['plan_id'];

if ($plan_id <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz plan ID'
    ]);
    exit;
}

try {
    $db->beginTransaction();

    // Plan var mı kontrol et
    $stmt = $db->prepare("SELECT id, durum FROM uretim_planlari WHERE id = ?");
    $stmt->execute([$plan_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$plan) {
        throw new Exception('Plan bulunamadı');
    }

    // Tamamlanmış planları silmeyi engelle (isteğe bağlı)
    if ($plan['durum'] === 'tamamlandi') {
        throw new Exception('Tamamlanmış planlar silinemez');
    }

    // İlişkili aşamaları sil
    $stmt = $db->prepare("DELETE FROM uretim_asamalari WHERE plan_id = ?");
    $stmt->execute([$plan_id]);

    // İlişkili malzeme rezervasyonlarını sil
    $stmt = $db->prepare("DELETE FROM malzeme_rezervasyonlari WHERE plan_id = ?");
    $stmt->execute([$plan_id]);

    // İlişkili vardiya atamalarını sil (eğer varsa)
    $stmt = $db->prepare("DELETE FROM vardiya_atamalari WHERE plan_id = ?");
    $stmt->execute([$plan_id]);

    // Planı sil
    $stmt = $db->prepare("DELETE FROM uretim_planlari WHERE id = ?");
    $stmt->execute([$plan_id]);

    $db->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Plan ve tüm ilişkili veriler başarıyla silindi',
        'plan_id' => $plan_id
    ]);

} catch (Exception $e) {
    $db->rollBack();
    echo json_encode([
        'success' => false,
        'message' => 'Silme hatası: ' . $e->getMessage()
    ]);
}
?> 