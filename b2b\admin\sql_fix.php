<?php
session_start();
include '../config.php';

echo "<h1>SQL Düzeltmeleri</h1>";

// 1. urunler tablosunda kategori_id kolonu var mı kontrol et
echo "<h3>1. urunler tablosunda kategori_id kolonu kontrol ediliyor...</h3>";
$check = $conn->query("SHOW COLUMNS FROM urunler LIKE 'kategori_id'");
if ($check->num_rows == 0) {
    echo "kategori_id kolonu bulunamadı, ekleniyor...<br>";
    $sql = "ALTER TABLE urunler ADD COLUMN kategori_id INT(11) NULL";
    if ($conn->query($sql)) {
        echo "✓ kategori_id kolonu eklendi<br>";
        // İndeks ekle
        $conn->query("ALTER TABLE urunler ADD INDEX (kategori_id)");
        echo "✓ kategori_id için indeks eklendi<br>";
    } else {
        echo "✗ Hata: " . $conn->error . "<br>";
    }
} else {
    echo "✓ kategori_id kolonu zaten mevcut<br>";
}

// 2. Test some data
echo "<h3>2. Test verileri kontrol ediliyor...</h3>";

// Kategori sayısı
$kategori_count = $conn->query("SELECT COUNT(*) as sayi FROM b2b_kategoriler")->fetch_assoc();
echo "B2B Kategori sayısı: " . $kategori_count['sayi'] . "<br>";

// Ürün sayısı
$urun_count = $conn->query("SELECT COUNT(*) as sayi FROM urunler")->fetch_assoc();
echo "Ürün sayısı: " . $urun_count['sayi'] . "<br>";

// Kategori atanmış ürün sayısı
$kategori_urun_count = $conn->query("SELECT COUNT(*) as sayi FROM urunler WHERE kategori_id IS NOT NULL")->fetch_assoc();
echo "Kategorisi olan ürün sayısı: " . $kategori_urun_count['sayi'] . "<br>";

// Sipariş sayısı
$siparis_count = $conn->query("SELECT COUNT(*) as sayi FROM b2b_siparisler")->fetch_assoc();
echo "Sipariş sayısı: " . $siparis_count['sayi'] . "<br>";

echo "<br><h3>✓ Tüm kontroller tamamlandı!</h3>";
echo "<a href='dashboard.php'>← Dashboard'a dön</a>";
?> 