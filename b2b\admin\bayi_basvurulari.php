<?php
session_start();
include dirname(__DIR__) . '/config.php';

// Admin kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header("Location: login.php");
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];

// Page bilgileri
$page_title = "Bayi Başvuruları - B2B Admin";
$current_page = "bayi_basvurulari";

$mesaj = '';
$mesaj_tur = '';

// Başvuru onaylama
if (isset($_POST['onayla']) && isset($_POST['basvuru_id'])) {
    $basvuru_id = intval($_POST['basvuru_id']);
    $admin_notu = trim($_POST['admin_notu']);
    
    try {
        // Başvuru bilgilerini al
        $sql = "SELECT * FROM b2b_bayi_basvurulari WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $basvuru_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $basvuru = $result->fetch_assoc();
            
            // Önce bu email ile zaten kayıtlı firma var mı kontrol et
            $email_check_sql = "SELECT id, firm_name FROM cari_firmalar WHERE email = ?";
            $email_check_stmt = $conn->prepare($email_check_sql);
            $email_check_stmt->bind_param('s', $basvuru['email']);
            $email_check_stmt->execute();
            $email_check_result = $email_check_stmt->get_result();
            
            if ($email_check_result && $email_check_result->num_rows > 0) {
                $existing_firm = $email_check_result->fetch_assoc();
                $mesaj = "Bu email adresi ({$basvuru['email']}) ile zaten '{$existing_firm['firm_name']}' firması kayıtlıdır. Aynı email ile birden fazla firma kaydedilemez.";
                $mesaj_tur = "warning";
            } else {
                // Cari firmalar tablosuna ekle (nakliye bilgileri ile)
                $cari_sql = "INSERT INTO cari_firmalar (firm_name, telefon, email, adres, vergi_dairesi, vergi_no, nakliye_firma, nakliye_telefon, sifre, b2b_aktif, otris_yetkisi) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 1)";
                $cari_stmt = $conn->prepare($cari_sql);
                
                if (!$cari_stmt) {
                    $mesaj = "Firma eklenirken hata oluştu: " . $conn->error;
                    $mesaj_tur = "danger";
                } else {
                    // Variables for bind_param (required for passing by reference)
                    $firma_adi = $basvuru['firma_adi'];
                    $telefon = $basvuru['telefon'];
                    $email = $basvuru['email'];
                    $adres = $basvuru['adres'];
                    $vergi_dairesi = $basvuru['vergi_dairesi'];
                    $vergi_no = $basvuru['vergi_no'];
                    $nakliye_firma = $basvuru['nakliye_firma'] ?? '';
                    $nakliye_telefon = $basvuru['nakliye_telefon'] ?? '';
                    $sifre = $basvuru['sifre'];
                    
                    $cari_stmt->bind_param('sssssssss', 
                        $firma_adi,
                        $telefon,
                        $email,
                        $adres,
                        $vergi_dairesi,
                        $vergi_no,
                        $nakliye_firma,
                        $nakliye_telefon,
                        $sifre
                    );
                    
                    if ($cari_stmt->execute()) {
                        $cari_id = $cari_stmt->insert_id;
                        
                        // Başvuru durumunu güncelle
                        $update_sql = "UPDATE b2b_bayi_basvurulari SET durum = 'onaylandi', admin_notu = ?, onay_tarihi = NOW(), onaylayan_admin_id = ? WHERE id = ?";
                        $update_stmt = $conn->prepare($update_sql);
                        $update_stmt->bind_param('sii', $admin_notu, $_SESSION['b2b_admin_id'], $basvuru_id);
                        $update_stmt->execute();
                        
                        // Depo adreslerini işle
                        if (!empty($basvuru['depo_adresleri'])) {
                            $depo_adresleri = json_decode($basvuru['depo_adresleri'], true);
                            if ($depo_adresleri && count($depo_adresleri) > 0) {
                                foreach ($depo_adresleri as $depo) {
                                    $depo_sql = "INSERT INTO b2b_cari_depo_adresleri (cari_id, depo_adi, depo_adresi, telefon) VALUES (?, ?, ?, ?)";
                                    $depo_stmt = $conn->prepare($depo_sql);
                                    
                                    // Variables for bind_param (required for passing by reference)
                                    $depo_adi = $depo['adi'];
                                    $depo_adresi = $depo['adres'];
                                    $depo_telefon = $depo['telefon'] ?? '';
                                    
                                    $depo_stmt->bind_param('isss', $cari_id, $depo_adi, $depo_adresi, $depo_telefon);
                                    $depo_stmt->execute();
                                }
                            }
                        }
                        
                        $mesaj = "Başvuru onaylandı! Firma sisteme eklendi. Giriş bilgileri: Email: {$basvuru['email']} (Şifre: Kullanıcının belirlediği şifre)";
                        $mesaj_tur = "success";
                    } else {
                        $mesaj = "Firma eklenirken hata oluştu: " . $cari_stmt->error;
                        $mesaj_tur = "danger";
                    }
                }
            }
        } else {
            $mesaj = "Başvuru bulunamadı.";
            $mesaj_tur = "danger";
        }
    } catch (Exception $e) {
        $mesaj = "İşlem sırasında hata oluştu: " . $e->getMessage();
        $mesaj_tur = "danger";
    }
}

// Başvuru reddetme
if (isset($_POST['reddet']) && isset($_POST['basvuru_id'])) {
    $basvuru_id = intval($_POST['basvuru_id']);
    $admin_notu = trim($_POST['admin_notu']);
    
    $sql = "UPDATE b2b_bayi_basvurulari SET durum = 'reddedildi', admin_notu = ?, onay_tarihi = NOW(), onaylayan_admin_id = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sii', $admin_notu, $_SESSION['b2b_admin_id'], $basvuru_id);
    
    if ($stmt->execute()) {
        $mesaj = "Başvuru reddedildi.";
        $mesaj_tur = "info";
    } else {
        $mesaj = "İşlem sırasında hata oluştu.";
        $mesaj_tur = "danger";
    }
}

// Başvuruları listele
$basvurular = [];
$durum_filter = $_GET['durum'] ?? 'tumu';
$where_clause = "";
if ($durum_filter !== 'tumu') {
    $where_clause = "WHERE b.durum = '$durum_filter'";
}

$sql = "SELECT b.id, b.firma_adi, b.yetkili_adi, b.telefon, b.email, b.adres, b.vergi_dairesi, b.vergi_no, b.nakliye_firma, b.nakliye_telefon, b.sifre, b.depo_adresleri, b.basvuru_tarihi, b.durum, b.admin_notu, b.onay_tarihi, b.onaylayan_admin_id, a.kullanici_adi as onaylayan_admin 
        FROM b2b_bayi_basvurulari b 
        LEFT JOIN b2b_admin a ON b.onaylayan_admin_id = a.id 
        $where_clause
        ORDER BY b.basvuru_tarihi DESC";

$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $basvurular[] = $row;
    }
}

// Header dahil et
include __DIR__ . '/includes/header.php';
?>

<style>
    .filter-tabs {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 2rem;
        background: rgba(255, 255, 255, 0.9);
        padding: 1rem;
        border-radius: 1rem;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .filter-tab {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .filter-tab.active {
        background: var(--secondary);
        color: white;
    }
    
    .filter-tab:not(.active) {
        background: rgba(107, 114, 128, 0.1);
        color: var(--gray);
    }
    
    .filter-tab:hover {
        transform: translateY(-1px);
    }
    
    .applications-table {
        width: 100%;
        border-collapse: collapse;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }
    
    .applications-table th {
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        color: white;
        font-weight: 600;
        padding: 1.25rem;
        text-align: left;
        font-size: 0.9rem;
    }
    
    .applications-table td {
        padding: 1.25rem;
        border-bottom: 1px solid var(--gray-light);
        vertical-align: top;
    }
    
    .applications-table tr:hover {
        background-color: rgba(249, 115, 22, 0.05);
    }
    
    .applications-table tr:last-child td {
        border-bottom: none;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 0.75rem;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .status-beklemede {
        background-color: rgba(245, 158, 11, 0.1);
        color: var(--warning);
        border: 1px solid rgba(245, 158, 11, 0.2);
    }
    
    .status-onaylandi {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }
    
    .status-reddedildi {
        background-color: rgba(239, 68, 68, 0.1);
        color: var(--danger);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .action-form {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        max-width: 300px;
    }
    
    .action-form input,
    .action-form textarea {
        padding: 0.5rem;
        border: 1px solid var(--gray-light);
        border-radius: 0.5rem;
        font-size: 0.85rem;
    }
    
    .action-form textarea {
        resize: vertical;
        min-height: 60px;
    }
    
    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
    }
    
    .btn-success {
        background: var(--success);
        color: white;
    }
    
    .btn-success:hover {
        background: #059669;
        transform: translateY(-1px);
    }
    
    .btn-danger {
        background: var(--danger);
        color: white;
    }
    
    .btn-danger:hover {
        background: #dc2626;
        transform: translateY(-1px);
    }
    
    .detail-text {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.9rem;
    }
    
    .company-info {
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 0.25rem;
    }
    
    .contact-info {
        font-size: 0.85rem;
        color: var(--gray);
    }
    
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: var(--gray);
        font-size: 0.9rem;
    }
    
    .stat-beklemede { color: var(--warning); }
    .stat-onaylandi { color: var(--success); }
    .stat-reddedildi { color: var(--danger); }
    .stat-toplam { color: var(--primary); }
</style>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-user-plus"></i>
            Bayi Başvuruları
        </h1>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'info-circle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<!-- İstatistikler -->
<?php
$stats = [
    'toplam' => 0,
    'beklemede' => 0,
    'onaylandi' => 0,
    'reddedildi' => 0
];

$stats_sql = "SELECT durum, COUNT(*) as sayi FROM b2b_bayi_basvurulari GROUP BY durum";
$stats_result = $conn->query($stats_sql);
if ($stats_result) {
    while ($stat = $stats_result->fetch_assoc()) {
        $stats[$stat['durum']] = $stat['sayi'];
        $stats['toplam'] += $stat['sayi'];
    }
}
?>

<div class="stats-cards">
    <div class="stat-card">
        <div class="stat-number stat-toplam"><?php echo $stats['toplam']; ?></div>
        <div class="stat-label">Toplam Başvuru</div>
    </div>
    <div class="stat-card">
        <div class="stat-number stat-beklemede"><?php echo $stats['beklemede']; ?></div>
        <div class="stat-label">Beklemede</div>
    </div>
    <div class="stat-card">
        <div class="stat-number stat-onaylandi"><?php echo $stats['onaylandi']; ?></div>
        <div class="stat-label">Onaylandı</div>
    </div>
    <div class="stat-card">
        <div class="stat-number stat-reddedildi"><?php echo $stats['reddedildi']; ?></div>
        <div class="stat-label">Reddedildi</div>
    </div>
</div>

<!-- Filtre Sekmeleri -->
<div class="filter-tabs">
    <a href="?durum=tumu" class="filter-tab <?php echo ($durum_filter == 'tumu') ? 'active' : ''; ?>">
        <i class="fas fa-list"></i>
        Tümü
    </a>
    <a href="?durum=beklemede" class="filter-tab <?php echo ($durum_filter == 'beklemede') ? 'active' : ''; ?>">
        <i class="fas fa-clock"></i>
        Beklemede
    </a>
    <a href="?durum=onaylandi" class="filter-tab <?php echo ($durum_filter == 'onaylandi') ? 'active' : ''; ?>">
        <i class="fas fa-check"></i>
        Onaylandı
    </a>
    <a href="?durum=reddedildi" class="filter-tab <?php echo ($durum_filter == 'reddedildi') ? 'active' : ''; ?>">
        <i class="fas fa-times"></i>
        Reddedildi
    </a>
</div>

<!-- Başvurular Listesi -->
<div class="content-card">
    <?php if (empty($basvurular)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--gray);">
            <i class="fas fa-user-plus" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem;">
                <?php 
                switch($durum_filter) {
                    case 'beklemede': echo 'Beklemede başvuru bulunmuyor'; break;
                    case 'onaylandi': echo 'Onaylanmış başvuru bulunmuyor'; break;
                    case 'reddedildi': echo 'Reddedilmiş başvuru bulunmuyor'; break;
                    default: echo 'Henüz başvuru bulunmuyor'; break;
                }
                ?>
            </h3>
            <p>Yeni başvurular otomatik olarak burada görünecektir.</p>
        </div>
    <?php else: ?>
        <div style="overflow-x: auto;">
            <table class="applications-table">
                <thead>
                    <tr>
                        <th>Firma Bilgileri</th>
                        <th>İletişim</th>
                        <th>Depo Bilgileri</th>
                        <th>Tarih</th>
                        <th>Durum</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($basvurular as $basvuru): ?>
                    <tr>
                        <td>
                            <div class="company-info"><?php echo htmlspecialchars($basvuru['firma_adi']); ?></div>
                            <div class="contact-info">
                                Yetkili: <?php echo htmlspecialchars($basvuru['yetkili_adi']); ?>
                                <?php if ($basvuru['vergi_no']): ?>
                                <br>VN: <?php echo htmlspecialchars($basvuru['vergi_no']); ?>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="contact-info">
                                <i class="fas fa-phone"></i> <?php echo htmlspecialchars($basvuru['telefon']); ?><br>
                                <i class="fas fa-envelope"></i> <?php echo htmlspecialchars($basvuru['email']); ?>
                                <?php if (!empty($basvuru['nakliye_firma'])): ?>
                                <br><i class="fas fa-truck"></i> <?php echo htmlspecialchars($basvuru['nakliye_firma']); ?>
                                <?php if (!empty($basvuru['nakliye_telefon'])): ?>
                                    <br><small style="color: var(--secondary); margin-left: 20px;">Tel: <?php echo htmlspecialchars($basvuru['nakliye_telefon']); ?></small>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php if (!empty($basvuru['depo_adresleri'])): ?>
                            <?php $depo_adresleri = json_decode($basvuru['depo_adresleri'], true); ?>
                            <?php if ($depo_adresleri && count($depo_adresleri) > 0): ?>
                            <div class="detail-text">
                                <strong>Depo Sayısı:</strong> <?php echo count($depo_adresleri); ?> adet
                                <div style="font-size: 0.8rem; color: var(--gray); margin-top: 0.25rem;">
                                    <?php foreach ($depo_adresleri as $index => $depo): ?>
                                        <div title="<?php echo htmlspecialchars($depo['adres']); ?>">
                                            • <?php echo htmlspecialchars($depo['adi']); ?>
                                            <?php if (!empty($depo['telefon'])): ?>
                                                <br><small style="color: var(--secondary);">Tel: <?php echo htmlspecialchars($depo['telefon']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <?php if ($index >= 1) break; // İlk 2 depoyu göster ?>
                                    <?php endforeach; ?>
                                    <?php if (count($depo_adresleri) > 2): ?>
                                        <div style="color: var(--secondary);">+<?php echo count($depo_adresleri) - 2; ?> depo daha...</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div style="font-size: 0.9rem;">
                                <?php echo date('d.m.Y', strtotime($basvuru['basvuru_tarihi'])); ?><br>
                                <span style="color: var(--gray); font-size: 0.8rem;">
                                    <?php echo date('H:i', strtotime($basvuru['basvuru_tarihi'])); ?>
                                </span>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-<?php echo $basvuru['durum']; ?>">
                                <i class="fas fa-<?php 
                                    switch($basvuru['durum']) {
                                        case 'beklemede': echo 'clock'; break;
                                        case 'onaylandi': echo 'check-circle'; break;
                                        case 'reddedildi': echo 'times-circle'; break;
                                    }
                                ?>"></i>
                                <?php 
                                    switch($basvuru['durum']) {
                                        case 'beklemede': echo 'Beklemede'; break;
                                        case 'onaylandi': echo 'Onaylandı'; break;
                                        case 'reddedildi': echo 'Reddedildi'; break;
                                    }
                                ?>
                            </span>
                            <?php if ($basvuru['durum'] != 'beklemede' && $basvuru['onaylayan_admin']): ?>
                            <div style="font-size: 0.75rem; color: var(--gray); margin-top: 0.25rem;">
                                <?php echo htmlspecialchars($basvuru['onaylayan_admin']); ?>
                            </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($basvuru['durum'] == 'beklemede'): ?>
                            <form method="post" class="action-form">
                                <input type="hidden" name="basvuru_id" value="<?php echo $basvuru['id']; ?>">
                                <textarea name="admin_notu" placeholder="Admin notu (opsiyonel)..."></textarea>
                                <div class="action-buttons">
                                    <button type="submit" name="onayla" class="btn-sm btn-success" 
                                            onclick="return confirm('Bu başvuruyu onaylamak istediğinizden emin misiniz?')">
                                        <i class="fas fa-check"></i>
                                        Onayla
                                    </button>
                                    <button type="submit" name="reddet" class="btn-sm btn-danger"
                                            onclick="return confirm('Bu başvuruyu reddetmek istediğinizden emin misiniz?')">
                                        <i class="fas fa-times"></i>
                                        Reddet
                                    </button>
                                </div>
                            </form>
                            <?php else: ?>
                            <div style="font-size: 0.85rem; color: var(--gray);">
                                <?php if ($basvuru['admin_notu']): ?>
                                    <strong>Not:</strong> <?php echo nl2br(htmlspecialchars($basvuru['admin_notu'])); ?>
                                <?php else: ?>
                                    İşlem tamamlandı
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?> 