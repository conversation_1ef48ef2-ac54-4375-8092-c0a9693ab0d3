<?php
include '../config.php';

// B2B banner tablosunu oluştur
$sql = "CREATE TABLE IF NOT EXISTS b2b_banners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    baslik VARCHAR(255) NOT NULL,
    aciklama TEXT,
    resim_url VARCHAR(500),
    link_url VARCHAR(500),
    button_text VARCHAR(100),
    sira INT DEFAULT 0,
    aktif TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ B2B Banner tablosu başarıyla oluşturuldu!<br>";
    
    // Örnek bannerlar ekle
    $ornekler = [
        [
            'baslik' => '<PERSON>zel İndirim Kampanyası',
            'aciklama' => 'Se<PERSON><PERSON> ürünlerde %30 indirim fırsatı',
            'resim_url' => 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
            'link_url' => 'urunler.php?indirim=1',
            'button_text' => 'İndirimli Ürünler',
            'sira' => 1
        ],
        [
            'baslik' => 'Yeni Koleksiyon', 
            'aciklama' => '2024 yeni sezon ürünleri şimdi mağazada',
            'resim_url' => 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
            'link_url' => 'urunler.php?yeni=1',
            'button_text' => 'Yeni Ürünler',
            'sira' => 2
        ]
    ];
    
    foreach ($ornekler as $banner) {
        $insert_sql = "INSERT INTO b2b_banners (baslik, aciklama, resim_url, link_url, button_text, sira) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param('sssssi', $banner['baslik'], $banner['aciklama'], $banner['resim_url'], $banner['link_url'], $banner['button_text'], $banner['sira']);
        $stmt->execute();
    }
    
    echo "✅ Örnek bannerlar başarıyla eklendi!<br>";
    echo '<a href="banner_yonetimi.php">Banner Yönetimine Git</a>';
    
} else {
    echo "❌ Hata: " . $conn->error;
}

$conn->close();
?> 