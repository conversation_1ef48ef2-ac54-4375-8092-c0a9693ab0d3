<?php
// ajax/update_plan_status.php - Plan Durumu Güncelle
require_once '../includes/db.php';

header('Content-Type: application/json');

// JSON verisini al
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['plan_id']) || !isset($input['durum'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Eksik parametreler'
    ]);
    exit;
}

$plan_id = (int)$input['plan_id'];
$durum = $input['durum'];
$notlar = $input['notlar'] ?? '';

// Geçerli durum kontrolü
$gecerli_durumlar = ['planlandi', 'basladi', 'devam_ediyor', 'tamamlandi', 'iptal'];
if (!in_array($durum, $gecerli_durumlar)) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz durum'
    ]);
    exit;
}

try {
    // Plan var mı kontrol et
    $stmt = $db->prepare("SELECT id FROM uretim_planlari WHERE id = ?");
    $stmt->execute([$plan_id]);
    
    if (!$stmt->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => 'Plan bulunamadı'
        ]);
        exit;
    }

    // Plan durumunu güncelle
    $stmt = $db->prepare("
        UPDATE uretim_planlari 
        SET durum = ?, notlar = CONCAT(IFNULL(notlar, ''), ?, '\n[', NOW(), '] Durum güncellendi: ', ?)
        WHERE id = ?
    ");
    $stmt->execute([$durum, $notlar ? "\n" . $notlar : '', $durum, $plan_id]);

    echo json_encode([
        'success' => true,
        'message' => 'Plan durumu başarıyla güncellendi',
        'plan_id' => $plan_id,
        'new_status' => $durum
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Güncelleme hatası: ' . $e->getMessage()
    ]);
}
?> 