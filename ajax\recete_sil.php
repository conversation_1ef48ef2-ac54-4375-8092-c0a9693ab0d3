<?php
// ajax/recete_sil.php - Reçete Silme İşlemi
require_once '../includes/db.php';

header('Content-Type: application/json');

// JSON verisini al
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz istek - ID eksik'
    ]);
    exit;
}

$recete_id = (int)$input['id'];

if ($recete_id <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz reçete ID'
    ]);
    exit;
}

try {
    // Veritabanı bağlantısını kontrol et
    if (!$db) {
        throw new Exception("Veritabanı bağlantısı yok");
    }

    // Önce reçetenin var olup olmadığını kontrol et
    $check_stmt = $db->prepare("SELECT recete_kodu FROM receteler WHERE id = ?");
    $check_stmt->execute([$recete_id]);
    $recete = $check_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$recete) {
        echo json_encode([
            'success' => false,
            'message' => 'Reçete bulunamadı'
        ]);
        exit;
    }

    // Reçetenin kullanımda olup olmadığını kontrol et
    $usage_check = $db->prepare("SELECT COUNT(*) as usage_count FROM uretim_planlari WHERE recete_id = ?");
    $usage_check->execute([$recete_id]);
    $usage = $usage_check->fetch(PDO::FETCH_ASSOC);

    if ($usage['usage_count'] > 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Bu reçete ' . $usage['usage_count'] . ' adet üretim planında kullanılıyor. Önce bu planları silin veya değiştirin.'
        ]);
        exit;
    }

    // Transaction başlat
    $db->beginTransaction();

    try {
        // Önce reçete satırlarını sil
        $delete_lines = $db->prepare("DELETE FROM recete_satirlari WHERE recete_id = ?");
        $delete_lines->execute([$recete_id]);

        // Sonra reçeteyi sil
        $delete_recipe = $db->prepare("DELETE FROM receteler WHERE id = ?");
        $delete_recipe->execute([$recete_id]);

        // Transaction commit
        $db->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Reçete başarıyla silindi',
            'deleted_recipe' => $recete['recete_kodu']
        ]);

    } catch (Exception $e) {
        // Transaction rollback
        $db->rollback();
        throw $e;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Silme hatası: ' . $e->getMessage()
    ]);
}
?> 