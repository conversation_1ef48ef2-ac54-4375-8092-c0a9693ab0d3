<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "Müşteriler - B2B Admin";
$current_page = "musteriler";

$mesaj = '';
$mesaj_tur = '';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];
$admin_kullanici = $_SESSION['b2b_admin_kullanici'];
$admin_ad_soyad = $_SESSION['b2b_admin_ad_soyad'] ?? $admin_kullanici;

// Giriş bilgileri oluşturma fonksiyonu
function generateUsername($firm_name) {
    // Firma adından kullanıcı adı oluştur
    $username = strtolower(trim($firm_name));
    $username = str_replace(['ş', 'ğ', 'ü', 'ö', 'ç', 'ı'], ['s', 'g', 'u', 'o', 'c', 'i'], $username);
    $username = preg_replace('/[^a-z0-9]/', '', $username);
    $username = substr($username, 0, 15);
    
    // Eğer çok kısa ise ID ekle
    if (strlen($username) < 3) {
        $username = 'firma' . rand(100, 999);
    }
    
    return $username;
}

function generatePassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%';
    $password = '';
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $password;
}

// Şifre yenileme işlemi
if (isset($_POST['sifre_yenile']) && isset($_POST['cari_id'])) {
    $cari_id = intval($_POST['cari_id']);
    
    // Yeni şifre oluştur
    $new_password = generatePassword(10);
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Veritabanında güncelle
    $update_sql = "UPDATE b2b_cari_giris SET sifre = ? WHERE cari_id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param('si', $hashed_password, $cari_id);
    
    if ($update_stmt->execute()) {
        // Firma adını getir
        $firma_sql = "SELECT firm_name FROM cari_firmalar WHERE id = ?";
        $firma_stmt = $conn->prepare($firma_sql);
        $firma_stmt->bind_param('i', $cari_id);
        $firma_stmt->execute();
        $firma_result = $firma_stmt->get_result();
        $firma_info = $firma_result->fetch_assoc();
        
        $_SESSION['yeni_sifre'] = [
            'sifre' => $new_password,
            'firma_adi' => $firma_info['firm_name']
        ];
        $mesaj = "Şifre başarıyla yenilendi!";
        $mesaj_tur = "success";
    } else {
        $mesaj = "Şifre yenilenirken hata oluştu: " . $conn->error;
        $mesaj_tur = "danger";
    }
}

// Giriş bilgileri oluşturma işlemi
if (isset($_POST['giris_bilgileri_olustur']) && isset($_POST['cari_id'])) {
    $cari_id = intval($_POST['cari_id']);
    
    // Cari bilgilerini getir
    $cari_sql = "SELECT firm_name, email FROM cari_firmalar WHERE id = ?";
    $cari_stmt = $conn->prepare($cari_sql);
    $cari_stmt->bind_param('i', $cari_id);
    $cari_stmt->execute();
    $cari_result = $cari_stmt->get_result();
    $cari_info = $cari_result->fetch_assoc();
    
    if ($cari_info) {
        // Kullanıcı adı oluştur
        $base_username = generateUsername($cari_info['firm_name']);
        $username = $base_username;
        $counter = 1;
        
        // Benzersiz kullanıcı adı bul
        while (true) {
            $check_sql = "SELECT id FROM b2b_cari_giris WHERE kullanici_adi = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param('s', $username);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows == 0) {
                break;
            }
            
            $username = $base_username . $counter;
            $counter++;
        }
        
        // Şifre oluştur
        $password = generatePassword(10);
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Veritabanına kaydet
        $insert_sql = "INSERT INTO b2b_cari_giris (cari_id, kullanici_adi, sifre, email) VALUES (?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param('isss', $cari_id, $username, $hashed_password, $cari_info['email']);
        
        if ($insert_stmt->execute()) {
            $_SESSION['giris_bilgileri'] = [
                'kullanici_adi' => $username,
                'sifre' => $password,
                'firma_adi' => $cari_info['firm_name']
            ];
            $mesaj = "Giriş bilgileri başarıyla oluşturuldu!";
            $mesaj_tur = "success";
        } else {
            $mesaj = "Giriş bilgileri oluşturulurken hata oluştu: " . $conn->error;
            $mesaj_tur = "danger";
        }
    }
}

// Müşteri aktiflik durumunu güncelle
if (isset($_GET['durum_guncelle']) && isset($_GET['id'])) {
    $musteri_id = intval($_GET['id']);
    $yeni_durum = isset($_GET['aktif']) && $_GET['aktif'] == 1 ? 1 : 0;
    
    // b2b_cari_ayarlar tablosuna kayıt var mı kontrol et
    $check_sql = "SELECT id FROM b2b_cari_ayarlar WHERE cari_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param('i', $musteri_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        // Güncelle - b2b_cari_ayarlar tablosuna aktif alanı yok, cari_firmalar'daki b2b_aktif'i güncelle
        $update_sql = "UPDATE cari_firmalar SET b2b_aktif = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param('ii', $yeni_durum, $musteri_id);
        if ($update_stmt->execute()) {
        $mesaj = "Müşteri durumu başarıyla güncellendi.";
        $mesaj_tur = "success";
        } else {
            $mesaj = "Müşteri durumu güncellenirken hata oluştu.";
            $mesaj_tur = "danger";
        }
    } else {
        // Yeni kayıt oluştur
        $insert_sql = "INSERT INTO b2b_cari_ayarlar (cari_id, iskonto_orani, kredi_limiti) VALUES (?, 0.00, 0.00)";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param('i', $musteri_id);
        if ($insert_stmt->execute()) {
            // Ayrıca cari_firmalar tablosunda da b2b_aktif alanını güncelle
            $cari_update_sql = "UPDATE cari_firmalar SET b2b_aktif = ? WHERE id = ?";
            $cari_update_stmt = $conn->prepare($cari_update_sql);
            $cari_update_stmt->bind_param('ii', $yeni_durum, $musteri_id);
            $cari_update_stmt->execute();
            
            $mesaj = "Müşteri B2B sistemine başarıyla eklendi.";
            $mesaj_tur = "success";
        } else {
            $mesaj = "Müşteri eklenirken hata oluştu.";
        $mesaj_tur = "danger";
        }
    }
}

// Müşterileri getir - cari_firmalar tablosundan mevcut kolonlarla
$musteriler = [];
$sql = "SELECT 
            cf.id,
            cf.firm_name,
            cf.telefon,
            cf.adres,
            cf.vergi_dairesi,
            cf.vergi_no,
            cf.email,
            cf.b2b_aktif as aktif,
            cf.ekleyen_admin_id,
            cf.ekleyen_admin_adi,
            cf.created_at as ekleme_tarihi,
            COALESCE(ca.iskonto_orani, 0) as iskonto_orani,
            COALESCE(ca.kredi_limiti, 0) as kredi_limiti,
            bg.kullanici_adi,
            bg.aktif as giris_aktif,
            bg.created_at as giris_olusturma_tarihi
        FROM cari_firmalar cf
        LEFT JOIN b2b_cari_ayarlar ca ON cf.id = ca.cari_id
        LEFT JOIN b2b_cari_giris bg ON cf.id = bg.cari_id
        ORDER BY cf.id DESC";

$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $musteriler[] = $row;
    }
}

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-users"></i>
            Müşteriler
        </h1>
        <a href="musteri_ekle.php" class="btn">
            <i class="fas fa-plus"></i>
            Yeni Müşteri Ekle
        </a>
    </div>
</div>

<?php if (isset($mesaj)): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['admin_mesaj'])): ?>
    <div class="alert alert-<?php echo $_SESSION['admin_mesaj_tipi']; ?>">
        <i class="fas fa-<?php echo $_SESSION['admin_mesaj_tipi'] == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php 
        echo $_SESSION['admin_mesaj']; 
        unset($_SESSION['admin_mesaj']);
        unset($_SESSION['admin_mesaj_tipi']);
        ?>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['giris_bilgileri'])): ?>
    <div class="alert alert-success" style="position: relative;">
        <i class="fas fa-check-circle"></i>
        <strong><?php echo htmlspecialchars($_SESSION['giris_bilgileri']['firma_adi']); ?></strong> için giriş bilgileri oluşturuldu!
        
        <div style="background: rgba(255,255,255,0.9); padding: 1rem; border-radius: 0.5rem; margin-top: 1rem; border: 2px dashed var(--success);">
            <h4 style="margin: 0 0 0.5rem 0; color: var(--dark);">📋 Müşteriye İletilecek Bilgiler:</h4>
            <div style="font-family: monospace; background: white; padding: 0.75rem; border-radius: 0.25rem; border: 1px solid #ddd;">
                <strong>🌐 Giriş Adresi:</strong> <?php echo $_SERVER['HTTP_HOST']; ?>/b2b/<br>
                <strong>👤 Kullanıcı Adı:</strong> <span id="username-copy"><?php echo htmlspecialchars($_SESSION['giris_bilgileri']['kullanici_adi']); ?></span>
                <button onclick="copyToClipboard('username-copy')" style="margin-left: 0.5rem; padding: 0.25rem 0.5rem; font-size: 0.7rem; background: var(--primary); color: white; border: none; border-radius: 0.25rem; cursor: pointer;">Kopyala</button><br>
                <strong>🔐 Şifre:</strong> <span id="password-copy"><?php echo htmlspecialchars($_SESSION['giris_bilgileri']['sifre']); ?></span>
                <button onclick="copyToClipboard('password-copy')" style="margin-left: 0.5rem; padding: 0.25rem 0.5rem; font-size: 0.7rem; background: var(--primary); color: white; border: none; border-radius: 0.25rem; cursor: pointer;">Kopyala</button>
            </div>
            <p style="margin: 0.5rem 0 0 0; font-size: 0.875rem; color: var(--gray);">
                ⚠️ Bu bilgileri güvenli bir şekilde müşteriye iletin ve bu sayfayı kapatın.
            </p>
        </div>
        
        <button onclick="this.parentElement.style.display='none'" style="position: absolute; top: 0.5rem; right: 0.5rem; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--success);">×</button>
    </div>
    
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(function() {
                // Geçici başarı mesajı göster
                const button = element.nextElementSibling;
                const originalText = button.textContent;
                button.textContent = '✓ Kopyalandı';
                button.style.background = 'var(--success)';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'var(--primary)';
                }, 2000);
            });
        }
    </script>
    
    <?php 
    unset($_SESSION['giris_bilgileri']);
    ?>
<?php endif; ?>

<?php if (isset($_SESSION['yeni_sifre'])): ?>
    <div class="alert alert-warning" style="position: relative;">
        <i class="fas fa-sync-alt"></i>
        <strong><?php echo htmlspecialchars($_SESSION['yeni_sifre']['firma_adi']); ?></strong> için şifre yenilendi!
        
        <div style="background: rgba(255,255,255,0.9); padding: 1rem; border-radius: 0.5rem; margin-top: 1rem; border: 2px dashed var(--warning);">
            <h4 style="margin: 0 0 0.5rem 0; color: var(--dark);">🔐 Yeni Şifre Bilgisi:</h4>
            <div style="font-family: monospace; background: white; padding: 0.75rem; border-radius: 0.25rem; border: 1px solid #ddd;">
                <strong>🔐 Yeni Şifre:</strong> <span id="new-password-copy"><?php echo htmlspecialchars($_SESSION['yeni_sifre']['sifre']); ?></span>
                <button onclick="copyToClipboard('new-password-copy')" style="margin-left: 0.5rem; padding: 0.25rem 0.5rem; font-size: 0.7rem; background: var(--warning); color: white; border: none; border-radius: 0.25rem; cursor: pointer;">Kopyala</button>
            </div>
            <p style="margin: 0.5rem 0 0 0; font-size: 0.875rem; color: var(--gray);">
                ⚠️ Bu yeni şifreyi müşteriye iletin. Eski şifre artık geçersizdir.
            </p>
        </div>
        
        <button onclick="this.parentElement.style.display='none'" style="position: absolute; top: 0.5rem; right: 0.5rem; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--warning);">×</button>
    </div>
    
    <?php 
    unset($_SESSION['yeni_sifre']);
    ?>
<?php endif; ?>

<div class="content-card">
    <?php if (empty($musteriler)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--gray);">
            <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem;">Henüz müşteri bulunmuyor</h3>
            <p>Yeni müşteri eklemek için yukarıdaki "Yeni Müşteri Ekle" butonunu kullanabilirsiniz.</p>
        </div>
    <?php else: ?>
    <style>
            .customers-table {
                width: 100%;
                border-collapse: collapse;
                border-radius: 0.75rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            }
            .customers-table th {
                background: linear-gradient(135deg, var(--primary), var(--primary-dark));
                color: white;
                font-weight: 600;
                padding: 1rem;
                text-align: left;
                font-size: 0.875rem;
        }
            .customers-table td {
                padding: 1rem;
                border-bottom: 1px solid var(--gray-light);
                vertical-align: middle;
        }
            .customers-table tr:hover {
                background-color: rgba(249, 115, 22, 0.05);
            }
            .customers-table tr:last-child td {
                border-bottom: none;
            }
            .status-badge {
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                font-size: 0.75rem;
                font-weight: 600;
                display: inline-flex;
            align-items: center;
                gap: 0.375rem;
        }
            .status-active {
                background-color: rgba(16, 185, 129, 0.1);
                color: var(--success);
                border: 1px solid rgba(16, 185, 129, 0.2);
            }
            .status-inactive {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger);
                border: 1px solid rgba(239, 68, 68, 0.2);
        }
            .action-links {
            display: flex;
            flex-wrap: wrap;
                gap: 0.5rem;
                align-items: center;
        }
            .action-btn {
                padding: 0.5rem 0.875rem;
                border-radius: 0.5rem;
            text-decoration: none;
                font-size: 0.8rem;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.375rem;
                transition: all 0.3s ease;
                border: 1px solid transparent;
        }
            .action-btn:hover {
                transform: translateY(-1px);
        }
            .action-view {
                background-color: rgba(59, 130, 246, 0.1);
                color: #1e40af;
                border-color: rgba(59, 130, 246, 0.2);
        }
            .action-view:hover {
                background-color: #3b82f6;
                color: white;
            }
            .action-edit {
                background-color: rgba(249, 115, 22, 0.1);
                color: var(--secondary);
                border-color: rgba(249, 115, 22, 0.2);
        }
            .action-edit:hover {
                background-color: var(--secondary);
            color: white;
            }
            .action-activate {
                background-color: rgba(16, 185, 129, 0.1);
                color: var(--success);
                border-color: rgba(16, 185, 129, 0.2);
        }
            .action-activate:hover {
                background-color: var(--success);
                color: white;
        }
            .action-deactivate {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger);
                border-color: rgba(239, 68, 68, 0.2);
        }
            .action-deactivate:hover {
                background-color: var(--danger);
                color: white;
        }
            .action-order {
                background-color: rgba(139, 69, 19, 0.1);
                color: #8B4513;
                border-color: rgba(139, 69, 19, 0.2);
            }
            .action-order:hover {
                background-color: #8B4513;
                color: white;
            }
    </style>
        
        <div style="overflow-x: auto;">
            <table class="customers-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Firma Adı</th>
                            <th>Telefon</th>
                        <th>Adres</th>
                        <th>Vergi Dairesi</th>
                            <th>Kim Ekledi</th>
                            <th>Kredi Limiti</th>
                            <th>İskonto</th>
                            <th>Giriş Bilgileri</th>
                            <th>Durum</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($musteriler as $musteri): ?>
                        <tr>
                        <td style="font-weight: 600;"><?php echo $musteri['id']; ?></td>
                        <td style="font-weight: 600; color: var(--dark);">
                            <?php echo htmlspecialchars($musteri['firm_name'] ?? ''); ?>
                        </td>
                            <td><?php echo htmlspecialchars($musteri['telefon'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars(substr($musteri['adres'] ?? '', 0, 50) . (strlen($musteri['adres'] ?? '') > 50 ? '...' : '')); ?></td>
                        <td><?php echo htmlspecialchars($musteri['vergi_dairesi'] ?? ''); ?></td>
                        <td style="font-size: 0.8rem;">
                            <?php if (!empty($musteri['ekleyen_admin_adi'])): ?>
                                <div style="font-weight: 600; color: var(--secondary);">
                                    <i class="fas fa-user-shield"></i>
                                    <?php echo htmlspecialchars($musteri['ekleyen_admin_adi']); ?>
                                </div>
                                <div style="color: var(--gray); font-size: 0.75rem;">
                                    <?php echo isset($musteri['ekleme_tarihi']) ? date('d.m.Y H:i', strtotime($musteri['ekleme_tarihi'])) : '-'; ?>
                                </div>
                            <?php else: ?>
                                <span style="color: var(--gray); font-style: italic;">
                                    <i class="fas fa-question-circle"></i>
                                    Bilinmiyor
                                </span>
                            <?php endif; ?>
                        </td>
                        <td style="font-weight: 600;">
                            <?php echo isset($musteri['kredi_limiti']) ? number_format($musteri['kredi_limiti'], 2, ',', '.') . ' ₺' : '-'; ?>
                        </td>
                        <td style="font-weight: 600;">
                            <?php echo isset($musteri['iskonto_orani']) ? '%' . $musteri['iskonto_orani'] : '-'; ?>
                        </td>
                        <td>
                            <?php if (!empty($musteri['kullanici_adi'])): ?>
                                <div style="font-size: 0.8rem; margin-bottom: 0.5rem;">
                                    <strong>K.Adı:</strong> <?php echo htmlspecialchars($musteri['kullanici_adi']); ?><br>
                                    <span style="color: var(--success);">✓ Oluşturulmuş</span>
                                </div>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="cari_id" value="<?php echo $musteri['id']; ?>">
                                    <button type="submit" name="sifre_yenile" 
                                            class="action-btn" 
                                            style="background-color: rgba(249, 115, 22, 0.1); color: var(--secondary); border-color: rgba(249, 115, 22, 0.2); font-size: 0.7rem; padding: 0.25rem 0.4rem;"
                                            onclick="return confirm('Bu müşterinin şifresi yenilensin mi?')">
                                        <i class="fas fa-sync-alt"></i>
                                        Şifre Yenile
                                    </button>
                                </form>
                            <?php else: ?>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="cari_id" value="<?php echo $musteri['id']; ?>">
                                    <button type="submit" name="giris_bilgileri_olustur" 
                                            class="action-btn" 
                                            style="background-color: rgba(16, 185, 129, 0.1); color: var(--success); border-color: rgba(16, 185, 129, 0.2); font-size: 0.75rem; padding: 0.375rem 0.5rem;"
                                            onclick="return confirm('Bu müşteri için giriş bilgileri oluşturulsun mu?')">
                                        <i class="fas fa-key"></i>
                                        Oluştur
                                    </button>
                                </form>
                            <?php endif; ?>
                        </td>
                            <td>
                                <?php if (isset($musteri['aktif'])): ?>
                                <span class="status-badge <?php echo $musteri['aktif'] ? 'status-active' : 'status-inactive'; ?>">
                                    <i class="fas fa-<?php echo $musteri['aktif'] ? 'check-circle' : 'times-circle'; ?>"></i>
                                        <?php echo $musteri['aktif'] ? 'Aktif' : 'Pasif'; ?>
                                    </span>
                                <?php else: ?>
                                <span class="status-badge status-inactive">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    B2B Kaydı Yok
                                </span>
                                <?php endif; ?>
                            </td>
                        <td>
                            <div class="action-links">
                                <a href="cari_detay.php?id=<?php echo $musteri['id']; ?>" class="action-btn action-view" title="Cari Detay">
                                    <i class="fas fa-chart-line"></i>
                                    Cari Detay
                                </a>
                                <a href="musteri_duzenle.php?id=<?php echo $musteri['id']; ?>" class="action-btn action-edit" title="Düzenle">
                                    <i class="fas fa-edit"></i>
                                    Düzenle
                                </a>
                                <?php if (isset($musteri['aktif']) && $musteri['aktif']): ?>
                                    <a href="musteri_giris.php?musteri_id=<?php echo $musteri['id']; ?>" class="action-btn action-order" title="Sipariş Gir">
                                        <i class="fas fa-shopping-cart"></i>
                                        Sipariş Gir
                                    </a>
                                <?php endif; ?>
                                <?php if (isset($musteri['aktif'])): ?>
                                    <?php if ($musteri['aktif']): ?>
                                        <a href="musteriler.php?durum_guncelle=1&id=<?php echo $musteri['id']; ?>&aktif=0" 
                                           class="action-btn action-deactivate" 
                                           onclick="return confirm('Bu müşteriyi pasif duruma almak istediğinize emin misiniz?')"
                                           title="Pasif Yap">
                                            <i class="fas fa-times"></i>
                                            Pasif Yap
                                        </a>
                                    <?php else: ?>
                                        <a href="musteriler.php?durum_guncelle=1&id=<?php echo $musteri['id']; ?>&aktif=1" 
                                           class="action-btn action-activate"
                                           title="Aktif Yap">
                                            <i class="fas fa-check"></i>
                                            Aktif Yap
                                        </a>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <a href="musteriler.php?durum_guncelle=1&id=<?php echo $musteri['id']; ?>&aktif=1" 
                                       class="action-btn action-activate"
                                       title="B2B'ye Ekle">
                                        <i class="fas fa-plus"></i>
                                        B2B'ye Ekle
                                    </a>
                                <?php endif; ?>
                            </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
        </div>
    <?php endif; ?>
    </div>

<?php include 'includes/footer.php'; ?> 