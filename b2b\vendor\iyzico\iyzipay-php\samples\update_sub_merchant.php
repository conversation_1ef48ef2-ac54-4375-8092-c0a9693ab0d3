<?php

require_once('config.php');

function update_personal_sub_merchant()
{
    # create request class
    $request = new \Iyzipay\Request\UpdateSubMerchantRequest();
    $request->setLocale(\Iyzipay\Model\Locale::TR);
    $request->setConversationId("*********");
    $request->setSubMerchantKey("sub merchant key");
    $request->setIban("**************************");
    $request->setAddress("Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1");
    $request->setContactName("Jane");
    $request->setContactSurname("Doe");
    $request->setEmail("<EMAIL>");
    $request->setGsmNumber("+905350000000");
    $request->setName("Jane's market");
    $request->setIdentityNumber("31300864726");
    $request->setCurrency(\Iyzipay\Model\Currency::TL);

    # make request
    $subMerchant = \Iyzipay\Model\SubMerchant::update($request, Config::options());

    # print result
    print_r($subMerchant);
}

function update_private_sub_merchant()
{
    # create request class
    $request = new \Iyzipay\Request\UpdateSubMerchantRequest();
    $request->setLocale(\Iyzipay\Model\Locale::TR);
    $request->setConversationId("*********");
    $request->setSubMerchantKey("sub merchant key");
    $request->setAddress("Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1");
    $request->setTaxOffice("Tax office");
    $request->setLegalCompanyTitle("Jane Doe inc");
    $request->setEmail("<EMAIL>");
    $request->setGsmNumber("+905350000000");
    $request->setName("Jane's market");
    $request->setIban("**************************");
    $request->setIdentityNumber("31300864726");
    $request->setCurrency(\Iyzipay\Model\Currency::TL);

    # make request
    $subMerchant = \Iyzipay\Model\SubMerchant::update($request, Config::options());

    # print result
    print_r($subMerchant);
}

function update_limited_company_sub_merchant()
{
    # create request class
    $request = new \Iyzipay\Request\UpdateSubMerchantRequest();
    $request->setLocale(\Iyzipay\Model\Locale::TR);
    $request->setConversationId("*********");
    $request->setSubMerchantKey("sub merchant key");
    $request->setAddress("Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1");
    $request->setTaxOffice("Tax office");
    $request->setTaxNumber("9261877");
    $request->setLegalCompanyTitle("ABC inc");
    $request->setEmail("<EMAIL>");
    $request->setGsmNumber("+905350000000");
    $request->setName("Jane's market");
    $request->setIban("**************************");
    $request->setCurrency(\Iyzipay\Model\Currency::TL);

    # make request
    $subMerchant = \Iyzipay\Model\SubMerchant::update($request, Config::options());

    # print result
    print_r($subMerchant);
}