<?php
// Hata raporlamayı aç (geliştirme için)
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

include __DIR__ . '/../config.php';

// Page bilgileri
$page_title = "Dashboard - B2B Admin";
$current_page = "dashboard";

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];
$admin_kullanici = $_SESSION['b2b_admin_kullanici'];
$admin_ad_soyad = $_SESSION['b2b_admin_ad_soyad'] ?? $admin_kullanici;
$admin_yetki = $_SESSION['b2b_admin_yetki'] ?? 1;

// İstatistikler
$urun_sayisi = 0;
$result = $conn->query("SELECT COUNT(*) as sayi FROM urunler");
if ($result && $row = $result->fetch_assoc()) {
    $urun_sayisi = $row['sayi'];
}

$musteri_sayisi = 0;
if ($conn->query("SHOW TABLES LIKE 'cari_firmalar'")->num_rows > 0) {
    $result = $conn->query("SELECT COUNT(*) as sayi FROM cari_firmalar");
    if ($result && $row = $result->fetch_assoc()) {
        $musteri_sayisi = $row['sayi'];
    }
}

$siparis_sayisi = 0;
if ($conn->query("SHOW TABLES LIKE 'b2b_siparisler'")->num_rows > 0) {
    $result = $conn->query("SELECT COUNT(*) as sayi FROM b2b_siparisler");
    if ($result && $row = $result->fetch_assoc()) {
        $siparis_sayisi = $row['sayi'];
    }
}

$kategori_sayisi = 0;
if ($conn->query("SHOW TABLES LIKE 'b2b_kategoriler'")->num_rows > 0) {
    $result = $conn->query("SELECT COUNT(*) as sayi FROM b2b_kategoriler");
    if ($result && $row = $result->fetch_assoc()) {
        $kategori_sayisi = $row['sayi'];
    }
}

$ssh_sayisi = 0;
if ($conn->query("SHOW TABLES LIKE 'b2b_ssh_kayitlari'")->num_rows > 0) {
    $result = $conn->query("SELECT COUNT(*) as sayi FROM b2b_ssh_kayitlari");
    if ($result && $row = $result->fetch_assoc()) {
        $ssh_sayisi = $row['sayi'];
    }
}

$bayi_basvuru_sayisi = 0;
if ($conn->query("SHOW TABLES LIKE 'b2b_bayi_basvurulari'")->num_rows > 0) {
    $result = $conn->query("SELECT COUNT(*) as sayi FROM b2b_bayi_basvurulari");
    if ($result && $row = $result->fetch_assoc()) {
        $bayi_basvuru_sayisi = $row['sayi'];
    }
}

// Çıkış işlemi
if (isset($_GET['cikis'])) {
    session_unset();
    session_destroy();
    header('Location: index.php');
    exit;
}

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </h1>
        <div style="display: flex; gap: 1rem;">
            <a href="../index.php" target="_blank" class="btn">
                <i class="fas fa-external-link-alt"></i>
                B2B Sitesini Görüntüle
            </a>
        </div>
    </div>
</div>

<!-- İstatistik Kartları -->
<style>
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    @media (max-width: 1400px) {
        .stats-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    @media (max-width: 900px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 600px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
    
    .stats-card {
        padding: 1.5rem 1rem;
        border-radius: 0.75rem;
        text-align: center;
        color: white;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card .icon {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        opacity: 0.9;
    }
    
    .stats-card .number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    .stats-card .title {
        margin-bottom: 0.75rem;
        font-size: 0.9rem;
        font-weight: 600;
        opacity: 0.95;
    }
    
    .stats-card .link {
        color: rgba(255,255,255,0.9);
        text-decoration: none;
        font-weight: 600;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        transition: opacity 0.3s ease;
    }
    
    .stats-card .link:hover {
        opacity: 1;
        color: white;
    }
</style>

<div class="stats-grid">
    <div class="stats-card" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
        <div class="icon">
            <i class="fas fa-box"></i>
        </div>
        <div class="number">
            <?php echo $urun_sayisi; ?>
        </div>
        <h3 class="title">Toplam Ürün</h3>
        <a href="urunler.php" class="link">
            <span>Ürünleri Yönet</span>
            <i class="fas fa-arrow-right"></i>
        </a>
    </div>

    <div class="stats-card" style="background: linear-gradient(135deg, var(--success), #059669);">
        <div class="icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="number">
            <?php echo $musteri_sayisi; ?>
        </div>
        <h3 class="title">Toplam Müşteri</h3>
        <a href="musteriler.php" class="link">
            <span>Müşterileri Yönet</span>
            <i class="fas fa-arrow-right"></i>
        </a>
    </div>

    <div class="stats-card" style="background: linear-gradient(135deg, var(--secondary), var(--accent));">
        <div class="icon">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <div class="number">
            <?php echo $siparis_sayisi; ?>
        </div>
        <h3 class="title">Toplam Sipariş</h3>
        <a href="siparisler.php" class="link">
            <span>Siparişleri Yönet</span>
            <i class="fas fa-arrow-right"></i>
        </a>
    </div>

    <div class="stats-card" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
        <div class="icon">
            <i class="fas fa-tags"></i>
        </div>
        <div class="number">
            <?php echo $kategori_sayisi; ?>
        </div>
        <h3 class="title">Kategori Sayısı</h3>
        <a href="kategoriler.php" class="link">
            <span>Kategorileri Yönet</span>
            <i class="fas fa-arrow-right"></i>
        </a>
    </div>

    <div class="stats-card" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
        <div class="icon">
            <i class="fas fa-user-plus"></i>
        </div>
        <div class="number">
            <?php echo $bayi_basvuru_sayisi; ?>
        </div>
        <h3 class="title">Bayi Başvuruları</h3>
        <a href="bayi_basvurulari.php" class="link">
            <span>Başvuruları Yönet</span>
            <i class="fas fa-arrow-right"></i>
        </a>
    </div>

    <div class="stats-card" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
        <div class="icon">
            <i class="fas fa-tools"></i>
        </div>
        <div class="number">
            <?php echo $ssh_sayisi; ?>
        </div>
        <h3 class="title">SSH Kayıtları</h3>
        <a href="ssh_kayitlari.php" class="link">
            <span>SSH Kayıtlarını Yönet</span>
            <i class="fas fa-arrow-right"></i>
        </a>
    </div>
</div>

<!-- Hızlı Erişim Menüsü -->
<div class="content-card">
    <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-rocket"></i>
        Hızlı Erişim
    </h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <a href="urunler.php" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(59, 130, 246, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#3b82f6'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-box" style="font-size: 2rem; color: #3b82f6; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">Ürün Yönetimi</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Ürün ekleme ve düzenleme</p>
            </div>
        </a>
        
        <a href="musteriler.php" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(16, 185, 129, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#10b981'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-users" style="font-size: 2rem; color: #10b981; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">Müşteri Yönetimi</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Müşteri bilgileri ve ayarları</p>
            </div>
        </a>
        
        <a href="siparisler.php" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(249, 115, 22, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#f97316'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-shopping-cart" style="font-size: 2rem; color: #f97316; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">Sipariş Takibi</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Sipariş durumu ve onayları</p>
            </div>
        </a>
        
        <a href="kategoriler.php" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(139, 92, 246, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#8b5cf6'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-tags" style="font-size: 2rem; color: #8b5cf6; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">Kategori Yönetimi</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Kategori düzenleme ve organizasyon</p>
            </div>
        </a>
        
        <a href="ayarlar.php" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(107, 114, 128, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#6b7280'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-cog" style="font-size: 2rem; color: #6b7280; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">Sistem Ayarları</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Genel sistem konfigürasyonu</p>
            </div>
        </a>
        
        <a href="admin_yonetimi.php" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(147, 51, 234, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#9333ea'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-user-shield" style="font-size: 2rem; color: #9333ea; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">Admin Yönetimi</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Alt kullanıcı ekleme ve yetkilendirme</p>
            </div>
        </a>
        
        <a href="ssh_kayitlari.php" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(239, 68, 68, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#ef4444'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-tools" style="font-size: 2rem; color: #ef4444; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">SSH Kayıtları</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Satış sonrası hizmet takibi</p>
            </div>
        </a>
        
        <a href="banner_yonetimi.php" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(245, 158, 11, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#f59e0b'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-images" style="font-size: 2rem; color: #f59e0b; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">Banner Yönetimi</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Ana sayfa banner düzenleme</p>
            </div>
        </a>
        
        <a href="../index.php" target="_blank" style="text-decoration: none; color: inherit;">
            <div style="background: rgba(16, 185, 129, 0.1); padding: 1.5rem; border-radius: 0.75rem; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;" 
                 onmouseover="this.style.borderColor='#10b981'; this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.borderColor='transparent'; this.style.transform='translateY(0)'">
                <i class="fas fa-external-link-alt" style="font-size: 2rem; color: #10b981; margin-bottom: 0.5rem;"></i>
                <h4 style="margin: 0; color: var(--dark);">B2B Sitesi</h4>
                <p style="margin: 0.5rem 0 0 0; color: var(--gray); font-size: 0.875rem;">Müşteri arayüzünü görüntüle</p>
            </div>
        </a>
    </div>
</div>

<!-- Son İşlemler -->
<div class="content-card">
    <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-clock"></i>
        Son İşlemler
    </h3>
    <div style="text-align: center; padding: 2rem; color: var(--gray);">
        <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
        <p>Henüz bir işlem kaydı bulunmuyor.</p>
        <p style="font-size: 0.875rem;">İşlem geçmişi burada görüntülenecek.</p>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 