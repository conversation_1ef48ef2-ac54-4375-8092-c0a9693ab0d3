<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "Siparişler - B2B Admin";
$current_page = "siparisler";

$mesaj = '';
$mesaj_tur = '';

// Sipariş durumu güncelleme
if (isset($_POST['siparis_durum_guncelle'])) {
    $siparis_id = intval($_POST['siparis_id']);
    $yeni_durum = $_POST['yeni_durum'];
    
    $sql = "UPDATE b2b_siparisler SET durum = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('si', $yeni_durum, $siparis_id);
    
    if ($stmt->execute()) {
        $mesaj = "Sipariş durumu başarıyla güncellendi.";
        $mesaj_tur = "success";
    } else {
        $mesaj = "Sipariş durumu güncellenirken hata oluştu.";
        $mesaj_tur = "danger";
    }
}

// Siparişleri listele - cari_firmalar tablosunda firm_name kullan
$siparisler = [];
$sql = "SELECT s.*, c.firm_name as musteri_adi,
               GROUP_CONCAT(DISTINCT u.renk ORDER BY u.renk SEPARATOR ', ') as renkler,
               GROUP_CONCAT(DISTINCT p.takip_kodu ORDER BY p.takip_kodu SEPARATOR ', ') as takip_kodlari
        FROM b2b_siparisler s
        LEFT JOIN cari_firmalar c ON s.cari_id = c.id
        LEFT JOIN b2b_siparis_detaylari sd ON s.id = sd.siparis_id
        LEFT JOIN urunler u ON sd.urun_id = u.id
        LEFT JOIN paketler p ON p.urun_id = u.id AND p.durum = 'stokta'
        GROUP BY s.id
        ORDER BY s.siparis_tarihi DESC";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $siparisler[] = $row;
    }
}

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-shopping-cart"></i>
            Siparişler
        </h1>
        <a href="siparis_ekle.php" class="btn">
            <i class="fas fa-plus"></i>
            Yeni Sipariş
        </a>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <?php if (empty($siparisler)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--gray);">
            <i class="fas fa-shopping-cart" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem;">Henüz sipariş bulunmuyor</h3>
            <p>Yeni sipariş eklemek için yukarıdaki "Yeni Sipariş" butonunu kullanabilirsiniz.</p>
        </div>
    <?php else: ?>
        <style>
            .orders-table {
                width: 100%;
                border-collapse: collapse;
                border-radius: 0.75rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            }
            .orders-table th {
                background: linear-gradient(135deg, var(--primary), var(--primary-dark));
                color: white;
                font-weight: 600;
                padding: 1rem;
                text-align: left;
                font-size: 0.875rem;
            }
            .orders-table td {
                padding: 1rem;
                border-bottom: 1px solid var(--gray-light);
                vertical-align: middle;
            }
            .orders-table tr:hover {
                background-color: rgba(249, 115, 22, 0.05);
            }
            .orders-table tr:last-child td {
                border-bottom: none;
            }
            .status-badge {
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                font-size: 0.75rem;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.375rem;
            }
            .status-beklemede {
                background-color: rgba(245, 158, 11, 0.1);
                color: var(--warning);
                border: 1px solid rgba(245, 158, 11, 0.2);
            }
            .status-hazirlaniyor {
                background-color: rgba(59, 130, 246, 0.1);
                color: #1e40af;
                border: 1px solid rgba(59, 130, 246, 0.2);
            }
            .status-tamamlandi {
                background-color: rgba(16, 185, 129, 0.1);
                color: var(--success);
                border: 1px solid rgba(16, 185, 129, 0.2);
            }
            .status-iptal {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger);
                border: 1px solid rgba(239, 68, 68, 0.2);
            }
            .action-links {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                align-items: center;
            }
            .action-btn {
                padding: 0.5rem 0.875rem;
                border-radius: 0.5rem;
                text-decoration: none;
                font-size: 0.8rem;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.375rem;
                transition: all 0.3s ease;
                border: 1px solid transparent;
            }
            .action-btn:hover {
                transform: translateY(-1px);
            }
            .action-view {
                background-color: rgba(59, 130, 246, 0.1);
                color: #1e40af;
                border-color: rgba(59, 130, 246, 0.2);
            }
            .action-view:hover {
                background-color: #3b82f6;
                color: white;
            }
            .action-edit {
                background-color: rgba(249, 115, 22, 0.1);
                color: var(--secondary);
                border-color: rgba(249, 115, 22, 0.2);
            }
            .action-edit:hover {
                background-color: var(--secondary);
                color: white;
            }
            .status-dropdown {
                position: relative;
                display: inline-block;
            }
            .status-dropdown select {
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid var(--gray-light);
                background: white;
                font-size: 0.75rem;
                cursor: pointer;
            }
        </style>
        
        <div style="overflow-x: auto;">
            <table class="orders-table">
                <thead>
                    <tr>
                        <th>Sipariş No</th>
                        <th>Müşteri</th>
                        <th>Tarihi</th>
                        <th>Renkler</th>
                        <th>Takip Kodları</th>
                        <th>Toplam Tutar</th>
                        <th>Durum</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($siparisler as $siparis): ?>
                    <tr>
                        <td style="font-weight: 600;">#<?php echo $siparis['id']; ?></td>
                        <td style="font-weight: 600; color: var(--dark);">
                            <?php echo htmlspecialchars($siparis['musteri_adi'] ?? 'Bilinmeyen'); ?>
                        </td>
                        <td><?php echo date('d.m.Y H:i', strtotime($siparis['siparis_tarihi'])); ?></td>
                        <td>
                            <div style="font-size: 0.875rem; color: var(--dark); font-weight: 600; max-width: 120px; word-wrap: break-word;">
                                <?php echo $siparis['renkler'] ? htmlspecialchars($siparis['renkler']) : '<span style="color: #999;">-</span>'; ?>
                            </div>
                        </td>
                        <td>
                            <div style="font-size: 0.875rem; color: var(--gray); font-weight: 600; max-width: 150px; word-wrap: break-word;">
                                <?php echo $siparis['takip_kodlari'] ? htmlspecialchars($siparis['takip_kodlari']) : '<span style="color: #999;">-</span>'; ?>
                            </div>
                        </td>
                        <td style="font-weight: 600;">
                            <?php echo number_format($siparis['toplam_tutar'] ?? 0, 2, ',', '.'); ?> ₺
                        </td>
                        <td>
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="siparis_id" value="<?php echo $siparis['id']; ?>">
                                <div class="status-dropdown">
                                    <select name="yeni_durum" onchange="this.form.submit()">
                                        <option value="beklemede" <?php echo ($siparis['durum'] == 'beklemede') ? 'selected' : ''; ?>>Beklemede</option>
                                        <option value="hazirlaniyor" <?php echo ($siparis['durum'] == 'hazirlaniyor') ? 'selected' : ''; ?>>Hazırlanıyor</option>
                                        <option value="tamamlandi" <?php echo ($siparis['durum'] == 'tamamlandi') ? 'selected' : ''; ?>>Tamamlandı</option>
                                        <option value="iptal" <?php echo ($siparis['durum'] == 'iptal') ? 'selected' : ''; ?>>İptal</option>
                                    </select>
                                    <input type="hidden" name="siparis_durum_guncelle" value="1">
                                </div>
                            </form>
                            <span class="status-badge status-<?php echo $siparis['durum']; ?>" style="margin-left: 0.5rem;">
                                <i class="fas fa-<?php 
                                    switch($siparis['durum']) {
                                        case 'beklemede': echo 'clock'; break;
                                        case 'hazirlaniyor': echo 'spinner'; break;
                                        case 'tamamlandi': echo 'check-circle'; break;
                                        case 'iptal': echo 'times-circle'; break;
                                        default: echo 'question-circle';
                                    }
                                ?>"></i>
                                <?php echo ucfirst($siparis['durum']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="action-links">
                                <a href="siparis_detay.php?id=<?php echo $siparis['id']; ?>" class="action-btn action-view" title="Detay Görüntüle">
                                    <i class="fas fa-eye"></i>
                                    Detay
                                </a>
                                <a href="siparis_duzenle.php?id=<?php echo $siparis['id']; ?>" class="action-btn action-edit" title="Düzenle">
                                    <i class="fas fa-edit"></i>
                                    Düzenle
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?> 