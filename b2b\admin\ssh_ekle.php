<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "SSH Kaydı Ekle - B2B Admin";
$current_page = "ssh_kayitlari";

$mesaj = '';
$mesaj_tur = '';

// <PERSON>iriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];

// Form submit edildiğinde
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $cari_id = intval($_POST['cari_id']);
    $siparis_id = !empty($_POST['siparis_id']) ? intval($_POST['siparis_id']) : null;
    $urun_ids = $_POST['urun_id'] ?? [];
    $urun_adlari = $_POST['urun_adi'] ?? [];
    $parca_adlari = $_POST['parca_adi'] ?? [];
    $ssh_nedeni = $_POST['ssh_nedeni'];
    $ssh_kaynagi = $_POST['ssh_kaynagi'];
    $aciklama = trim($_POST['aciklama']);
    $durum = $_POST['durum'] ?? 'beklemede';
    
    // En az bir ürün var mı kontrol et
    $urun_sayisi = count($urun_adlari);
    $gecerli_urun_var = false;
    for ($i = 0; $i < $urun_sayisi; $i++) {
        if (!empty(trim($urun_adlari[$i]))) {
            $gecerli_urun_var = true;
            break;
        }
    }
    
    if ($cari_id && $gecerli_urun_var && $ssh_nedeni && $ssh_kaynagi) {
        try {
            $conn->begin_transaction();
            $ssh_ids = [];
            
            // Her ürün için ayrı SSH kaydı oluştur
            for ($i = 0; $i < $urun_sayisi; $i++) {
                $urun_adi = trim($urun_adlari[$i]);
                if (empty($urun_adi)) continue; // Boş ürün adlarını atla
                
                $urun_id = !empty($urun_ids[$i]) ? intval($urun_ids[$i]) : null;
                $parca_adi = trim($parca_adlari[$i] ?? '');
            
            // SSH kaydını ekle
            $sql = "INSERT INTO b2b_ssh_kayitlari (cari_id, siparis_id, urun_id, urun_adi, parca_adi, ssh_nedeni, ssh_kaynagi, durum, aciklama, islem_yapan_admin_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('iiissssssi', $cari_id, $siparis_id, $urun_id, $urun_adi, $parca_adi, $ssh_nedeni, $ssh_kaynagi, $durum, $aciklama, $admin_id);
            
            if ($stmt->execute()) {
                    $ssh_ids[] = $conn->insert_id;
                } else {
                    throw new Exception("SSH kaydı eklenirken hata oluştu: " . $conn->error);
                }
            }
            
            if (empty($ssh_ids)) {
                throw new Exception("Hiçbir SSH kaydı oluşturulamadı!");
            }
            
            // İlk SSH kaydının ID'sini al (dosya yükleme için)
            $ssh_id = $ssh_ids[0];
                
                // Dosya yükleme işlemi
                $upload_dir = '../uploads/ssh/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $uploaded_files = [];
                
                // 3 adet resim yükleme
                for ($i = 1; $i <= 3; $i++) {
                    if (isset($_FILES["ssh_resim_$i"]) && $_FILES["ssh_resim_$i"]['error'] == 0) {
                        $file = $_FILES["ssh_resim_$i"];
                        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                        
                        // Dosya boyutu kontrolü (5MB = 5 * 1024 * 1024)
                        if ($file['size'] > 5 * 1024 * 1024) {
                            throw new Exception("Resim $i çok büyük. Maksimum 5MB olmalı.");
                        }
                        
                        // Dosya uzantısı kontrolü
                        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                        if (in_array(strtolower($file_extension), $allowed_extensions)) {
                            
                            // Benzersiz dosya adı oluştur
                            $new_filename = 'ssh_' . $ssh_id . '_' . $i . '_' . time() . '.' . $file_extension;
                            $upload_path = $upload_dir . $new_filename;
                            
                            // Dosyayı yükle
                            if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                                
                                // Veritabanına kaydet
                                $file_sql = "INSERT INTO b2b_ssh_dosyalar (ssh_id, dosya_adi, dosya_yolu, dosya_tipi, dosya_boyutu) VALUES (?, ?, ?, 'resim', ?)";
                                $file_stmt = $conn->prepare($file_sql);
                                $file_stmt->bind_param('issi', $ssh_id, $file['name'], $upload_path, $file['size']);
                                $file_stmt->execute();
                                
                                $uploaded_files[] = $file['name'];
                            } else {
                                throw new Exception("Resim $i yüklenemedi. Dosya izinlerini kontrol edin.");
                            }
                        } else {
                            throw new Exception("Resim $i desteklenmeyen format. JPG, PNG, GIF, WEBP kullanın.");
                        }
                    }
                }
                
                // Durum geçmişine kaydet
                $history_sql = "INSERT INTO b2b_ssh_durum_gecmisi (ssh_id, yeni_durum, degisiklik_notu, degistiren_admin_id) VALUES (?, ?, ?, ?)";
                $history_stmt = $conn->prepare($history_sql);
                $history_note = "SSH kaydı oluşturuldu" . (count($uploaded_files) > 0 ? " - " . count($uploaded_files) . " resim yüklendi" : "");
                $history_stmt->bind_param('issi', $ssh_id, $durum, $history_note, $admin_id);
                $history_stmt->execute();
                
                $conn->commit();
                
            $mesaj = count($ssh_ids) . " adet SSH kaydı başarıyla eklendi!";
                if (count($uploaded_files) > 0) {
                    $mesaj .= "<br><strong>Yüklenen resimler:</strong> " . implode(', ', $uploaded_files);
                }
            if (count($ssh_ids) > 1) {
                $mesaj .= "<br><strong>Oluşturulan SSH kayıtları:</strong> " . implode(', ', $ssh_ids);
            }
            $mesaj .= "<br><a href='ssh_kayitlari.php' class='alert-link'>SSH Kayıtlarını Görüntüle</a>";
                $mesaj_tur = "success";
            
        } catch (Exception $e) {
            $conn->rollback();
            $mesaj = "Hata: " . $e->getMessage();
            $mesaj_tur = "danger";
        }
    } else {
        $mesaj = "Lütfen gerekli alanları doldurun!";
        $mesaj_tur = "warning";
    }
}

// Müşterileri çek
$musteriler = [];
$musteri_sql = "SELECT id, firm_name FROM cari_firmalar ORDER BY firm_name";
$musteri_result = $conn->query($musteri_sql);
if ($musteri_result && $musteri_result->num_rows > 0) {
    while ($row = $musteri_result->fetch_assoc()) {
        $musteriler[] = $row;
    }
}

// Ürünleri çek
$urunler = [];
$urun_sql = "SELECT id, stok_kodu, stok_adi FROM urunler ORDER BY stok_adi";
$urun_result = $conn->query($urun_sql);
if ($urun_result && $urun_result->num_rows > 0) {
    while ($row = $urun_result->fetch_assoc()) {
        $urunler[] = $row;
    }
}

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-plus"></i>
            SSH Kaydı Ekle
        </h1>
        <a href="ssh_kayitlari.php" class="btn">
            <i class="fas fa-arrow-left"></i>
            SSH Kayıtlarına Dön
        </a>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-tools"></i>
        Yeni SSH Kaydı
    </h3>
    
    <form method="post" enctype="multipart/form-data" style="display: grid; gap: 2rem;">
        <!-- Müşteri ve Sipariş Bilgileri -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-user"></i> Müşteri Bilgileri
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-building"></i> Müşteri *
                    </label>
                    <select name="cari_id" id="cari_id" required style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="">Müşteri seçiniz...</option>
                        <?php foreach ($musteriler as $musteri): ?>
                            <option value="<?php echo $musteri['id']; ?>"><?php echo htmlspecialchars($musteri['firm_name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-shopping-cart"></i> Sipariş No (Opsiyonel)
                    </label>
                    <select name="siparis_id" id="siparis_id" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="">Sipariş seçiniz...</option>
                    </select>
                    <small style="color: var(--gray);">Müşteri seçildikten sonra siparişler yüklenecek</small>
                </div>
            </div>
        </div>
        
        <!-- Ürün Bilgileri -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem; display: flex; justify-content: space-between; align-items: center;">
                <span><i class="fas fa-box"></i> Ürün Bilgileri</span>
                <button type="button" onclick="addProduct()" class="btn" style="padding: 0.5rem 1rem; font-size: 0.875rem;">
                    <i class="fas fa-plus"></i> Ürün Ekle
                </button>
            </h4>
            
            <div id="products-container">
                <!-- İlk ürün satırı -->
                <div class="product-row" data-index="0">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) auto; gap: 1rem; padding: 1rem; border: 2px solid var(--gray-light); border-radius: 0.5rem; margin-bottom: 1rem; background: #f8f9fa;">
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-cube"></i> Ürün Seç (Opsiyonel)
                    </label>
                            <select name="urun_id[]" class="urun_select" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" onchange="fillProductInfo(this)">
                        <option value="">Ürün seçiniz...</option>
                        <?php foreach ($urunler as $urun): ?>
                            <option value="<?php echo $urun['id']; ?>" data-stok-kodu="<?php echo htmlspecialchars($urun['stok_kodu']); ?>" data-stok-adi="<?php echo htmlspecialchars($urun['stok_adi']); ?>">
                                <?php echo htmlspecialchars($urun['stok_kodu'] . ' - ' . $urun['stok_adi']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-tag"></i> Ürün Adı *
                    </label>
                            <input type="text" name="urun_adi[]" class="urun_adi" required style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" placeholder="Ürün adını giriniz">
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-cog"></i> Parça Adı
                    </label>
                            <input type="text" name="parca_adi[]" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" placeholder="Parça adını giriniz (opsiyonel)">
                        </div>
                        
                        <div class="form-group" style="display: flex; align-items: end;">
                            <button type="button" onclick="removeProduct(this)" class="btn" style="background: var(--danger); padding: 0.875rem; border-radius: 0.5rem;" title="Ürünü Kaldır">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- SSH Detayları -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-tools"></i> SSH Detayları
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-exclamation-triangle"></i> SSH Nedeni *
                    </label>
                    <select name="ssh_nedeni" required style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="">Neden seçiniz...</option>
                        <option value="eksik">Eksik</option>
                        <option value="hasarli">Hasarlı</option>
                        <option value="yanlis">Yanlış</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-source"></i> SSH Kaynağı *
                    </label>
                    <select name="ssh_kaynagi" required style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="">Kaynak seçiniz...</option>
                        <option value="uretim">Üretim</option>
                        <option value="depo">Depo</option>
                        <option value="paketleme">Paketleme</option>
                        <option value="kullanici_hatasi">Kullanıcı Hatası</option>
                        <option value="etiket_hatasi">Etiket Hatası</option>
                        <option value="tasima">Taşıma</option>
                        <option value="tedarikci">Tedarikçi</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-flag"></i> SSH Durumu
                    </label>
                    <select name="durum" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="hazirlaniyor">Hazırlanıyor</option>
                        <option value="sevk_edildi">Sevk Edildi</option>
                        <option value="iptal">İptal</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- SSH Resimleri -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-images"></i> SSH Resimleri (Opsiyonel)
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-image"></i> Resim 1
                    </label>
                    <input type="file" name="ssh_resim_1" accept="image/*" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                    <small style="color: var(--gray);">JPG, PNG, GIF formatları desteklenir (Max: 5MB)</small>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-image"></i> Resim 2
                    </label>
                    <input type="file" name="ssh_resim_2" accept="image/*" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                    <small style="color: var(--gray);">JPG, PNG, GIF formatları desteklenir (Max: 5MB)</small>
                </div>
                
                <div class="form-group">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                        <i class="fas fa-image"></i> Resim 3
                    </label>
                    <input type="file" name="ssh_resim_3" accept="image/*" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                    <small style="color: var(--gray);">JPG, PNG, GIF formatları desteklenir (Max: 5MB)</small>
                </div>
            </div>
            <div style="background: #e3f2fd; padding: 1rem; border-radius: 0.5rem; margin-top: 1rem;">
                <p style="margin: 0; color: #1976d2; font-size: 0.875rem;">
                    <i class="fas fa-info-circle"></i>
                    <strong>Bilgi:</strong> SSH ile ilgili fotoğrafları yükleyebilirsiniz. Hasarlı ürün, paket durumu, vb. görseller problem çözümünde yardımcı olacaktır.
                </p>
            </div>
        </div>
        
        <!-- Açıklama -->
        <div class="form-section">
            <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                <i class="fas fa-comment"></i> Açıklama
            </h4>
            <div class="form-group">
                <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                    <i class="fas fa-edit"></i> Detaylı Açıklama
                </label>
                <textarea name="aciklama" rows="4" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem; resize: vertical;" placeholder="SSH ile ilgili detaylı açıklama yazınız..."></textarea>
            </div>
        </div>
        
        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
            <a href="ssh_kayitlari.php" class="btn" style="background: var(--gray); text-decoration: none;">
                <i class="fas fa-times"></i>
                İptal
            </a>
            <button type="submit" class="btn">
                <i class="fas fa-save"></i>
                SSH Kaydını Oluştur
            </button>
        </div>
    </form>
</div>

<script>
// Müşteri seçildiğinde siparişleri yükle
document.getElementById('cari_id').addEventListener('change', function() {
    const cariId = this.value;
    const siparisSelect = document.getElementById('siparis_id');
    
    // Sipariş listesini temizle
    siparisSelect.innerHTML = '<option value="">Sipariş seçiniz...</option>';
    
    if (cariId) {
        // AJAX ile siparişleri getir
        fetch('ajax_get_orders.php?cari_id=' + cariId)
            .then(response => response.json())
            .then(data => {
                data.forEach(siparis => {
                    const option = document.createElement('option');
                    option.value = siparis.id;
                    option.textContent = `#${siparis.id} - ${siparis.created_at} (${siparis.toplam_tutar} TL)`;
                    siparisSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Siparişler yüklenirken hata:', error);
            });
    }
});

// Ürün seçildiğinde ürün adını otomatik doldur
function fillProductInfo(selectElement) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const productRow = selectElement.closest('.product-row');
    const urunAdiInput = productRow.querySelector('.urun_adi');
    
    if (selectedOption.value) {
        const stokKodu = selectedOption.getAttribute('data-stok-kodu');
        const stokAdi = selectedOption.getAttribute('data-stok-adi');
        urunAdiInput.value = `${stokKodu} - ${stokAdi}`;
    }
}

// Yeni ürün satırı ekle
let productIndex = 1;
function addProduct() {
    const container = document.getElementById('products-container');
    const newProductRow = document.createElement('div');
    newProductRow.className = 'product-row';
    newProductRow.setAttribute('data-index', productIndex);
    
    newProductRow.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) auto; gap: 1rem; padding: 1rem; border: 2px solid var(--gray-light); border-radius: 0.5rem; margin-bottom: 1rem; background: #f8f9fa;">
            <div class="form-group">
                <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                    <i class="fas fa-cube"></i> Ürün Seç (Opsiyonel)
                </label>
                <select name="urun_id[]" class="urun_select" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" onchange="fillProductInfo(this)">
                    <option value="">Ürün seçiniz...</option>
                    <?php foreach ($urunler as $urun): ?>
                        <option value="<?php echo $urun['id']; ?>" data-stok-kodu="<?php echo htmlspecialchars($urun['stok_kodu']); ?>" data-stok-adi="<?php echo htmlspecialchars($urun['stok_adi']); ?>">
                            <?php echo htmlspecialchars($urun['stok_kodu'] . ' - ' . $urun['stok_adi']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                    <i class="fas fa-tag"></i> Ürün Adı *
                </label>
                <input type="text" name="urun_adi[]" class="urun_adi" required style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" placeholder="Ürün adını giriniz">
            </div>
            
            <div class="form-group">
                <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                    <i class="fas fa-cog"></i> Parça Adı
                </label>
                <input type="text" name="parca_adi[]" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" placeholder="Parça adını giriniz (opsiyonel)">
            </div>
            
            <div class="form-group" style="display: flex; align-items: end;">
                <button type="button" onclick="removeProduct(this)" class="btn" style="background: var(--danger); padding: 0.875rem; border-radius: 0.5rem;" title="Ürünü Kaldır">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newProductRow);
    productIndex++;
    
    // Animasyon efekti
    newProductRow.style.opacity = '0';
    newProductRow.style.transform = 'translateY(-20px)';
    setTimeout(() => {
        newProductRow.style.transition = 'all 0.3s ease';
        newProductRow.style.opacity = '1';
        newProductRow.style.transform = 'translateY(0)';
    }, 10);
}

// Ürün satırını kaldır
function removeProduct(button) {
    const productRow = button.closest('.product-row');
    const container = document.getElementById('products-container');
    
    // En az 1 ürün kalmalı
    if (container.children.length <= 1) {
        alert('En az 1 ürün bulunmalıdır!');
        return;
    }
    
    // Animasyon ile kaldır
    productRow.style.transition = 'all 0.3s ease';
    productRow.style.opacity = '0';
    productRow.style.transform = 'translateX(-20px)';
    
    setTimeout(() => {
        productRow.remove();
    }, 300);
}
</script>

<?php include 'includes/footer.php'; ?> 