[2025-06-26 23:21:04] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 32
)

[2025-06-26 23:21:04] 📊 Tablo | Data: uretim_malze<PERSON>er_koli
[2025-06-26 23:21:04] 🔍 Tek malzeme detayı isteniyor | Data: 32
[2025-06-26 23:21:04] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-26 23:21:04] 🚀 SQL Execute ediliyor | Data: 32
[2025-06-26 23:21:04] ✅ SQL Execute başarılı
[2025-06-26 23:21:04] 📋 Fetch sonucu | Data: Array
(
    [id] => 32
    [malz_kodu] => K2
    [malzeme_adi] => 180 2K YAN ÜST KOLİSİ
    [koli_turu] => ÖZEL ÖLÇÜ
    [birim] => 
    [fiyatAdet] => 0.00
    [fiyatMetre] => 0.00
    [fiyatM2] => 131.52
    [stok_miktar] => 1000.00
    [en_cm] => 640.00
    [boy_cm] => 2055.00
    [fiyat_adet] => 0.00
    [fiyat_m2] => 131.52
    [fiyat_metre] => 0.00
)

[2025-06-26 23:21:04] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-26 23:21:30] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 32
)

[2025-06-26 23:21:30] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-26 23:21:30] 🔍 Tek malzeme detayı isteniyor | Data: 32
[2025-06-26 23:21:30] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-26 23:21:30] 🚀 SQL Execute ediliyor | Data: 32
[2025-06-26 23:21:30] ✅ SQL Execute başarılı
[2025-06-26 23:21:30] 📋 Fetch sonucu | Data: Array
(
    [id] => 32
    [malz_kodu] => K2
    [malzeme_adi] => 180 2K YAN ÜST KOLİSİ
    [koli_turu] => ÖZEL ÖLÇÜ
    [birim] => 
    [fiyatAdet] => 0.00
    [fiyatMetre] => 0.00
    [fiyatM2] => 131.52
    [stok_miktar] => 1000.00
    [en_cm] => 640.00
    [boy_cm] => 2055.00
    [fiyat_adet] => 0.00
    [fiyat_m2] => 131.52
    [fiyat_metre] => 0.00
)

[2025-06-26 23:21:30] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:01:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 08:01:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 08:01:43] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:01:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 08:01:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 08:01:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => urunler
    [id] => 0
)

[2025-06-27 08:01:43] ❌ Geçersiz kategori | Data: urunler
[2025-06-27 08:01:43] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:01:43] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:01:43] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:02:20] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 26
)

[2025-06-27 08:02:20] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:02:20] 🔍 Tek malzeme detayı isteniyor | Data: 26
[2025-06-27 08:02:20] 🚀 SQL Execute ediliyor | Data: 26
[2025-06-27 08:02:20] ✅ SQL Execute başarılı
[2025-06-27 08:02:20] 📋 Fetch sonucu | Data: Array
(
    [id] => 26
    [malz_kodu] => S4
    [malzeme_adi] => AYTAŞI SUNTA
    [birim] => 280
    [fiyat] => 0.029762
    [stok_miktar] => 10.00
)

[2025-06-27 08:02:20] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:02:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 6
)

[2025-06-27 08:02:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:02:25] 🔍 Tek malzeme detayı isteniyor | Data: 6
[2025-06-27 08:02:25] 🚀 SQL Execute ediliyor | Data: 6
[2025-06-27 08:02:25] ✅ SQL Execute başarılı
[2025-06-27 08:02:25] 📋 Fetch sonucu | Data: Array
(
    [id] => 6
    [malz_kodu] => P2
    [malzeme_adi] => 22*0.40 RENKLİ
    [birim] => METRE
    [fiyat] => 4.17
    [stok_miktar] => 1000.00
)

[2025-06-27 08:02:25] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:02:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 31
)

[2025-06-27 08:02:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:02:26] 🔍 Tek malzeme detayı isteniyor | Data: 31
[2025-06-27 08:02:26] 🚀 SQL Execute ediliyor | Data: 31
[2025-06-27 08:02:26] ✅ SQL Execute başarılı
[2025-06-27 08:02:26] 📋 Fetch sonucu | Data: Array
(
    [id] => 31
    [malz_kodu] => S9
    [malzeme_adi] => HAM SUNTA
    [birim] => 366
    [fiyat] => 0.021052
    [stok_miktar] => 20.00
)

[2025-06-27 08:02:26] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:02:29] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 7
)

[2025-06-27 08:02:29] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:02:29] 🔍 Tek malzeme detayı isteniyor | Data: 7
[2025-06-27 08:02:29] 🚀 SQL Execute ediliyor | Data: 7
[2025-06-27 08:02:29] ✅ SQL Execute başarılı
[2025-06-27 08:02:29] 📋 Fetch sonucu | Data: Array
(
    [id] => 7
    [malz_kodu] => H3
    [malzeme_adi] => RAF PİMİ BEYAZ PLS.
    [birim] => adet
    [fiyat] => 0.17
    [stok_miktar] => 1000.00
)

[2025-06-27 08:02:29] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:02:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 17
)

[2025-06-27 08:02:31] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:02:31] 🔍 Tek malzeme detayı isteniyor | Data: 17
[2025-06-27 08:02:31] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-27 08:02:31] 🚀 SQL Execute ediliyor | Data: 17
[2025-06-27 08:02:31] ✅ SQL Execute başarılı
[2025-06-27 08:02:31] 📋 Fetch sonucu | Data: Array
(
    [id] => 17
    [malz_kodu] => ZK4
    [malzeme_adi] => Z KARTON 140x
    [koli_turu] => Z-KARTON
    [birim] => 
    [fiyatAdet] => 140.00
    [fiyatMetre] => 400.00
    [fiyatM2] => 0.00
    [stok_miktar] => 1.00
    [en_cm] => 0.00
    [boy_cm] => 0.00
    [fiyat_adet] => 140.00
    [fiyat_m2] => 0.00
    [fiyat_metre] => 400.00
)

[2025-06-27 08:02:31] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:11:29] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 08:11:29] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 08:11:29] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 08:11:29] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 08:11:29] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:11:30] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:11:30] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 33
)

[2025-06-27 08:11:30] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:11:30] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:11:30] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 6
)

[2025-06-27 08:11:30] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:11:30] 🔍 Tek malzeme detayı isteniyor | Data: 33
[2025-06-27 08:11:30] 🚀 SQL Execute ediliyor | Data: 33
[2025-06-27 08:11:30] ✅ SQL Execute başarılı
[2025-06-27 08:11:30] 📋 Fetch sonucu | Data: Array
(
    [id] => 33
    [malz_kodu] => S10
    [malzeme_adi] => HAM SUNTA KAPAK
    [birim] => 280
    [fiyat] => 0.022959
    [stok_miktar] => 10.00
)

[2025-06-27 08:11:30] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:11:30] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:11:30] 🔍 Tek malzeme detayı isteniyor | Data: 6
[2025-06-27 08:11:30] 🚀 SQL Execute ediliyor | Data: 6
[2025-06-27 08:11:30] ✅ SQL Execute başarılı
[2025-06-27 08:11:30] 📋 Fetch sonucu | Data: Array
(
    [id] => 6
    [malz_kodu] => P2
    [malzeme_adi] => 22*0.40 RENKLİ
    [birim] => METRE
    [fiyat] => 4.17
    [stok_miktar] => 1000.00
)

[2025-06-27 08:11:30] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:11:30] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 57
)

[2025-06-27 08:11:30] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:11:30] 🔍 Tek malzeme detayı isteniyor | Data: 57
[2025-06-27 08:11:30] 🚀 SQL Execute ediliyor | Data: 57
[2025-06-27 08:11:30] ✅ SQL Execute başarılı
[2025-06-27 08:11:30] 📋 Fetch sonucu | Data: Array
(
    [id] => 57
    [malz_kodu] => H25
    [malzeme_adi] => ALİMİNYUM L KULP
    [birim] => adet
    [fiyat] => 9.50
    [stok_miktar] => 1000.00
)

[2025-06-27 08:11:30] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:12:16] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 23
)

[2025-06-27 08:12:16] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:12:16] 🔍 Tek malzeme detayı isteniyor | Data: 23
[2025-06-27 08:12:16] 🚀 SQL Execute ediliyor | Data: 23
[2025-06-27 08:12:16] ✅ SQL Execute başarılı
[2025-06-27 08:12:16] 📋 Fetch sonucu | Data: Array
(
    [id] => 23
    [malz_kodu] => S2
    [malzeme_adi] => BUTE BEYAZ SUNTA
    [birim] => 366
    [fiyat] => 0.026128
    [stok_miktar] => 10.00
)

[2025-06-27 08:12:16] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:12:18] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 30
)

[2025-06-27 08:12:18] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:12:18] 🔍 Tek malzeme detayı isteniyor | Data: 30
[2025-06-27 08:12:18] 🚀 SQL Execute ediliyor | Data: 30
[2025-06-27 08:12:18] ✅ SQL Execute başarılı
[2025-06-27 08:12:18] 📋 Fetch sonucu | Data: Array
(
    [id] => 30
    [malz_kodu] => S8
    [malzeme_adi] => ARKALIK 5MM
    [birim] => 280
    [fiyat] => 0.012755
    [stok_miktar] => 10.00
)

[2025-06-27 08:12:18] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:12:35] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 8
)

[2025-06-27 08:12:35] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:12:35] 🔍 Tek malzeme detayı isteniyor | Data: 8
[2025-06-27 08:12:35] 🚀 SQL Execute ediliyor | Data: 8
[2025-06-27 08:12:35] ✅ SQL Execute başarılı
[2025-06-27 08:12:35] 📋 Fetch sonucu | Data: Array
(
    [id] => 8
    [malz_kodu] => P4
    [malzeme_adi] => 22*0.50 AYTAŞI MAT
    [birim] => METRE
    [fiyat] => 1.91
    [stok_miktar] => 1000.00
)

[2025-06-27 08:12:35] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:12:37] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 6
)

[2025-06-27 08:12:37] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:12:37] 🔍 Tek malzeme detayı isteniyor | Data: 6
[2025-06-27 08:12:37] 🚀 SQL Execute ediliyor | Data: 6
[2025-06-27 08:12:37] ✅ SQL Execute başarılı
[2025-06-27 08:12:37] 📋 Fetch sonucu | Data: Array
(
    [id] => 6
    [malz_kodu] => P2
    [malzeme_adi] => 22*0.40 RENKLİ
    [birim] => METRE
    [fiyat] => 4.17
    [stok_miktar] => 1000.00
)

[2025-06-27 08:12:37] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:12:40] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 42
)

[2025-06-27 08:12:40] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:12:40] 🔍 Tek malzeme detayı isteniyor | Data: 42
[2025-06-27 08:12:40] 🚀 SQL Execute ediliyor | Data: 42
[2025-06-27 08:12:40] ✅ SQL Execute başarılı
[2025-06-27 08:12:40] 📋 Fetch sonucu | Data: Array
(
    [id] => 42
    [malz_kodu] => MP7
    [malzeme_adi] => TEL/MİL
    [birim] => kg
    [fiyat] => 41.00
    [stok_miktar] => 1000.00
)

[2025-06-27 08:12:40] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:12:42] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 49
)

[2025-06-27 08:12:42] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:12:42] 🔍 Tek malzeme detayı isteniyor | Data: 49
[2025-06-27 08:12:42] 🚀 SQL Execute ediliyor | Data: 49
[2025-06-27 08:12:42] ✅ SQL Execute başarılı
[2025-06-27 08:12:42] 📋 Fetch sonucu | Data: Array
(
    [id] => 49
    [malz_kodu] => H17
    [malzeme_adi] => BONCUK BANTLAMA TUTKAL
    [birim] => kg
    [fiyat] => 92.00
    [stok_miktar] => 1000.00
)

[2025-06-27 08:12:42] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:12:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 18
)

[2025-06-27 08:12:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:12:45] 🔍 Tek malzeme detayı isteniyor | Data: 18
[2025-06-27 08:12:45] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-27 08:12:45] 🚀 SQL Execute ediliyor | Data: 18
[2025-06-27 08:12:45] ✅ SQL Execute başarılı
[2025-06-27 08:12:45] 📋 Fetch sonucu | Data: Array
(
    [id] => 18
    [malz_kodu] => ZK5
    [malzeme_adi] => Z KARTON 150x
    [koli_turu] => Z-KARTON
    [birim] => 
    [fiyatAdet] => 150.00
    [fiyatMetre] => 400.00
    [fiyatM2] => 0.00
    [stok_miktar] => 1.00
    [en_cm] => 0.00
    [boy_cm] => 0.00
    [fiyat_adet] => 150.00
    [fiyat_m2] => 0.00
    [fiyat_metre] => 400.00
)

[2025-06-27 08:12:45] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:12:47] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 69
)

[2025-06-27 08:12:47] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:12:47] 🔍 Tek malzeme detayı isteniyor | Data: 69
[2025-06-27 08:12:47] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-27 08:12:47] 🚀 SQL Execute ediliyor | Data: 69
[2025-06-27 08:12:47] ✅ SQL Execute başarılı
[2025-06-27 08:12:47] 📋 Fetch sonucu | Data: Array
(
    [id] => 69
    [malz_kodu] => K38
    [malzeme_adi] => YANKE ÇALIŞMA MASASI ALT
    [koli_turu] => ÖZEL ÖLÇÜ
    [birim] => adet
    [fiyatAdet] => 0.00
    [fiyatMetre] => 0.00
    [fiyatM2] => 97.50
    [stok_miktar] => 1000.00
    [en_cm] => 750.00
    [boy_cm] => 1300.00
    [fiyat_adet] => 0.00
    [fiyat_m2] => 97.50
    [fiyat_metre] => 0.00
)

[2025-06-27 08:12:47] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:17:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 08:17:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 08:17:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 08:17:02] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:17:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 08:17:02] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:17:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 33
)

[2025-06-27 08:17:02] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:17:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 6
)

[2025-06-27 08:17:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 57
)

[2025-06-27 08:17:02] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:17:02] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:17:02] 🔍 Tek malzeme detayı isteniyor | Data: 33
[2025-06-27 08:17:02] 🚀 SQL Execute ediliyor | Data: 33
[2025-06-27 08:17:02] ✅ SQL Execute başarılı
[2025-06-27 08:17:02] 📋 Fetch sonucu | Data: Array
(
    [id] => 33
    [malz_kodu] => S10
    [malzeme_adi] => HAM SUNTA KAPAK
    [birim] => 280
    [fiyat] => 0.022959
    [stok_miktar] => 10.00
)

[2025-06-27 08:17:02] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:17:02] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:17:02] 🔍 Tek malzeme detayı isteniyor | Data: 6
[2025-06-27 08:17:02] 🚀 SQL Execute ediliyor | Data: 6
[2025-06-27 08:17:02] ✅ SQL Execute başarılı
[2025-06-27 08:17:02] 📋 Fetch sonucu | Data: Array
(
    [id] => 6
    [malz_kodu] => P2
    [malzeme_adi] => 22*0.40 RENKLİ
    [birim] => METRE
    [fiyat] => 4.17
    [stok_miktar] => 1000.00
)

[2025-06-27 08:17:02] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:17:02] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:17:02] 🔍 Tek malzeme detayı isteniyor | Data: 57
[2025-06-27 08:17:02] 🚀 SQL Execute ediliyor | Data: 57
[2025-06-27 08:17:02] ✅ SQL Execute başarılı
[2025-06-27 08:17:02] 📋 Fetch sonucu | Data: Array
(
    [id] => 57
    [malz_kodu] => H25
    [malzeme_adi] => ALİMİNYUM L KULP
    [birim] => adet
    [fiyat] => 9.50
    [stok_miktar] => 1000.00
)

[2025-06-27 08:17:02] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:19:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 08:19:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 08:19:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 08:19:14] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:19:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 08:19:14] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:19:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 6
)

[2025-06-27 08:19:14] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:19:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 33
)

[2025-06-27 08:19:14] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:19:15] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 57
)

[2025-06-27 08:19:15] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:19:15] 🔍 Tek malzeme detayı isteniyor | Data: 6
[2025-06-27 08:19:15] 🚀 SQL Execute ediliyor | Data: 6
[2025-06-27 08:19:15] ✅ SQL Execute başarılı
[2025-06-27 08:19:15] 📋 Fetch sonucu | Data: Array
(
    [id] => 6
    [malz_kodu] => P2
    [malzeme_adi] => 22*0.40 RENKLİ
    [birim] => METRE
    [fiyat] => 4.17
    [stok_miktar] => 1000.00
)

[2025-06-27 08:19:15] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:19:15] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:19:15] 🔍 Tek malzeme detayı isteniyor | Data: 33
[2025-06-27 08:19:15] 🚀 SQL Execute ediliyor | Data: 33
[2025-06-27 08:19:15] ✅ SQL Execute başarılı
[2025-06-27 08:19:15] 📋 Fetch sonucu | Data: Array
(
    [id] => 33
    [malz_kodu] => S10
    [malzeme_adi] => HAM SUNTA KAPAK
    [birim] => 280
    [fiyat] => 0.022959
    [stok_miktar] => 10.00
)

[2025-06-27 08:19:15] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:19:15] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:19:15] 🔍 Tek malzeme detayı isteniyor | Data: 57
[2025-06-27 08:19:15] 🚀 SQL Execute ediliyor | Data: 57
[2025-06-27 08:19:15] ✅ SQL Execute başarılı
[2025-06-27 08:19:15] 📋 Fetch sonucu | Data: Array
(
    [id] => 57
    [malz_kodu] => H25
    [malzeme_adi] => ALİMİNYUM L KULP
    [birim] => adet
    [fiyat] => 9.50
    [stok_miktar] => 1000.00
)

[2025-06-27 08:19:15] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:19:22] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 26
)

[2025-06-27 08:19:22] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:19:22] 🔍 Tek malzeme detayı isteniyor | Data: 26
[2025-06-27 08:19:22] 🚀 SQL Execute ediliyor | Data: 26
[2025-06-27 08:19:22] ✅ SQL Execute başarılı
[2025-06-27 08:19:22] 📋 Fetch sonucu | Data: Array
(
    [id] => 26
    [malz_kodu] => S4
    [malzeme_adi] => AYTAŞI SUNTA
    [birim] => 280
    [fiyat] => 0.029762
    [stok_miktar] => 10.00
)

[2025-06-27 08:19:22] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:19:29] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 30
)

[2025-06-27 08:19:29] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:19:29] 🔍 Tek malzeme detayı isteniyor | Data: 30
[2025-06-27 08:19:29] 🚀 SQL Execute ediliyor | Data: 30
[2025-06-27 08:19:29] ✅ SQL Execute başarılı
[2025-06-27 08:19:29] 📋 Fetch sonucu | Data: Array
(
    [id] => 30
    [malz_kodu] => S8
    [malzeme_adi] => ARKALIK 5MM
    [birim] => 280
    [fiyat] => 0.012755
    [stok_miktar] => 10.00
)

[2025-06-27 08:19:29] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:19:49] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 8
)

[2025-06-27 08:19:49] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:19:49] 🔍 Tek malzeme detayı isteniyor | Data: 8
[2025-06-27 08:19:49] 🚀 SQL Execute ediliyor | Data: 8
[2025-06-27 08:19:49] ✅ SQL Execute başarılı
[2025-06-27 08:19:49] 📋 Fetch sonucu | Data: Array
(
    [id] => 8
    [malz_kodu] => P4
    [malzeme_adi] => 22*0.50 AYTAŞI MAT
    [birim] => METRE
    [fiyat] => 1.91
    [stok_miktar] => 1000.00
)

[2025-06-27 08:19:49] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:20:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 41
)

[2025-06-27 08:20:00] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:20:00] 🔍 Tek malzeme detayı isteniyor | Data: 41
[2025-06-27 08:20:00] 🚀 SQL Execute ediliyor | Data: 41
[2025-06-27 08:20:00] ✅ SQL Execute başarılı
[2025-06-27 08:20:00] 📋 Fetch sonucu | Data: Array
(
    [id] => 41
    [malz_kodu] => MP6
    [malzeme_adi] => 10x0.8 CR BORU
    [birim] => metre
    [fiyat] => 15.00
    [stok_miktar] => 1000.00
)

[2025-06-27 08:20:00] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:20:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 15
)

[2025-06-27 08:20:02] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:20:02] 🔍 Tek malzeme detayı isteniyor | Data: 15
[2025-06-27 08:20:02] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-27 08:20:02] 🚀 SQL Execute ediliyor | Data: 15
[2025-06-27 08:20:02] ✅ SQL Execute başarılı
[2025-06-27 08:20:02] 📋 Fetch sonucu | Data: Array
(
    [id] => 15
    [malz_kodu] => ZK2
    [malzeme_adi] => Z KARTON 120x
    [koli_turu] => Z-KARTON
    [birim] => 
    [fiyatAdet] => 120.00
    [fiyatMetre] => 400.00
    [fiyatM2] => 0.00
    [stok_miktar] => 1.00
    [en_cm] => 0.00
    [boy_cm] => 0.00
    [fiyat_adet] => 120.00
    [fiyat_m2] => 0.00
    [fiyat_metre] => 400.00
)

[2025-06-27 08:20:02] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:29:47] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 08:29:47] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 08:29:47] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:29:47] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:29:47] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:29:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 31
)

[2025-06-27 08:29:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:29:55] 🔍 Tek malzeme detayı isteniyor | Data: 31
[2025-06-27 08:29:55] 🚀 SQL Execute ediliyor | Data: 31
[2025-06-27 08:29:55] ✅ SQL Execute başarılı
[2025-06-27 08:29:55] 📋 Fetch sonucu | Data: Array
(
    [id] => 31
    [malz_kodu] => S9
    [malzeme_adi] => HAM SUNTA
    [birim] => 366
    [fiyat] => 0.021052
    [stok_miktar] => 20.00
)

[2025-06-27 08:29:55] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:29:59] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 5
)

[2025-06-27 08:29:59] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:29:59] 🔍 Tek malzeme detayı isteniyor | Data: 5
[2025-06-27 08:29:59] 🚀 SQL Execute ediliyor | Data: 5
[2025-06-27 08:29:59] ✅ SQL Execute başarılı
[2025-06-27 08:29:59] 📋 Fetch sonucu | Data: Array
(
    [id] => 5
    [malz_kodu] => P1
    [malzeme_adi] => 22*0.40 DÜZ RENK
    [birim] => METRE
    [fiyat] => 2.74
    [stok_miktar] => 1000.00
)

[2025-06-27 08:29:59] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:30:01] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 21
)

[2025-06-27 08:30:01] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:30:01] 🔍 Tek malzeme detayı isteniyor | Data: 21
[2025-06-27 08:30:01] 🚀 SQL Execute ediliyor | Data: 21
[2025-06-27 08:30:01] ✅ SQL Execute başarılı
[2025-06-27 08:30:01] 📋 Fetch sonucu | Data: Array
(
    [id] => 21
    [malz_kodu] => H8
    [malzeme_adi] => SARI DÜBEL
    [birim] => adet
    [fiyat] => 0.42
    [stok_miktar] => 1000.00
)

[2025-06-27 08:30:01] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:30:02] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 35
)

[2025-06-27 08:30:02] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:30:02] 🔍 Tek malzeme detayı isteniyor | Data: 35
[2025-06-27 08:30:02] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-27 08:30:02] 🚀 SQL Execute ediliyor | Data: 35
[2025-06-27 08:30:02] ✅ SQL Execute başarılı
[2025-06-27 08:30:02] 📋 Fetch sonucu | Data: Array
(
    [id] => 35
    [malz_kodu] => K5
    [malzeme_adi] => 180 2K ERZAK 3/1 ORTAK KOLİSİ
    [koli_turu] => ÖZEL ÖLÇÜ
    [birim] => 
    [fiyatAdet] => 0.00
    [fiyatMetre] => 0.00
    [fiyatM2] => 106.98
    [stok_miktar] => 1000.00
    [en_cm] => 434.00
    [boy_cm] => 2465.00
    [fiyat_adet] => 0.00
    [fiyat_m2] => 106.98
    [fiyat_metre] => 0.00
)

[2025-06-27 08:30:02] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:49:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 08:49:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 08:49:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)


)

[2025-06-27 08:49:00] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:49:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 33
)

[2025-06-27 08:49:00] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:49:00] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:49:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 6
)

[2025-06-27 08:49:00] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:49:00] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:49:00] 🔍 Tek malzeme detayı isteniyor | Data: 33
[2025-06-27 08:49:00] 🚀 SQL Execute ediliyor | Data: 33
[2025-06-27 08:49:00] ✅ SQL Execute başarılı
[2025-06-27 08:49:00] 📋 Fetch sonucu | Data: Array
(
    [id] => 33
    [malz_kodu] => S10
    [malzeme_adi] => HAM SUNTA KAPAK
    [birim] => 280
    [fiyat] => 0.022959
    [stok_miktar] => 10.00
)

[2025-06-27 08:49:00] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:49:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 57
)

[2025-06-27 08:49:00] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:49:00] 🔍 Tek malzeme detayı isteniyor | Data: 6
[2025-06-27 08:49:00] 🚀 SQL Execute ediliyor | Data: 6
[2025-06-27 08:49:00] ✅ SQL Execute başarılı
[2025-06-27 08:49:00] 📋 Fetch sonucu | Data: Array
(
    [id] => 6
    [malz_kodu] => P2
    [malzeme_adi] => 22*0.40 RENKLİ
    [birim] => METRE
    [fiyat] => 4.17
    [stok_miktar] => 1000.00
)

[2025-06-27 08:49:00] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:49:00] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:49:00] 🔍 Tek malzeme detayı isteniyor | Data: 57
[2025-06-27 08:49:00] 🚀 SQL Execute ediliyor | Data: 57
[2025-06-27 08:49:00] ✅ SQL Execute başarılı
[2025-06-27 08:49:00] 📋 Fetch sonucu | Data: Array
(
    [id] => 57
    [malz_kodu] => H25
    [malzeme_adi] => ALİMİNYUM L KULP
    [birim] => adet
    [fiyat] => 9.50
    [stok_miktar] => 1000.00
)

[2025-06-27 08:49:00] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:49:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 25
)

[2025-06-27 08:49:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:49:10] 🔍 Tek malzeme detayı isteniyor | Data: 25
[2025-06-27 08:49:10] 🚀 SQL Execute ediliyor | Data: 25
[2025-06-27 08:49:10] ✅ SQL Execute başarılı
[2025-06-27 08:49:10] 📋 Fetch sonucu | Data: Array
(
    [id] => 25
    [malz_kodu] => S3
    [malzeme_adi] => NATURAL BEYAZ SUNTA
    [birim] => 280
    [fiyat] => 0.029762
    [stok_miktar] => 10.00
)

[2025-06-27 08:49:10] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:49:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 41
)

[2025-06-27 08:49:14] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:49:14] 🔍 Tek malzeme detayı isteniyor | Data: 41
[2025-06-27 08:49:14] 🚀 SQL Execute ediliyor | Data: 41
[2025-06-27 08:49:14] ✅ SQL Execute başarılı
[2025-06-27 08:49:14] 📋 Fetch sonucu | Data: Array
(
    [id] => 41
    [malz_kodu] => MP6
    [malzeme_adi] => 10x0.8 CR BORU
    [birim] => metre
    [fiyat] => 15.00
    [stok_miktar] => 1000.00
)

[2025-06-27 08:49:14] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:49:16] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 33
)

[2025-06-27 08:49:16] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:49:16] 🔍 Tek malzeme detayı isteniyor | Data: 33
[2025-06-27 08:49:16] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-27 08:49:16] 🚀 SQL Execute ediliyor | Data: 33
[2025-06-27 08:49:16] ✅ SQL Execute başarılı
[2025-06-27 08:49:16] 📋 Fetch sonucu | Data: Array
(
    [id] => 33
    [malz_kodu] => K3
    [malzeme_adi] => 180 2K KAPAK ALT TAVA
    [koli_turu] => ÖZEL ÖLÇÜ
    [birim] => 
    [fiyatAdet] => 0.00
    [fiyatMetre] => 0.00
    [fiyatM2] => 131.32
    [stok_miktar] => 1000.00
    [en_cm] => 670.00
    [boy_cm] => 1960.00
    [fiyat_adet] => 0.00
    [fiyat_m2] => 131.32
    [fiyat_metre] => 0.00
)

[2025-06-27 08:49:16] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 08:51:31] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:51:31] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 33
)

[2025-06-27 08:51:31] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 6
)

[2025-06-27 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 57
)

[2025-06-27 08:51:31] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:51:31] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 08:51:31] 🔍 Tek malzeme detayı isteniyor | Data: 33
[2025-06-27 08:51:31] 🚀 SQL Execute ediliyor | Data: 33
[2025-06-27 08:51:31] ✅ SQL Execute başarılı
[2025-06-27 08:51:31] 📋 Fetch sonucu | Data: Array
(
    [id] => 33
    [malz_kodu] => S10
    [malzeme_adi] => HAM SUNTA KAPAK
    [birim] => 280
    [fiyat] => 0.022959
    [stok_miktar] => 10.00
)

[2025-06-27 08:51:31] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:51:31] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 08:51:31] 🔍 Tek malzeme detayı isteniyor | Data: 6
[2025-06-27 08:51:31] 🚀 SQL Execute ediliyor | Data: 6
[2025-06-27 08:51:31] ✅ SQL Execute başarılı
[2025-06-27 08:51:31] 📋 Fetch sonucu | Data: Array
(
    [id] => 6
    [malz_kodu] => P2
    [malzeme_adi] => 22*0.40 RENKLİ
    [birim] => METRE
    [fiyat] => 4.17
    [stok_miktar] => 1000.00
)

[2025-06-27 08:51:31] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:51:31] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 08:51:31] 🔍 Tek malzeme detayı isteniyor | Data: 57
[2025-06-27 08:51:31] 🚀 SQL Execute ediliyor | Data: 57
[2025-06-27 08:51:31] ✅ SQL Execute başarılı
[2025-06-27 08:51:31] 📋 Fetch sonucu | Data: Array
(
    [id] => 57
    [malz_kodu] => H25
    [malzeme_adi] => ALİMİNYUM L KULP
    [birim] => adet
    [fiyat] => 9.50
    [stok_miktar] => 1000.00
)

[2025-06-27 08:51:31] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 08:51:42] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 15
)

[2025-06-27 08:51:42] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 08:51:42] 🔍 Tek malzeme detayı isteniyor | Data: 15
[2025-06-27 08:51:42] 📦 Koli BASIT SQL sorgusu | Data: 
                    SELECT 
                        id, 
                        COALESCE(malz_kodu, CONCAT('K',id)) as malz_kodu,
                        COALESCE(malzeme_adi, 'Koli Malzeme') as malzeme_adi, 
                        COALESCE(koli_turu, '') as koli_turu, 
                        COALESCE(birim, 'adet') as birim,
                        COALESCE(olcu, 0) as fiyatAdet,
                        COALESCE(metre, 0) as fiyatMetre, 
                        COALESCE(miktar_m2, 0) as fiyatM2,
                        COALESCE(stok_miktar, 0) as stok_miktar,
                        COALESCE(en_cm, 0) as en_cm,
                        COALESCE(boy_cm, 0) as boy_cm,
                        COALESCE(olcu, 0) as fiyat_adet,
                        COALESCE(miktar_m2, 0) as fiyat_m2,
                        COALESCE(metre, 0) as fiyat_metre
                    FROM uretim_malzemeler_koli WHERE id = ? LIMIT 1
                
[2025-06-27 08:51:42] 🚀 SQL Execute ediliyor | Data: 15
[2025-06-27 08:51:42] ✅ SQL Execute başarılı
[2025-06-27 08:51:42] 📋 Fetch sonucu | Data: Array
(
    [id] => 15
    [malz_kodu] => ZK2
    [malzeme_adi] => Z KARTON 120x
    [koli_turu] => Z-KARTON
    [birim] => 
    [fiyatAdet] => 120.00
    [fiyatMetre] => 400.00
    [fiyatM2] => 0.00
    [stok_miktar] => 1.00
    [en_cm] => 0.00
    [boy_cm] => 0.00
    [fiyat_adet] => 120.00
    [fiyat_m2] => 0.00
    [fiyat_metre] => 400.00
)

[2025-06-27 08:51:42] ✅ Malzeme bulundu, JSON döndürülüyor
[2025-06-27 09:32:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 09:32:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 09:32:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 09:32:14] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 09:32:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 09:32:14] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 09:32:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 09:32:14] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 09:32:14] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 09:32:14] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 09:32:14] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 09:32:14] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 09:32:14] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 09:37:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 09:37:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 09:37:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)


[2025-06-27 09:37:43] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 09:37:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 09:37:43] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 09:37:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 09:37:43] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 09:37:43] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 09:37:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 09:37:43] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 09:37:43] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 09:37:43] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 09:39:38] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 09:39:38] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 09:39:38] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 09:39:38] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 09:39:39] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 09:39:39] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 09:39:39] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 09:39:39] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 09:39:39] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 09:39:39] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 09:39:39] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 09:39:39] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 09:39:39] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 09:39:39] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 09:42:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 09:42:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-27 09:42:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-27 09:42:00] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 09:42:00] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 09:42:00] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 09:42:00] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-27 09:42:00] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 11:55:28] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-27 11:55:28] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-27 11:55:28] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

0
)

[2025-06-27 11:55:28] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-27 11:55:28] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-27 11:55:28] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-27 11:55:28] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 01:10:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => urunler
    [id] => 0
)

[2025-06-28 01:10:56] 🏷️ Ürünler kategorisi işleniyor
[2025-06-28 01:10:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 01:10:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 01:10:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 01:10:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 01:10:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 01:10:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 01:10:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 01:10:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 01:25:15] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 01:25:15] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 01:25:15] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 01:25:15] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 01:25:15] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 01:25:15] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 01:25:15] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 01:25:15] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 01:25:15] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 01:25:19] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 01:25:19] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 01:25:19] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 01:25:19] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 01:25:19] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 01:25:19] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 01:25:19] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 01:25:19] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 01:25:19] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 01:29:33] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 01:29:33] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 01:29:33] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)


)

[2025-06-28 01:29:33] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 01:29:33] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 01:29:33] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 01:29:33] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 01:29:33] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 08:51:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 08:51:31] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 08:51:31] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 08:51:31] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 08:51:31] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 08:51:31] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 08:51:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 08:51:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 08:51:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 08:51:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 08:51:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 08:51:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 08:51:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 08:51:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 08:53:03] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 08:53:03] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)


[2025-06-28 08:53:03] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 08:53:03] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 08:53:03] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 08:53:03] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 08:53:03] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 08:53:03] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:02:50] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:02:50] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:02:50] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)


)

[2025-06-28 09:02:50] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:02:50] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:02:50] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:02:50] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:02:50] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:05:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:05:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

0
)

[2025-06-28 09:05:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:05:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:05:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:05:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:05:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:05:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:10:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:10:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:10:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:10:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:10:08] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:10:08] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:10:08] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:10:08] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:10:08] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:10:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)



[2025-06-28 09:10:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:10:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:10:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:10:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:10:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:10:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:10:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:11:13] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:11:13] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:11:13] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:11:13] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:11:13] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:11:13] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:11:13] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:11:13] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:11:13] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:21] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:21] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:21] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:21] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:21] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:21] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:21] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:21] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:21] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:23] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)



[2025-06-28 09:23:23] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:23] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:23] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:23] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:23] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:23] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:23] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:25] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:25] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:25] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:27] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:27] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:27] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:31] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:31] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:31] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:31] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:31] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:31] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:39] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:39] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:23:39] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:23:39] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:39] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:39] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:39] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:39] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:23:39] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:23:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:23:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)


)

[2025-06-28 09:23:43] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:23:43] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:23:43] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:23:43] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:23:43] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:52] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:52] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:52] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:52] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:52] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:53] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:53] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:53] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:53] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)


)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:55] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:55] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:55] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:56] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:56] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:56] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:24:57] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:24:57] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:24:57] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:06] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:06] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:06] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:06] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:06] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:06] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:06] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:06] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:06] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

0
)

[2025-06-28 09:29:08] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:08] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:08] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:08] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:08] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:08] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:08] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:08] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:08] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:08] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:09] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:09] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:09] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:10] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:10] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:10] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:11] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:11] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:11] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:11] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:11] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:11] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:11] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:11] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:46] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:29:46] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)


)

[2025-06-28 09:29:46] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:29:46] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:29:46] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:29:46] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:29:46] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:41] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:41] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:41] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:41] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:41] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:41] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:41] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:41] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:41] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:43] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:43] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:43] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:43] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:43] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:43] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:44] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:44] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:44] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:45] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-28 09:36:45] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:45] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-28 09:36:46] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:46] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-28 09:36:46] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:46] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-28 09:36:46] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-28 09:36:46] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-28 09:36:46] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-28 09:36:46] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-28 09:36:46] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-28 09:36:46] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:24] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:24] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:24] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)


)

[2025-06-29 22:23:24] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:24] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:24] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:24] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:24] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)



[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => sunta
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => pvc
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => hirdavat
    [id] => 0
)

[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_sunta
[2025-06-29 22:23:26] 🔍 API Çağrısı | Data: Array
(
    [kategori] => koli
    [id] => 0
)

[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_pvc
[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_hirdavat
[2025-06-29 22:23:26] 📊 Tablo | Data: uretim_malzemeler_koli
[2025-06-29 22:23:26] 📦 Koli liste SQL | Data: SELECT id, malz_kodu, malzeme_adi, koli_turu, birim, stok_miktar, 0 as stok_adet, birim_fiyat_m2, birim_fiyat_adet, olcu, metre, miktar_m2 FROM uretim_malzemeler_koli ORDER BY id
