<?php
// ajax/uretim_plan_kaydet.php - Reçete Tabanlı Üretim Planı Kaydet
require_once '../includes/db.php';

header('Content-Type: application/json');

// JSON verisini al
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['recete_id']) || !isset($input['adet']) || !isset($input['baslangic_tarihi'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Zorunlu alanlar eksik'
    ]);
    exit;
}

$recete_id = (int)$input['recete_id'];
$adet = (int)$input['adet'];
$baslangic_tarihi = $input['baslangic_tarihi'];
$teslim_tarihi = $input['teslim_tarihi'] ?? null;
$oncelik = $input['oncelik'] ?? 'normal';
$siparis_no = $input['siparis_no'] ?? null;
$notlar = $input['notlar'] ?? '';
$recete_kodu = $input['recete_kodu'] ?? null;
$malzemeler = $input['malzemeler'] ?? [];

// Öncelik dönüştürme
$oncelik_enum = '2'; // normal
switch ($oncelik) {
    case 'acil':
        $oncelik_enum = '3';
        break;
    case 'yuksek':
        $oncelik_enum = '3';
        break;
    case 'dusuk':
        $oncelik_enum = '1';
        break;
    default:
        $oncelik_enum = '2';
}

try {
    $db->beginTransaction();

    // Reçete var mı kontrol et
    $stmt = $db->prepare("
        SELECT 
            r.id, 
            r.recete_kodu, 
            r.urun,
            u.stok_adi
        FROM receteler r 
        LEFT JOIN urunler u ON r.urun_id = u.id 
        WHERE r.id = ?
    ");
    $stmt->execute([$recete_id]);
    $recete = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$recete) {
        throw new Exception('Reçete bulunamadı');
    }

    // Reçeteye ait ürün ID'sini al (varsa)
    $urun_id = null;
    if ($recete['stok_adi']) {
        $stmt = $db->prepare("SELECT id FROM urunler WHERE stok_adi = ? LIMIT 1");
        $stmt->execute([$recete['stok_adi']]);
        $urun = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($urun) {
            $urun_id = $urun['id'];
        }
    }

    // Üretim planını kaydet (reçete_id ile)
    $stmt = $db->prepare("
        INSERT INTO uretim_planlari 
        (urun_id, recete_id, miktar, baslangic_tarihi, bitis_tarihi, oncelik, durum, siparis_no, notlar, recete_kodu, olusturma_tarihi)
        VALUES (?, ?, ?, ?, ?, ?, 'planlandi', ?, ?, ?, NOW())
    ");
    $stmt->execute([
        $urun_id, 
        $recete_id,
        $adet, 
        $baslangic_tarihi, 
        $teslim_tarihi, 
        $oncelik_enum, 
        $siparis_no, 
        $notlar, 
        $recete_kodu
    ]);

    $plan_id = $db->lastInsertId();

    // Malzeme rezervasyonları (eğer malzeme listesi varsa)
    if (!empty($malzemeler)) {
        foreach ($malzemeler as $malzeme) {
            if (isset($malzeme['malzeme_id']) && $malzeme['malzeme_id']) {
                // Malzeme rezervasyonu kaydet
                $stmt = $db->prepare("
                    INSERT INTO malzeme_rezervasyonlari 
                    (plan_id, malzeme_id, miktar, rezervasyon_tarihi)
                    VALUES (?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $plan_id, 
                    $malzeme['malzeme_id'], 
                    $malzeme['toplam_gereken']
                ]);
            }
        }
    }

    // Varsayılan üretim aşamalarını oluştur
    $varsayilan_asamalar = [
        // Otomasyon aşamaları
        ['asama_adi' => 'Hazırlık', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
        ['asama_adi' => 'Kesim', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
        ['asama_adi' => 'Bant-Delik', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
        ['asama_adi' => 'Kalite Kontrol', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
        ['asama_adi' => 'Paket', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
        // Manuel hat aşamaları
        ['asama_adi' => 'Hazırlık', 'tip' => 'manuel', 'durum' => 'bekliyor'],
        ['asama_adi' => 'Kesim', 'tip' => 'manuel', 'durum' => 'bekliyor'],
        ['asama_adi' => 'Bant', 'tip' => 'manuel', 'durum' => 'bekliyor'],
        ['asama_adi' => 'Delik', 'tip' => 'manuel', 'durum' => 'bekliyor'],
        ['asama_adi' => 'Kalite Kontrol-Otomasyon Birleşim', 'tip' => 'manuel', 'durum' => 'bekliyor']
    ];

    foreach ($varsayilan_asamalar as $asama) {
        $stmt = $db->prepare("
            INSERT INTO uretim_asamalari (plan_id, asama_adi, durum, tip, olusturma_tarihi)
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$plan_id, $asama['asama_adi'], $asama['durum'], $asama['tip']]);
    }

    $db->commit();

    $plan_adi = $recete['urun'] ?: $recete['stok_adi'] ?: $recete['recete_kodu'];

    echo json_encode([
        'success' => true,
        'message' => 'Reçete tabanlı üretim planı başarıyla kaydedildi',
        'plan_id' => $plan_id,
        'recete_kodu' => $recete['recete_kodu'],
        'plan_adi' => $plan_adi,
        'adet' => $adet
    ]);

} catch (Exception $e) {
    $db->rollBack();
    echo json_encode([
        'success' => false,
        'message' => 'Kayıt hatası: ' . $e->getMessage()
    ]);
}
?> 