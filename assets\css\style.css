/* Stok & Barkod Takip Sistemi - Özel CSS Stilleri */

body {
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.footer {
    margin-top: auto;
}

/* Navbar */
.navbar-brand {
    font-weight: 600;
}

/* Kartlar */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Tablolar */
.table th {
    font-weight: 600;
    white-space: nowrap;
}

/* Bar<PERSON>d tarayıcı */
#scanner-container {
    position: relative;
    min-height: 300px;
    overflow: hidden;
}

#scanner-container video {
    width: 100%;
    height: auto;
}

#scanner-container canvas {
    position: absolute;
    top: 0;
    left: 0;
}

/* Barkod gösterimi */
#barcodeImage {
    max-width: 100%;
    height: auto;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON> stilleri */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        background-color: #fff;
    }
    
    .card {
        box-shadow: none;
        border: none;
    }
    
    .container {
        max-width: 100%;
        width: 100%;
    }
}

/* Animasyonlar */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Mobil uyumluluk */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Özel renkler */
.bg-light-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.bg-light-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

.bg-light-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.bg-light-info {
    background-color: rgba(23, 162, 184, 0.1);
}

/* Barkod gösterimi */
.barcode-container {
    text-align: center;
    padding: 1rem;
    background-color: #fff;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

/* Yükleme animasyonu */
.loading {
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #0d6efd;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
} 