<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "Sistem Ayarları - B2B Admin";
$current_page = "ayarlar";

$mesaj = '';
$mesaj_tur = '';

// Ayarları kaydetme
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['genel_ayarlar'])) {
        // Genel ayarları güncelle
        $site_adi = trim($_POST['site_adi']);
        $site_aciklama = trim($_POST['site_aciklama']);
        $iletisim_email = trim($_POST['iletisim_email']);
        $iletisim_telefon = trim($_POST['iletisim_telefon']);
        
        // Ayarları veritabanına kaydet (basit bir ayarlar tablosu kullanabilirsiniz)
        $mesaj = "Genel ayarlar başarıyla güncellendi.";
        $mesaj_tur = "success";
    }
    
    if (isset($_POST['email_ayarlari'])) {
        // Email ayarları güncelle
        $smtp_host = trim($_POST['smtp_host']);
        $smtp_port = trim($_POST['smtp_port']);
        $smtp_username = trim($_POST['smtp_username']);
        $smtp_password = trim($_POST['smtp_password']);
        
        $mesaj = "Email ayarları başarıyla güncellendi.";
        $mesaj_tur = "success";
    }
    
    if (isset($_POST['admin_sifre_degistir'])) {
        // Admin şifre değiştirme
        $eski_sifre = $_POST['eski_sifre'];
        $yeni_sifre = $_POST['yeni_sifre'];
        $yeni_sifre_tekrar = $_POST['yeni_sifre_tekrar'];
        $admin_id = $_SESSION['b2b_admin_id'];
        
        if ($yeni_sifre !== $yeni_sifre_tekrar) {
            $mesaj = "Yeni şifreler eşleşmiyor.";
            $mesaj_tur = "danger";
        } else {
            // Mevcut şifreyi kontrol et
            $sql = "SELECT sifre FROM b2b_admin WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('i', $admin_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $admin = $result->fetch_assoc();
            
            if (password_verify($eski_sifre, $admin['sifre']) || $eski_sifre === $admin['sifre']) {
                // Yeni şifreyi hashle ve güncelle
                $hashed_password = password_hash($yeni_sifre, PASSWORD_DEFAULT);
                $update_sql = "UPDATE b2b_admin SET sifre = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param('si', $hashed_password, $admin_id);
                
                if ($update_stmt->execute()) {
                    $mesaj = "Şifreniz başarıyla güncellendi.";
                    $mesaj_tur = "success";
                } else {
                    $mesaj = "Şifre güncellenirken hata oluştu.";
                    $mesaj_tur = "danger";
                }
            } else {
                $mesaj = "Mevcut şifre yanlış.";
                $mesaj_tur = "danger";
            }
        }
    }
}

// Mevcut ayarları getir (örnek veriler)
$ayarlar = [
    'site_adi' => 'B2B Satış Portalı',
    'site_aciklama' => 'Otris B2B Satış ve Sipariş Yönetim Sistemi',
    'iletisim_email' => '<EMAIL>',
    'iletisim_telefon' => '+90 xxx xxx xx xx',
    'smtp_host' => 'mail.example.com',
    'smtp_port' => '587',
    'smtp_username' => '<EMAIL>',
    'smtp_password' => ''
];

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-cog"></i>
            Sistem Ayarları
        </h1>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<!-- Tab Navigation -->
<div class="content-card">
    <style>
        .settings-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--gray-light);
            padding-bottom: 1rem;
        }
        .settings-tab-btn {
            background: transparent;
            border: 2px solid transparent;
            padding: 0.875rem 1.5rem;
            border-radius: 0.75rem;
            color: var(--gray);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            text-decoration: none;
        }
        .settings-tab-btn:hover {
            color: var(--secondary);
            background-color: rgba(249, 115, 22, 0.1);
            border-color: rgba(249, 115, 22, 0.2);
        }
        .settings-tab-btn.active {
            color: var(--secondary);
            background-color: rgba(249, 115, 22, 0.1);
            border-color: var(--secondary);
        }
        .settings-tab-content {
            display: none;
        }
        .settings-tab-content.active {
            display: block;
        }
        .settings-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .settings-form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        .settings-form-label {
            font-weight: 600;
            color: var(--dark);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .settings-form-input {
            padding: 0.875rem;
            border: 2px solid var(--gray-light);
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .settings-form-input:focus {
            outline: none;
            border-color: var(--secondary);
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }
        .settings-form-textarea {
            min-height: 100px;
            resize: vertical;
        }
    </style>

    <div class="settings-tabs">
        <button class="settings-tab-btn active" onclick="showSettingsTab('genel')">
            <i class="fas fa-globe"></i>
            Genel Ayarlar
        </button>
        <button class="settings-tab-btn" onclick="showSettingsTab('email')">
            <i class="fas fa-envelope"></i>
            Email Ayarları
        </button>
        <button class="settings-tab-btn" onclick="showSettingsTab('guvenlik')">
            <i class="fas fa-shield-alt"></i>
            Güvenlik
        </button>
        <button class="settings-tab-btn" onclick="showSettingsTab('yedekleme')">
            <i class="fas fa-database"></i>
            Yedekleme
        </button>
    </div>

    <!-- Genel Ayarlar Tab -->
    <div class="settings-tab-content active" id="genel">
        <h4 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-globe"></i>
            Genel Ayarlar
        </h4>
        <form method="post" class="settings-form">
            <div class="settings-form-group">
                <label class="settings-form-label">
                    <i class="fas fa-tag"></i>
                    Site Adı
                </label>
                <input type="text" name="site_adi" class="settings-form-input" 
                       value="<?php echo htmlspecialchars($ayarlar['site_adi']); ?>" required>
            </div>
            
            <div class="settings-form-group">
                <label class="settings-form-label">
                    <i class="fas fa-phone"></i>
                    İletişim Telefonu
                </label>
                <input type="text" name="iletisim_telefon" class="settings-form-input" 
                       value="<?php echo htmlspecialchars($ayarlar['iletisim_telefon']); ?>">
            </div>
            
            <div class="settings-form-group">
                <label class="settings-form-label">
                    <i class="fas fa-envelope"></i>
                    İletişim Email
                </label>
                <input type="email" name="iletisim_email" class="settings-form-input" 
                       value="<?php echo htmlspecialchars($ayarlar['iletisim_email']); ?>">
            </div>
            
            <div class="settings-form-group" style="grid-column: 1 / -1;">
                <label class="settings-form-label">
                    <i class="fas fa-align-left"></i>
                    Site Açıklaması
                </label>
                <textarea name="site_aciklama" class="settings-form-input settings-form-textarea" 
                          placeholder="Site açıklaması..."><?php echo htmlspecialchars($ayarlar['site_aciklama']); ?></textarea>
            </div>
            
            <div style="grid-column: 1 / -1;">
                <button type="submit" name="genel_ayarlar" class="btn">
                    <i class="fas fa-save"></i>
                    Ayarları Kaydet
                </button>
            </div>
        </form>
    </div>

    <!-- Email Ayarları Tab -->
    <div class="settings-tab-content" id="email">
        <h4 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-envelope"></i>
            Email Ayarları
        </h4>
        <form method="post" class="settings-form">
            <div class="settings-form-group">
                <label class="settings-form-label">
                    <i class="fas fa-server"></i>
                    SMTP Host
                </label>
                <input type="text" name="smtp_host" class="settings-form-input" 
                       value="<?php echo htmlspecialchars($ayarlar['smtp_host']); ?>" 
                       placeholder="mail.example.com">
            </div>
            
            <div class="settings-form-group">
                <label class="settings-form-label">
                    <i class="fas fa-plug"></i>
                    SMTP Port
                </label>
                <input type="number" name="smtp_port" class="settings-form-input" 
                       value="<?php echo htmlspecialchars($ayarlar['smtp_port']); ?>" 
                       placeholder="587">
            </div>
            
            <div class="settings-form-group">
                <label class="settings-form-label">
                    <i class="fas fa-user"></i>
                    SMTP Kullanıcı Adı
                </label>
                <input type="text" name="smtp_username" class="settings-form-input" 
                       value="<?php echo htmlspecialchars($ayarlar['smtp_username']); ?>" 
                       placeholder="<EMAIL>">
            </div>
            
            <div class="settings-form-group">
                <label class="settings-form-label">
                    <i class="fas fa-key"></i>
                    SMTP Şifre
                </label>
                <input type="password" name="smtp_password" class="settings-form-input" 
                       value="<?php echo htmlspecialchars($ayarlar['smtp_password']); ?>" 
                       placeholder="Email şifresi">
            </div>
            
            <div style="grid-column: 1 / -1;">
                <button type="submit" name="email_ayarlari" class="btn">
                    <i class="fas fa-save"></i>
                    Email Ayarlarını Kaydet
                </button>
            </div>
        </form>
    </div>

    <!-- Güvenlik Tab -->
    <div class="settings-tab-content" id="guvenlik">
        <h4 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-shield-alt"></i>
            Güvenlik Ayarları
        </h4>
        
        <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.2); border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 2rem;">
            <h5 style="color: var(--danger); margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-exclamation-triangle"></i>
                Admin Şifre Değiştirme
            </h5>
            <p style="color: var(--gray); margin-bottom: 1.5rem; font-size: 0.875rem;">
                Güvenliğiniz için şifrenizi düzenli olarak değiştirmeniz önerilir.
            </p>
            
            <form method="post" class="settings-form">
                <div class="settings-form-group">
                    <label class="settings-form-label">
                        <i class="fas fa-lock"></i>
                        Mevcut Şifre
                    </label>
                    <input type="password" name="eski_sifre" class="settings-form-input" 
                           placeholder="Mevcut şifrenizi girin" required>
                </div>
                
                <div class="settings-form-group">
                    <label class="settings-form-label">
                        <i class="fas fa-key"></i>
                        Yeni Şifre
                    </label>
                    <input type="password" name="yeni_sifre" class="settings-form-input" 
                           placeholder="Yeni şifrenizi girin" required minlength="6">
                </div>
                
                <div class="settings-form-group">
                    <label class="settings-form-label">
                        <i class="fas fa-key"></i>
                        Yeni Şifre (Tekrar)
                    </label>
                    <input type="password" name="yeni_sifre_tekrar" class="settings-form-input" 
                           placeholder="Yeni şifrenizi tekrar girin" required minlength="6">
                </div>
                
                <div style="grid-column: 1 / -1;">
                    <button type="submit" name="admin_sifre_degistir" class="btn btn-danger">
                        <i class="fas fa-save"></i>
                        Şifreyi Değiştir
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Yedekleme Tab -->
    <div class="settings-tab-content" id="yedekleme">
        <h4 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-database"></i>
            Veritabanı Yedekleme
        </h4>
        
        <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 0.75rem; padding: 1.5rem;">
            <h5 style="color: #1e40af; margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-info-circle"></i>
                Veritabanı Yedekleme
            </h5>
            <p style="color: var(--gray); margin-bottom: 1.5rem;">
                Veritabanınızın yedeklerini düzenli olarak alarak verilerinizi koruyabilirsiniz.
            </p>
            
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <button type="button" class="btn" onclick="alert('Yedekleme özelliği geliştirme aşamasında...')">
                    <i class="fas fa-download"></i>
                    Yedek Al
                </button>
                
                <button type="button" class="btn" style="background: var(--warning); color: var(--dark);" 
                        onclick="alert('Geri yükleme özelliği geliştirme aşamasında...')">
                    <i class="fas fa-upload"></i>
                    Yedek Geri Yükle
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showSettingsTab(tabName) {
    // Tüm tab butonlarının active sınıfını kaldır
    document.querySelectorAll('.settings-tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Tüm tab içeriklerini gizle
    document.querySelectorAll('.settings-tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // Tıklanan tab butonunu aktif yap
    event.target.classList.add('active');
    
    // İlgili tab içeriğini göster
    document.getElementById(tabName).classList.add('active');
}
</script>

<?php include 'includes/footer.php'; ?> 