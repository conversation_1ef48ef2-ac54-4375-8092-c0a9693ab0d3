<?php
session_start();
include '../config.php';

$page_title = "Müşteri Düzenle - B2B Admin";
$current_page = "musteriler";

$mesaj = '';
$mesaj_tur = '';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Müşteri ID kontrolü
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: musteriler.php');
    exit;
}

$musteri_id = intval($_GET['id']);

// Form gönderildi mi kontrolü
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $otris_yetkisi = isset($_POST['otris_yetkisi']) ? 1 : 0;
    $avmpark_yetkisi = isset($_POST['avmpark_yetkisi']) ? 1 : 0;
    
    // Yetkileri güncelle
    $update_sql = "UPDATE cari_firmalar SET otris_yetkisi = ?, avmpark_yetkisi = ? WHERE id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param('iii', $otris_yetkisi, $avmpark_yetkisi, $musteri_id);
    
    if ($update_stmt->execute()) {
        $mesaj = "Müşteri yetkileri başarıyla güncellendi.";
        $mesaj_tur = "success";
    } else {
        $mesaj = "Yetkiler güncellenirken hata oluştu: " . $conn->error;
        $mesaj_tur = "danger";
    }
}

// Müşteri bilgilerini çek
$sql = "SELECT * FROM cari_firmalar WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $musteri_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: musteriler.php');
    exit;
}

$musteri = $result->fetch_assoc();

include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-user-edit"></i>
            Müşteri Düzenle: <?php echo htmlspecialchars($musteri['firm_name']); ?>
        </h1>
        <a href="musteriler.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Geri Dön
        </a>
    </div>
</div>

<?php if (!empty($mesaj)): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <form method="POST" action="">
        <div class="form-section">
            <h3><i class="fas fa-info-circle"></i> Firma Bilgileri</h3>
            <div class="form-grid">
                <div class="form-group">
                    <label>Firma Adı</label>
                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($musteri['firm_name']); ?>" readonly>
                </div>
                <div class="form-group">
                    <label>E-posta</label>
                    <input type="email" class="form-control" value="<?php echo htmlspecialchars($musteri['email'] ?? ''); ?>" readonly>
                </div>
                <div class="form-group">
                    <label>Telefon</label>
                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($musteri['telefon'] ?? ''); ?>" readonly>
                </div>
                <div class="form-group">
                    <label>B2B Durum</label>
                    <span class="status-badge <?php echo $musteri['b2b_aktif'] ? 'status-active' : 'status-inactive'; ?>">
                        <i class="fas fa-<?php echo $musteri['b2b_aktif'] ? 'check-circle' : 'times-circle'; ?>"></i>
                        <?php echo $musteri['b2b_aktif'] ? 'Aktif' : 'Pasif'; ?>
                    </span>
                </div>
            </div>
        </div>

        <div class="form-section">
            <h3><i class="fas fa-key"></i> Ürün Yetkileri</h3>
            <div class="permission-grid">
                <div class="permission-card">
                    <div class="permission-header">
                        <h4><i class="fas fa-building"></i> Otris Bayilik</h4>
                        <div class="permission-toggle">
                            <input type="checkbox" id="otris_yetkisi" name="otris_yetkisi" value="1" 
                                   <?php echo $musteri['otris_yetkisi'] ? 'checked' : ''; ?>
                                   class="toggle-checkbox">
                            <label for="otris_yetkisi" class="toggle-label">
                                <span class="toggle-inner"></span>
                                <span class="toggle-switch"></span>
                            </label>
                        </div>
                    </div>
                    <div class="permission-description">
                        <p><strong>T ile başlayan</strong> takip kodlu ürünler</p>
                        <p>Fiyat görme yetkisi dahil</p>
                        <p>Normal bayi müşterisi</p>
                    </div>
                </div>

                <div class="permission-card">
                    <div class="permission-header">
                        <h4><i class="fas fa-store"></i> AVMPARK</h4>
                        <div class="permission-toggle">
                            <input type="checkbox" id="avmpark_yetkisi" name="avmpark_yetkisi" value="1" 
                                   <?php echo $musteri['avmpark_yetkisi'] ? 'checked' : ''; ?>
                                   class="toggle-checkbox">
                            <label for="avmpark_yetkisi" class="toggle-label">
                                <span class="toggle-inner"></span>
                                <span class="toggle-switch"></span>
                            </label>
                        </div>
                    </div>
                    <div class="permission-description">
                        <p><strong>P ile başlayan</strong> takip kodlu ürünler</p>
                        <p>Özel koleksiyon ürünleri</p>
                        <p>AVM ve perakende müşterisi</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn">
                <i class="fas fa-save"></i>
                Yetkileri Güncelle
            </button>
            <a href="musteriler.php" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                İptal
            </a>
        </div>
    </form>
</div>

<style>
.form-section {
    background: white;
    border-radius: 0.75rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border: 1px solid var(--gray-light);
}

.form-section h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    color: var(--dark);
    font-size: 1.25rem;
    font-weight: 600;
    border-bottom: 2px solid var(--gray-light);
    padding-bottom: 0.75rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark);
}

.permission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.permission-card {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
    border: 2px solid var(--gray-light);
    transition: all 0.3s ease;
}

.permission-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--secondary);
}

.permission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.permission-header h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    color: var(--dark);
    font-size: 1.1rem;
    font-weight: 700;
}

.permission-description p {
    margin: 0.5rem 0;
    color: var(--gray);
    font-size: 0.9rem;
}

.permission-description p:first-child {
    font-weight: 600;
    color: var(--secondary);
}

/* Toggle Switch */
.permission-toggle {
    position: relative;
}

.toggle-checkbox {
    display: none;
}

.toggle-label {
    display: block;
    width: 60px;
    height: 30px;
    background: #ddd;
    border-radius: 30px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-inner {
    display: block;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, #ff4444, #44ff44);
    border-radius: 30px;
    position: absolute;
    opacity: 0;
    transition: all 0.3s ease;
}

.toggle-switch {
    display: block;
    width: 26px;
    height: 26px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.toggle-checkbox:checked + .toggle-label .toggle-inner {
    opacity: 1;
}

.toggle-checkbox:checked + .toggle-label .toggle-switch {
    left: 32px;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.status-active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-inactive {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-light);
}

@media (max-width: 768px) {
    .permission-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/footer.php'; ?> 