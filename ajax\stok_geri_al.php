<?php
require_once '../config/database.php';

header('Content-Type: application/json');

if (!isset($_POST['hareket_idleri'])) {
    echo json_encode(['success' => false, 'message' => 'Hareket ID\'leri belirtilmedi']);
    exit;
}

$hareket_idleri = explode(',', $_POST['hareket_idleri']);

try {
    $conn->begin_transaction();
    
    // En eski hareketi bul (ilk çıkışı)
    $ilk_hareket_id = min($hareket_idleri);
    
    // Diğer hareketleri iptal et
    foreach ($hareket_idleri as $hareket_id) {
        if ($hareket_id != $ilk_hareket_id) {
            // Stok hareketini güncelle
            $sql = "UPDATE stok_hareketleri SET islem_turu = 'iptal', aciklama = CONCAT(aciklama, ' [İPTAL EDİLDİ]') WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $hareket_id);
            $stmt->execute();
            
            // Stok çıkışını sil
            $sql = "DELETE FROM stok_cikislari WHERE stok_hareket_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $hareket_id);
            $stmt->execute();
        }
    }
    
    $conn->commit();
    echo json_encode(['success' => true, 'message' => 'İşlem başarılı']);
    
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
} 