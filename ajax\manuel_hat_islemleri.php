<?php
// ajax/manuel_hat_islemleri.php - <PERSON> İşlemleri
require_once '../includes/db.php';

header('Content-Type: application/json');

if (!isset($_POST['action']) || !isset($_POST['plan_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Eksik parametreler'
    ]);
    exit;
}

$action = $_POST['action'];
$plan_id = (int)$_POST['plan_id'];

if ($plan_id <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Geçersiz plan ID'
    ]);
    exit;
}

try {
    $db->beginTransaction();

    // Plan var mı kontrol et
    $stmt = $db->prepare("SELECT id FROM uretim_planlari WHERE id = ?");
    $stmt->execute([$plan_id]);
    
    if (!$stmt->fetch()) {
        throw new Exception('Plan bulunamadı');
    }

    switch ($action) {
        case 'hat_temizle':
            // <PERSON> hat aşamalarını sıfırla
            $stmt = $db->prepare("
                UPDATE uretim_asamalari 
                SET durum = 'bekliyor', notlar = CONCAT(IFNULL(notlar, ''), '\n[', NOW(), '] Hat temizlendi'), guncelleme_tarihi = NOW()
                WHERE plan_id = ? AND tip = 'manuel'
            ");
            $stmt->execute([$plan_id]);
            $message = 'Manuel hat temizlendi';
            break;

        case 'mukerrer_temizle':
            // Mükerrer aşamaları temizle
            $stmt = $db->prepare("
                DELETE ua1 FROM uretim_asamalari ua1
                INNER JOIN uretim_asamalari ua2 
                WHERE ua1.plan_id = ? AND ua2.plan_id = ? 
                AND ua1.asama_adi = ua2.asama_adi 
                AND ua1.tip = ua2.tip 
                AND ua1.id > ua2.id
            ");
            $stmt->execute([$plan_id, $plan_id]);
            $message = 'Mükerrer aşamalar temizlendi';
            break;

        case 'hizli_temizlik':
            // Tüm aşamaları bekliyor durumuna getir
            $stmt = $db->prepare("
                UPDATE uretim_asamalari 
                SET durum = 'bekliyor', notlar = CONCAT(IFNULL(notlar, ''), '\n[', NOW(), '] Hızlı temizlik yapıldı'), guncelleme_tarihi = NOW()
                WHERE plan_id = ?
            ");
            $stmt->execute([$plan_id]);
            $message = 'Tüm aşamalar sıfırlandı';
            break;

        case 'asama_sifirla':
            // Sadece aşamaları sıfırla, planı koru
            $stmt = $db->prepare("DELETE FROM uretim_asamalari WHERE plan_id = ?");
            $stmt->execute([$plan_id]);
            
            // Varsayılan aşamaları yeniden oluştur
            $varsayilan_asamalar = [
                ['asama_adi' => 'Hazırlık', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Kesim', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Bant-Delik', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Kalite Kontrol', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Paket', 'tip' => 'otomasyon', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Hazırlık', 'tip' => 'manuel', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Kesim', 'tip' => 'manuel', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Bant', 'tip' => 'manuel', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Delik', 'tip' => 'manuel', 'durum' => 'bekliyor'],
                ['asama_adi' => 'Kalite Kontrol-Otomasyon Birleşim', 'tip' => 'manuel', 'durum' => 'bekliyor']
            ];

            foreach ($varsayilan_asamalar as $asama) {
                $stmt = $db->prepare("
                    INSERT INTO uretim_asamalari (plan_id, asama_adi, durum, tip, olusturma_tarihi)
                    VALUES (?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$plan_id, $asama['asama_adi'], $asama['durum'], $asama['tip']]);
            }
            $message = 'Aşamalar sıfırlandı ve yeniden oluşturuldu';
            break;

        case 'plan_kopyala':
            // Planı kopyala
            $stmt = $db->prepare("
                SELECT * FROM uretim_planlari WHERE id = ?
            ");
            $stmt->execute([$plan_id]);
            $original_plan = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$original_plan) {
                throw new Exception('Kopyalanacak plan bulunamadı');
            }

            // Yeni plan oluştur
            $stmt = $db->prepare("
                INSERT INTO uretim_planlari 
                (urun_id, miktar, baslangic_tarihi, bitis_tarihi, oncelik, durum, siparis_no, notlar, takip_kodu, olusturma_tarihi)
                VALUES (?, ?, DATE_ADD(?, INTERVAL 1 DAY), DATE_ADD(?, INTERVAL 1 DAY), ?, 'planlandi', ?, CONCAT(?, '\n[KOPYA] Orijinal Plan ID: ', ?), ?, NOW())
            ");
            $stmt->execute([
                $original_plan['urun_id'],
                $original_plan['miktar'],
                $original_plan['baslangic_tarihi'],
                $original_plan['bitis_tarihi'],
                $original_plan['oncelik'],
                $original_plan['siparis_no'] . '-KOPYA',
                $original_plan['notlar'],
                $plan_id,
                $original_plan['takip_kodu']
            ]);

            $new_plan_id = $db->lastInsertId();

            // Aşamaları da kopyala
            $stmt = $db->prepare("
                INSERT INTO uretim_asamalari (plan_id, asama_adi, durum, tip, notlar, olusturma_tarihi)
                SELECT ?, asama_adi, 'bekliyor', tip, CONCAT('[KOPYA] ', IFNULL(notlar, '')), NOW()
                FROM uretim_asamalari WHERE plan_id = ?
            ");
            $stmt->execute([$new_plan_id, $plan_id]);

            $message = 'Plan başarıyla kopyalandı (Yeni Plan ID: ' . $new_plan_id . ')';
            break;

        default:
            throw new Exception('Geçersiz işlem');
    }

    // Plan durumunu güncelle (kopyala hariç)
    if ($action !== 'plan_kopyala') {
        $stmt = $db->prepare("UPDATE uretim_planlari SET durum = 'planlandi' WHERE id = ?");
        $stmt->execute([$plan_id]);
    }

    $db->commit();

    echo json_encode([
        'success' => true,
        'message' => $message,
        'action' => $action,
        'plan_id' => $plan_id
    ]);

} catch (Exception $e) {
    $db->rollBack();
    echo json_encode([
        'success' => false,
        'message' => 'İşlem hatası: ' . $e->getMessage()
    ]);
}
?> 