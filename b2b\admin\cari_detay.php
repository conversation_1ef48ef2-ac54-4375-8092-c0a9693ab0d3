<?php
session_start();
include '../config.php';

// Admin giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header("Location: index.php");
    exit;
}

// Page bilgileri
$page_title = "Cari Detay - B2B Admin";
$current_page = "musteriler";

$cari_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($cari_id <= 0) {
    header("Location: musteriler.php");
    exit;
}

$mesaj = '';
$mesaj_tipi = '';

// Cari bilgilerini getir
$sql = "SELECT * FROM cari_firmalar WHERE id = ? AND b2b_aktif = 1";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $cari_id);
$stmt->execute();
$cari = $stmt->get_result()->fetch_assoc();

if (!$cari) {
    header("Location: musteriler.php");
    exit;
}

// Cari ayarlarını getir veya oluştur
$sql = "SELECT * FROM b2b_cari_ayarlar WHERE cari_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $cari_id);
$stmt->execute();
$cari_ayarlar = $stmt->get_result()->fetch_assoc();

if (!$cari_ayarlar) {
    // İlk kez giriyorsa default ayarları oluştur
    $sql = "INSERT INTO b2b_cari_ayarlar (cari_id) VALUES (?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $cari_id);
    $stmt->execute();
    
    // Tekrar getir
    $sql = "SELECT * FROM b2b_cari_ayarlar WHERE cari_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $cari_id);
    $stmt->execute();
    $cari_ayarlar = $stmt->get_result()->fetch_assoc();
}

// Cari bakiye bilgilerini getir
$sql = "SELECT * FROM b2b_cari_bakiye WHERE cari_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $cari_id);
$stmt->execute();
$bakiye_bilgi = $stmt->get_result()->fetch_assoc();

// Form işlemleri
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Cari ayarları güncelleme
    if (isset($_POST['ayarlar_guncelle'])) {
        $iskonto_orani = floatval($_POST['iskonto_orani']);
        $ek_indirim_orani = floatval($_POST['ek_indirim_orani']);
        $kredi_limiti = floatval($_POST['kredi_limiti']);
        $odeme_vadesi = intval($_POST['odeme_vadesi']);
        
        $sql = "UPDATE b2b_cari_ayarlar SET iskonto_orani = ?, ek_indirim_orani = ?, kredi_limiti = ?, odeme_vadesi = ? WHERE cari_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('dddii', $iskonto_orani, $ek_indirim_orani, $kredi_limiti, $odeme_vadesi, $cari_id);
        
        if ($stmt->execute()) {
            $mesaj = "Cari ayarları başarıyla güncellendi.";
            $mesaj_tipi = "success";
            // Güncel bilgileri tekrar getir
            $sql = "SELECT * FROM b2b_cari_ayarlar WHERE cari_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('i', $cari_id);
            $stmt->execute();
            $cari_ayarlar = $stmt->get_result()->fetch_assoc();
        } else {
            $mesaj = "Güncelleme sırasında hata oluştu.";
            $mesaj_tipi = "danger";
        }
    }
    
    // Cari hareket ekleme
    if (isset($_POST['hareket_ekle'])) {
        $hareket_tipi = $_POST['hareket_tipi'];
        $tutar = floatval($_POST['tutar']);
        $aciklama = $_POST['aciklama'];
        $belge_no = $_POST['belge_no'];
        $vade_tarihi = $_POST['vade_tarihi'] ?: null;
        $islem_tarihi = $_POST['islem_tarihi'];
        $admin_id = $_SESSION['b2b_admin_id'] ?? 1;
        
        $sql = "INSERT INTO b2b_cari_hareketler (cari_id, hareket_tipi, tutar, aciklama, belge_no, vade_tarihi, islem_tarihi, created_by, referans_tip) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'manuel')";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('isdssssi', $cari_id, $hareket_tipi, $tutar, $aciklama, $belge_no, $vade_tarihi, $islem_tarihi, $admin_id);
        
        if ($stmt->execute()) {
            $mesaj = "Cari hareket başarıyla eklendi.";
            $mesaj_tipi = "success";
        } else {
            $mesaj = "Hareket ekleme sırasında hata oluştu.";
            $mesaj_tipi = "danger";
        }
    }
    
    // Ödeme ekleme
    if (isset($_POST['odeme_ekle'])) {
        $odeme_tipi = $_POST['odeme_tipi'];
        $tutar = floatval($_POST['odeme_tutar']);
        $aciklama = $_POST['odeme_aciklama'];
        $banka_bilgisi = $_POST['banka_bilgisi'];
        $belge_no = $_POST['odeme_belge_no'];
        $odeme_tarihi = $_POST['odeme_tarihi'];
        $durum = $_POST['odeme_durum'];
        $admin_id = $_SESSION['b2b_admin_id'] ?? 1;
        
        $sql = "INSERT INTO b2b_cari_odemeler (cari_id, odeme_tipi, tutar, aciklama, banka_bilgisi, belge_no, odeme_tarihi, durum, onaylayan_admin) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('isdssssi', $cari_id, $odeme_tipi, $tutar, $aciklama, $banka_bilgisi, $belge_no, $odeme_tarihi, $durum, $admin_id);
        
        if ($stmt->execute()) {
            // Onaylandıysa cari hareketlere de ekle
            if ($durum === 'onaylandi') {
                $sql2 = "INSERT INTO b2b_cari_hareketler (cari_id, hareket_tipi, tutar, aciklama, islem_tarihi, created_by, referans_tip, referans_id) 
                        VALUES (?, 'odeme', ?, ?, ?, ?, 'odeme', ?)";
                $stmt2 = $conn->prepare($sql2);
                $stmt2->bind_param('idssi', $cari_id, $tutar, $aciklama, $odeme_tarihi, $admin_id, $conn->insert_id);
                $stmt2->execute();
            }
            
            $mesaj = "Ödeme kaydı başarıyla eklendi.";
            $mesaj_tipi = "success";
        } else {
            $mesaj = "Ödeme ekleme sırasında hata oluştu.";
            $mesaj_tipi = "danger";
        }
    }
}

// Son hareketleri getir
$sql = "SELECT * FROM b2b_cari_hareketler WHERE cari_id = ? ORDER BY created_at DESC LIMIT 20";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $cari_id);
$stmt->execute();
$hareketler = $stmt->get_result();

// Son ödemeleri getir
$sql = "SELECT * FROM b2b_cari_odemeler WHERE cari_id = ? ORDER BY created_at DESC LIMIT 10";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $cari_id);
$stmt->execute();
$odemeler = $stmt->get_result();

// Müşterinin siparişlerini getir (renk ve takip kodları ile birlikte)
$sql = "SELECT s.*, 
               GROUP_CONCAT(DISTINCT u.renk ORDER BY u.renk SEPARATOR ', ') as renkler,
               GROUP_CONCAT(DISTINCT p.takip_kodu ORDER BY p.takip_kodu SEPARATOR ', ') as takip_kodlari
        FROM b2b_siparisler s
        LEFT JOIN b2b_siparis_detaylari sd ON s.id = sd.siparis_id
        LEFT JOIN urunler u ON sd.urun_id = u.id
        LEFT JOIN paketler p ON p.urun_id = u.id AND p.durum = 'stokta'
        WHERE s.cari_id = ?
        GROUP BY s.id
        ORDER BY s.siparis_tarihi DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $cari_id);
$stmt->execute();
$siparisler = $stmt->get_result();

// Header dahil et
include 'includes/header.php';
?>

<div class="container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title">
            <h1>
                <i class="fas fa-user-tie"></i>
                <?php echo htmlspecialchars($cari['firm_name']); ?>
            </h1>
            <a href="musteriler.php" class="btn">
                <i class="fas fa-arrow-left"></i>
                Geri Dön
            </a>
        </div>
    </div>

    <?php if ($mesaj): ?>
        <div class="alert alert-<?php echo $mesaj_tipi; ?>">
            <i class="fas fa-<?php echo $mesaj_tipi == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo $mesaj; ?>
        </div>
    <?php endif; ?>

    <!-- Bakiye Kartı -->
    <div class="content-card">
        <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-chart-line"></i>
            Cari Bakiye Özeti
        </h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem;">
            <div style="background: linear-gradient(135deg, var(--danger), #dc2626); color: white; padding: 1.5rem; border-radius: 1rem; text-align: center;">
                <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 0.5rem;">Toplam Borç</div>
                <div style="font-size: 1.5rem; font-weight: 700;">
                    <?php echo number_format($bakiye_bilgi['toplam_borc'] ?? 0, 2); ?> ₺
                </div>
            </div>
            <div style="background: linear-gradient(135deg, var(--success), #059669); color: white; padding: 1.5rem; border-radius: 1rem; text-align: center;">
                <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 0.5rem;">Toplam Alacak</div>
                <div style="font-size: 1.5rem; font-weight: 700;">
                    <?php echo number_format($bakiye_bilgi['toplam_alacak'] ?? 0, 2); ?> ₺
                </div>
            </div>
            <div style="background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white; padding: 1.5rem; border-radius: 1rem; text-align: center;">
                <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 0.5rem;">Toplam Ödeme</div>
                <div style="font-size: 1.5rem; font-weight: 700;">
                    <?php echo number_format($bakiye_bilgi['toplam_odeme'] ?? 0, 2); ?> ₺
                </div>
            </div>
            <div style="background: linear-gradient(135deg, var(--secondary), var(--accent)); color: white; padding: 1.5rem; border-radius: 1rem; text-align: center;">
                <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 0.5rem;">Net Bakiye</div>
                <div style="font-size: 1.5rem; font-weight: 700;">
                    <?php echo number_format($bakiye_bilgi['bakiye'] ?? 0, 2); ?> ₺
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="content-card">
        <style>
            .tabs {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                margin-bottom: 2rem;
                border-bottom: 2px solid var(--gray-light);
                padding-bottom: 1rem;
            }
            .tab-btn {
                background: transparent;
                border: 2px solid transparent;
                padding: 0.875rem 1.5rem;
                border-radius: 0.75rem;
                color: var(--gray);
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
            }
            .tab-btn:hover {
                color: var(--secondary);
                background-color: rgba(249, 115, 22, 0.1);
                border-color: rgba(249, 115, 22, 0.2);
            }
            .tab-btn.active {
                color: var(--secondary);
                background-color: rgba(249, 115, 22, 0.1);
                border-color: var(--secondary);
            }
            .tab-panel {
                display: none;
            }
            .tab-panel.active {
                display: block;
            }
            .form-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-bottom: 2rem;
            }
            .form-group {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }
            .form-label {
                font-weight: 600;
                color: var(--dark);
                font-size: 0.875rem;
            }
            .form-input {
                padding: 0.875rem;
                border: 2px solid var(--gray-light);
                border-radius: 0.5rem;
                font-size: 1rem;
                transition: all 0.3s ease;
            }
            .form-input:focus {
                outline: none;
                border-color: var(--secondary);
                box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
            }
            .table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 1rem;
            }
            .table th,
            .table td {
                padding: 1rem;
                text-align: left;
                border-bottom: 1px solid var(--gray-light);
            }
            .table th {
                background-color: rgba(55, 65, 81, 0.05);
                font-weight: 600;
                color: var(--dark);
            }
            .table tr:hover {
                background-color: rgba(249, 115, 22, 0.05);
            }
            .badge {
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                font-size: 0.75rem;
                font-weight: 600;
                color: white;
            }
            .badge-borc { background-color: var(--danger); }
            .badge-alacak { background-color: var(--success); }
            .badge-odeme { background-color: #0ea5e9; }
            .badge-tahsilat { background-color: var(--warning); color: var(--dark); }
            .badge-beklemede { background-color: var(--warning); color: var(--dark); }
            .badge-onaylandi { background-color: var(--success); }
            .badge-iptal { background-color: var(--danger); }
            .badge-hazirlaniyor { background-color: #0ea5e9; }
            .badge-tamamlandi { background-color: var(--success); }
        </style>

        <div class="tabs">
            <button class="tab-btn active" onclick="showTab(this, 'ayarlar')">
                <i class="fas fa-cog"></i>
                Cari Ayarları
            </button>
            <button class="tab-btn" onclick="showTab(this, 'hareketler')">
                <i class="fas fa-exchange-alt"></i>
                Cari Hareketler
            </button>
            <button class="tab-btn" onclick="showTab(this, 'odemeler')">
                <i class="fas fa-credit-card"></i>
                Ödemeler
            </button>
            <button class="tab-btn" onclick="showTab(this, 'siparisler')">
                <i class="fas fa-shopping-cart"></i>
                Siparişler
            </button>
            <button class="tab-btn" onclick="showTab(this, 'hareket-ekle')">
                <i class="fas fa-plus"></i>
                Hareket Ekle
            </button>
            <button class="tab-btn" onclick="showTab(this, 'odeme-ekle')">
                <i class="fas fa-money-bill"></i>
                Ödeme Ekle
            </button>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="content-card">
        
        <!-- Cari Ayarları Tab -->
        <div class="tab-panel active" id="ayarlar">
            <h4 style="margin-bottom: 1.5rem; color: var(--dark);">
                <i class="fas fa-cog"></i>
                Cari Ayarları
            </h4>
            <form method="post">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">İskonto Oranı (%)</label>
                        <input type="number" class="form-input" name="iskonto_orani" 
                               value="<?php echo $cari_ayarlar['iskonto_orani']; ?>" 
                               step="0.01" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Ek İndirim Oranı (%)</label>
                        <input type="number" class="form-input" name="ek_indirim_orani" 
                               value="<?php echo $cari_ayarlar['ek_indirim_orani']; ?>" 
                               step="0.01" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Kredi Limiti (₺)</label>
                        <input type="number" class="form-input" name="kredi_limiti" 
                               value="<?php echo $cari_ayarlar['kredi_limiti']; ?>" 
                               step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Ödeme Vadesi (Gün)</label>
                        <input type="number" class="form-input" name="odeme_vadesi" 
                               value="<?php echo $cari_ayarlar['odeme_vadesi']; ?>" 
                               min="0">
                    </div>
                </div>
                <button type="submit" name="ayarlar_guncelle" class="btn">
                    <i class="fas fa-save"></i>
                    Ayarları Güncelle
                </button>
            </form>
        </div>

        <!-- Cari Hareketler Tab -->
        <div class="tab-panel" id="hareketler">
            <h4 style="margin-bottom: 1.5rem; color: var(--dark);">
                <i class="fas fa-exchange-alt"></i>
                Son Cari Hareketler
            </h4>
            <div style="overflow-x: auto;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Tarih</th>
                            <th>Tip</th>
                            <th>Tutar</th>
                            <th>Açıklama</th>
                            <th>Belge No</th>
                            <th>Vade</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $hareketler->data_seek(0); while ($hareket = $hareketler->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo date('d.m.Y', strtotime($hareket['islem_tarihi'])); ?></td>
                            <td>
                                <span class="badge badge-<?php echo $hareket['hareket_tipi']; ?>">
                                    <?php echo ucfirst($hareket['hareket_tipi']); ?>
                                </span>
                            </td>
                            <td style="font-weight: 600;"><?php echo number_format($hareket['tutar'], 2); ?> ₺</td>
                            <td><?php echo htmlspecialchars($hareket['aciklama']); ?></td>
                            <td><?php echo htmlspecialchars($hareket['belge_no']); ?></td>
                            <td>
                                <?php if ($hareket['vade_tarihi']): ?>
                                    <?php echo date('d.m.Y', strtotime($hareket['vade_tarihi'])); ?>
                                <?php else: ?>
                                    <span style="color: var(--gray);">-</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Ödemeler Tab -->
        <div class="tab-panel" id="odemeler">
            <h4 style="margin-bottom: 1.5rem; color: var(--dark);">
                <i class="fas fa-credit-card"></i>
                Son Ödemeler
            </h4>
            <div style="overflow-x: auto;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Tarih</th>
                            <th>Tip</th>
                            <th>Tutar</th>
                            <th>Açıklama</th>
                            <th>Banka</th>
                            <th>Durum</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $odemeler->data_seek(0); while ($odeme = $odemeler->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo date('d.m.Y', strtotime($odeme['odeme_tarihi'])); ?></td>
                            <td><?php echo ucfirst(str_replace('_', ' ', $odeme['odeme_tipi'])); ?></td>
                            <td style="font-weight: 600;"><?php echo number_format($odeme['tutar'], 2); ?> ₺</td>
                            <td><?php echo htmlspecialchars($odeme['aciklama']); ?></td>
                            <td><?php echo htmlspecialchars($odeme['banka_bilgisi']); ?></td>
                            <td>
                                <span class="badge badge-<?php echo $odeme['durum']; ?>">
                                    <?php echo ucfirst($odeme['durum']); ?>
                                </span>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Siparişler Tab -->
        <div class="tab-panel" id="siparisler">
            <h4 style="margin-bottom: 1.5rem; color: var(--dark);">
                <i class="fas fa-shopping-cart"></i>
                Müşteri Siparişleri
            </h4>
            <?php if ($siparisler->num_rows == 0): ?>
                <div style="text-align: center; padding: 3rem; color: var(--gray);">
                    <i class="fas fa-shopping-cart" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <h3 style="margin-bottom: 0.5rem;">Henüz sipariş bulunmuyor</h3>
                    <p>Bu müşteri henüz hiç sipariş vermemiş.</p>
                </div>
            <?php else: ?>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Sipariş No</th>
                                <th>Tarih</th>
                                <th>Renkler</th>
                                <th>Takip Kodları</th>
                                <th>Tutar</th>
                                <th>Durum</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($siparis = $siparisler->fetch_assoc()): ?>
                            <tr>
                                <td style="font-weight: 600;">#<?php echo $siparis['id']; ?></td>
                                <td><?php echo date('d.m.Y H:i', strtotime($siparis['siparis_tarihi'])); ?></td>
                                <td>
                                    <div style="font-size: 0.875rem; color: var(--dark); font-weight: 600; max-width: 120px; word-wrap: break-word;">
                                        <?php echo $siparis['renkler'] ? htmlspecialchars($siparis['renkler']) : '<span style="color: #999;">-</span>'; ?>
                                    </div>
                                </td>
                                <td>
                                    <div style="font-size: 0.875rem; color: var(--gray); font-weight: 600; max-width: 150px; word-wrap: break-word;">
                                        <?php echo $siparis['takip_kodlari'] ? htmlspecialchars($siparis['takip_kodlari']) : '<span style="color: #999;">-</span>'; ?>
                                    </div>
                                </td>
                                <td style="font-weight: 600;">
                                    <?php echo number_format($siparis['toplam_tutar'] ?? 0, 2, ',', '.'); ?> ₺
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo $siparis['durum']; ?>">
                                        <i class="fas fa-<?php 
                                            switch($siparis['durum']) {
                                                case 'beklemede': echo 'clock'; break;
                                                case 'hazirlaniyor': echo 'spinner'; break;
                                                case 'tamamlandi': echo 'check-circle'; break;
                                                case 'iptal': echo 'times-circle'; break;
                                                default: echo 'question-circle';
                                            }
                                        ?>"></i>
                                        <?php echo ucfirst($siparis['durum']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                        <a href="siparis_detay.php?id=<?php echo $siparis['id']; ?>" 
                                           style="padding: 0.5rem 0.875rem; border-radius: 0.5rem; text-decoration: none; font-size: 0.8rem; font-weight: 600; display: inline-flex; align-items: center; gap: 0.375rem; background-color: rgba(59, 130, 246, 0.1); color: #1e40af; border: 1px solid rgba(59, 130, 246, 0.2);"
                                           title="Detay Görüntüle">
                                            <i class="fas fa-eye"></i>
                                            Detay
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Hareket Ekle Tab -->
        <div class="tab-panel" id="hareket-ekle">
            <h4 style="margin-bottom: 1.5rem; color: var(--dark);">
                <i class="fas fa-plus"></i>
                Yeni Cari Hareket Ekle
            </h4>
            <form method="post">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Hareket Tipi</label>
                        <select class="form-input" name="hareket_tipi" required>
                            <option value="">Seçiniz</option>
                            <option value="borc">Borç</option>
                            <option value="alacak">Alacak</option>
                            <option value="odeme">Ödeme</option>
                            <option value="tahsilat">Tahsilat</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Tutar (₺)</label>
                        <input type="number" class="form-input" name="tutar" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">İşlem Tarihi</label>
                        <input type="date" class="form-input" name="islem_tarihi" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Vade Tarihi</label>
                        <input type="date" class="form-input" name="vade_tarihi">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Belge No</label>
                        <input type="text" class="form-input" name="belge_no">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Açıklama</label>
                        <input type="text" class="form-input" name="aciklama" required>
                    </div>
                </div>
                <button type="submit" name="hareket_ekle" class="btn">
                    <i class="fas fa-plus"></i>
                    Hareketi Ekle
                </button>
            </form>
        </div>

        <!-- Ödeme Ekle Tab -->
        <div class="tab-panel" id="odeme-ekle">
            <h4 style="margin-bottom: 1.5rem; color: var(--dark);">
                <i class="fas fa-money-bill"></i>
                Yeni Ödeme Ekle
            </h4>
            <form method="post">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Ödeme Tipi</label>
                        <select class="form-input" name="odeme_tipi" required>
                            <option value="">Seçiniz</option>
                            <option value="nakit">Nakit</option>
                            <option value="kredi_karti">Kredi Kartı</option>
                            <option value="havale">Havale/EFT</option>
                            <option value="cek">Çek</option>
                            <option value="senet">Senet</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Tutar (₺)</label>
                        <input type="number" class="form-input" name="odeme_tutar" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Ödeme Tarihi</label>
                        <input type="date" class="form-input" name="odeme_tarihi" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Durum</label>
                        <select class="form-input" name="odeme_durum" required>
                            <option value="beklemede">Beklemede</option>
                            <option value="onaylandi">Onaylandı</option>
                            <option value="iptal">İptal</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Belge No</label>
                        <input type="text" class="form-input" name="odeme_belge_no">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Banka Bilgisi</label>
                        <input type="text" class="form-input" name="banka_bilgisi">
                    </div>
                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label class="form-label">Açıklama</label>
                        <textarea class="form-input" name="odeme_aciklama" rows="3"></textarea>
                    </div>
                </div>
                <button type="submit" name="odeme_ekle" class="btn">
                    <i class="fas fa-save"></i>
                    Ödeme Kaydı Ekle
                </button>
            </form>
        </div>
    </div>

    <script>
        function showTab(clickedBtn, tabName) {
            // Tüm tab butonlarının active sınıfını kaldır
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Tüm tab panellerini gizle
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            
            // Tıklanan tab butonunu aktif yap
            clickedBtn.classList.add('active');
            
            // İlgili tab panelini göster
            document.getElementById(tabName).classList.add('active');
        }
    </script>

<?php include 'includes/footer.php'; ?> 