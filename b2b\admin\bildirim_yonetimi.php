<?php
session_start();
include '../config.php';

// Admin kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header("Location: login.php");
    exit;
}

// Page bilgileri
$page_title = "Bildirim Yönetimi - B2B Admin";
$current_page = "bildirimler";

$mesaj = '';
$mesaj_tur = '';

// Tablo oluştur eğer yoksa
$create_table_sql = "CREATE TABLE IF NOT EXISTS b2b_bildirimler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    baslik VARCHAR(255) NOT NULL,
    icerik TEXT NOT NULL,
    aktif TINYINT(1) DEFAULT 1,
    baslangic_tarihi DATE NULL,
    bitis_tarihi DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
$conn->query($create_table_sql);

// Bildirim ekleme/güncelleme
if (isset($_POST['kaydet'])) {
    $baslik = trim($_POST['baslik']);
    $icerik = trim($_POST['icerik']);
    $aktif = isset($_POST['aktif']) ? 1 : 0;
    $baslangic_tarihi = $_POST['baslangic_tarihi'] ?: null;
    $bitis_tarihi = $_POST['bitis_tarihi'] ?: null;
    
    if (!empty($baslik) && !empty($icerik)) {
        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Güncelleme
            $id = intval($_POST['id']);
            $sql = "UPDATE b2b_bildirimler SET baslik = ?, icerik = ?, aktif = ?, baslangic_tarihi = ?, bitis_tarihi = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ssissi', $baslik, $icerik, $aktif, $baslangic_tarihi, $bitis_tarihi, $id);
            
            if ($stmt->execute()) {
                $mesaj = "Bildirim başarıyla güncellendi.";
                $mesaj_tur = "success";
            } else {
                $mesaj = "Bildirim güncellenirken hata oluştu.";
                $mesaj_tur = "danger";
            }
        } else {
            // Yeni ekleme
            $sql = "INSERT INTO b2b_bildirimler (baslik, icerik, aktif, baslangic_tarihi, bitis_tarihi) VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ssiss', $baslik, $icerik, $aktif, $baslangic_tarihi, $bitis_tarihi);
            
            if ($stmt->execute()) {
                $mesaj = "Bildirim başarıyla eklendi.";
                $mesaj_tur = "success";
            } else {
                $mesaj = "Bildirim eklenirken hata oluştu.";
                $mesaj_tur = "danger";
            }
        }
    } else {
        $mesaj = "Başlık ve içerik alanları zorunludur.";
        $mesaj_tur = "danger";
    }
}

// Bildirim silme
if (isset($_GET['sil']) && is_numeric($_GET['sil'])) {
    $id = intval($_GET['sil']);
    $sql = "DELETE FROM b2b_bildirimler WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    
    if ($stmt->execute()) {
        $mesaj = "Bildirim başarıyla silindi.";
        $mesaj_tur = "success";
    } else {
        $mesaj = "Bildirim silinirken hata oluştu.";
        $mesaj_tur = "danger";
    }
}

// Aktif/pasif toggle
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $id = intval($_GET['toggle']);
    $sql = "UPDATE b2b_bildirimler SET aktif = NOT aktif WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    
    if ($stmt->execute()) {
        $mesaj = "Bildirim durumu değiştirildi.";
        $mesaj_tur = "success";
    } else {
        $mesaj = "Durum değiştirilirken hata oluştu.";
        $mesaj_tur = "danger";
    }
}

// Düzenleme için bildirim getir
$duzenle_bildirim = null;
if (isset($_GET['duzenle']) && is_numeric($_GET['duzenle'])) {
    $id = intval($_GET['duzenle']);
    $sql = "SELECT * FROM b2b_bildirimler WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result && $result->num_rows > 0) {
        $duzenle_bildirim = $result->fetch_assoc();
    }
}

// Bildirimleri listele
$bildirimler = [];
$sql = "SELECT * FROM b2b_bildirimler ORDER BY created_at DESC";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $bildirimler[] = $row;
    }
}

// Header dahil et
include 'includes/header.php';
?>

<style>
    .form-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-group.full-width {
        grid-column: 1 / -1;
    }
    
    .form-group label {
        display: block;
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 0.5rem;
    }
    
    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--gray-light);
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--secondary);
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 120px;
    }
    
    .checkbox-wrapper {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .checkbox-wrapper input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
    
    .notifications-table {
        width: 100%;
        border-collapse: collapse;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }
    
    .notifications-table th {
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        color: white;
        font-weight: 600;
        padding: 1rem;
        text-align: left;
        font-size: 0.875rem;
    }
    
    .notifications-table td {
        padding: 1rem;
        border-bottom: 1px solid var(--gray-light);
        vertical-align: middle;
    }
    
    .notifications-table tr:hover {
        background-color: rgba(249, 115, 22, 0.05);
    }
    
    .notifications-table tr:last-child td {
        border-bottom: none;
    }
    
    .status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
    }
    
    .status-aktif {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }
    
    .status-pasif {
        background-color: rgba(107, 114, 128, 0.1);
        color: var(--gray);
        border: 1px solid rgba(107, 114, 128, 0.2);
    }
    
    .action-links {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        align-items: center;
    }
    
    .action-btn {
        padding: 0.5rem 0.875rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }
    
    .action-btn:hover {
        transform: translateY(-1px);
    }
    
    .action-edit {
        background-color: rgba(249, 115, 22, 0.1);
        color: var(--secondary);
        border-color: rgba(249, 115, 22, 0.2);
    }
    
    .action-edit:hover {
        background-color: var(--secondary);
        color: white;
    }
    
    .action-toggle {
        background-color: rgba(59, 130, 246, 0.1);
        color: #1e40af;
        border-color: rgba(59, 130, 246, 0.2);
    }
    
    .action-toggle:hover {
        background-color: #3b82f6;
        color: white;
    }
    
    .action-delete {
        background-color: rgba(239, 68, 68, 0.1);
        color: var(--danger);
        border-color: rgba(239, 68, 68, 0.2);
    }
    
    .action-delete:hover {
        background-color: var(--danger);
        color: white;
    }
    
    .content-preview {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-bell"></i>
            Bildirim Yönetimi
        </h1>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<!-- Bildirim Ekleme/Düzenleme Formu -->
<div class="form-card">
    <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-<?php echo $duzenle_bildirim ? 'edit' : 'plus'; ?>"></i>
        <?php echo $duzenle_bildirim ? 'Bildirim Düzenle' : 'Yeni Bildirim Ekle'; ?>
    </h3>
    
    <form method="post">
        <?php if ($duzenle_bildirim): ?>
            <input type="hidden" name="id" value="<?php echo $duzenle_bildirim['id']; ?>">
        <?php endif; ?>
        
        <div class="form-grid">
            <div class="form-group">
                <label for="baslik">Bildirim Başlığı:</label>
                <input type="text" id="baslik" name="baslik" 
                       value="<?php echo htmlspecialchars($duzenle_bildirim['baslik'] ?? ''); ?>" 
                       placeholder="Örn: Önemli Duyuru" required>
            </div>
            
            <div class="form-group">
                <label for="baslangic_tarihi">Başlangıç Tarihi:</label>
                <input type="date" id="baslangic_tarihi" name="baslangic_tarihi" 
                       value="<?php echo $duzenle_bildirim['baslangic_tarihi'] ?? ''; ?>">
            </div>
            
            <div class="form-group full-width">
                <label for="icerik">Bildirim İçeriği:</label>
                <textarea id="icerik" name="icerik" 
                          placeholder="Bildirim içeriğini buraya yazın..." required><?php echo htmlspecialchars($duzenle_bildirim['icerik'] ?? ''); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="bitis_tarihi">Bitiş Tarihi:</label>
                <input type="date" id="bitis_tarihi" name="bitis_tarihi" 
                       value="<?php echo $duzenle_bildirim['bitis_tarihi'] ?? ''; ?>">
            </div>
            
            <div class="form-group">
                <div class="checkbox-wrapper">
                    <input type="checkbox" id="aktif" name="aktif" 
                           <?php echo ($duzenle_bildirim['aktif'] ?? 1) ? 'checked' : ''; ?>>
                    <label for="aktif">Bildirimi aktif et</label>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 1.5rem; display: flex; gap: 1rem;">
            <button type="submit" name="kaydet" class="btn">
                <i class="fas fa-save"></i>
                <?php echo $duzenle_bildirim ? 'Güncelle' : 'Kaydet'; ?>
            </button>
            
            <?php if ($duzenle_bildirim): ?>
                <a href="bildirim_yonetimi.php" class="btn" style="background: var(--gray);">
                    <i class="fas fa-times"></i>
                    İptal
                </a>
            <?php endif; ?>
        </div>
    </form>
</div>

<!-- Bildirimler Listesi -->
<div class="content-card">
    <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-list"></i>
        Mevcut Bildirimler (<?php echo count($bildirimler); ?>)
    </h3>
    
    <?php if (empty($bildirimler)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--gray);">
            <i class="fas fa-bell-slash" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem;">Henüz bildirim bulunmuyor</h3>
            <p>Yukarıdaki formdan yeni bildirim ekleyebilirsiniz.</p>
        </div>
    <?php else: ?>
        <div style="overflow-x: auto;">
            <table class="notifications-table">
                <thead>
                    <tr>
                        <th>Başlık</th>
                        <th>İçerik</th>
                        <th>Tarih Aralığı</th>
                        <th>Durum</th>
                        <th>Oluşturulma</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($bildirimler as $bildirim): ?>
                    <tr>
                        <td style="font-weight: 600; color: var(--dark);">
                            <?php echo htmlspecialchars($bildirim['baslik']); ?>
                        </td>
                        <td>
                            <div class="content-preview">
                                <?php echo htmlspecialchars($bildirim['icerik']); ?>
                            </div>
                        </td>
                        <td style="font-size: 0.875rem;">
                            <?php if ($bildirim['baslangic_tarihi'] || $bildirim['bitis_tarihi']): ?>
                                <?php echo $bildirim['baslangic_tarihi'] ? date('d.m.Y', strtotime($bildirim['baslangic_tarihi'])) : '∞'; ?> 
                                - 
                                <?php echo $bildirim['bitis_tarihi'] ? date('d.m.Y', strtotime($bildirim['bitis_tarihi'])) : '∞'; ?>
                            <?php else: ?>
                                <span style="color: var(--gray);">Süresiz</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="status-badge status-<?php echo $bildirim['aktif'] ? 'aktif' : 'pasif'; ?>">
                                <i class="fas fa-<?php echo $bildirim['aktif'] ? 'check-circle' : 'times-circle'; ?>"></i>
                                <?php echo $bildirim['aktif'] ? 'Aktif' : 'Pasif'; ?>
                            </span>
                        </td>
                        <td style="font-size: 0.875rem;">
                            <?php echo date('d.m.Y H:i', strtotime($bildirim['created_at'])); ?>
                        </td>
                        <td>
                            <div class="action-links">
                                <a href="?duzenle=<?php echo $bildirim['id']; ?>" class="action-btn action-edit" title="Düzenle">
                                    <i class="fas fa-edit"></i>
                                    Düzenle
                                </a>
                                <a href="?toggle=<?php echo $bildirim['id']; ?>" class="action-btn action-toggle" title="Durumu Değiştir"
                                   onclick="return confirm('Bildirim durumunu değiştirmek istediğinizden emin misiniz?')">
                                    <i class="fas fa-<?php echo $bildirim['aktif'] ? 'eye-slash' : 'eye'; ?>"></i>
                                    <?php echo $bildirim['aktif'] ? 'Pasif Et' : 'Aktif Et'; ?>
                                </a>
                                <a href="?sil=<?php echo $bildirim['id']; ?>" class="action-btn action-delete" title="Sil"
                                   onclick="return confirm('Bu bildirimi silmek istediğinizden emin misiniz?')">
                                    <i class="fas fa-trash"></i>
                                    Sil
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?> 