<?php
// Bu dosya tüm B2B sayfalarında ortak header sağlar
if (!isset($page_title)) {
    $page_title = "B2B Satış Portalı";
}
if (!isset($current_page)) {
    $current_page = "";
}

// Logo ayarlarını veritabanından al
$site_logo = '';
$logo_position = 'left';
$logo_size = 'normal';

// Veritabanı bağlantısını dahil et
include_once dirname(__FILE__) . '/../config.php';

// Session yetkilerini güncelle (giriş yapmış kullanıcılar için)
if (kullaniciGirisYapmisMi()) {
    sessionYetkileriniGuncelle();
}

// Tema ayarları tablosunu kontrol et ve logo ayarlarını al
$logo_sql = "SELECT ayar_adi, ayar_degeri FROM b2b_tema_ayarlari WHERE ayar_adi IN ('site_logo', 'logo_position', 'logo_size')";
$logo_result = $conn->query($logo_sql);
if ($logo_result && $logo_result->num_rows > 0) {
    while ($row = $logo_result->fetch_assoc()) {
        switch($row['ayar_adi']) {
            case 'site_logo':
                $site_logo = $row['ayar_degeri'];
                break;
            case 'logo_position':
                $logo_position = $row['ayar_degeri'];
                break;
            case 'logo_size':
                $logo_size = $row['ayar_degeri'];
                break;
        }
    }
}

// Giriş yapmış kullanıcının firma bilgilerini al
$current_user_company = '';
if (isset($_SESSION['b2b_cari_id'])) {
    $cari_id = $_SESSION['b2b_cari_id'];
    $user_sql = "SELECT firm_name FROM cari_firmalar WHERE id = ?";
    $user_stmt = $conn->prepare($user_sql);
    $user_stmt->bind_param('i', $cari_id);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();
    if ($user_result && $user_result->num_rows > 0) {
        $user_data = $user_result->fetch_assoc();
        $current_user_company = $user_data['firm_name'];
    }
}

// Logo URL'sini belirle
$logo_url = '../images/otris-logo.png'; // Varsayılan logo
if (!empty($site_logo)) {
    $logo_path = '../uploads/logos/' . $site_logo;
    
    // Dosya kontrolü için mutlak path kullan
    $absolute_check_path = __DIR__ . '/../uploads/logos/' . $site_logo;
    
    if (file_exists($absolute_check_path)) {
        $logo_url = $logo_path;
    }
    // Alternatif path kontrolü
    elseif (file_exists(dirname(__FILE__) . '/../uploads/logos/' . $site_logo)) {
        $logo_url = $logo_path;
    }
    // Son çare: doğrudan uploads klasörü kontrolü
    elseif (file_exists('uploads/logos/' . $site_logo)) {
        $logo_url = 'uploads/logos/' . $site_logo;
    }
}

// Logo boyutunu belirle
$logo_height = '40px';
switch($logo_size) {
    case 'small':
        $logo_height = '30px';
        break;
    case 'large':
        $logo_height = '50px';
        break;
    default:
        $logo_height = '40px';
        break;
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #1f2937;        /* Koyu gri/siyah */
            --primary-dark: #111827;   /* Çok koyu gri */
            --secondary: #f97316;      /* Turuncu */
            --accent: #ea580c;         /* Koyu turuncu */
            --dark: #111827;           /* Siyah */
            --light: #ffffff;          /* Beyaz */
            --gray: #6b7280;           /* Gri */
            --gray-light: #e5e7eb;     /* Açık gri */
            --success: #10b981;        /* Yeşil */
            --warning: #f59e0b;        /* Sarı */
            --danger: #ef4444;         /* Kırmızı */
            --background: #f8fafc;     /* Çok açık gri arka plan */
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: var(--background);
            min-height: 100vh;
            color: var(--dark);
            line-height: 1.6;
        }
        
        header {
            background: var(--light);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid var(--gray-light);
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            max-width: 1280px;
            margin: 0 auto;
            position: relative;
        }
        
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark);
            cursor: pointer;
            padding: 0.75rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }
        
        .mobile-menu-toggle:hover {
            background-color: var(--gray-light);
            color: var(--secondary);
        }
        
        .mobile-menu-toggle svg {
            width: 24px;
            height: 24px;
        }
        
        .brand {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark);
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            border: 2px solid #e5e7eb;
            background: rgba(255, 255, 255, 0.9);
            min-height: 2.5rem;
            box-sizing: border-box;
        }
        
        .brand:hover {
            color: var(--secondary);
            transform: translateY(-1px);
            border-color: var(--secondary);
            background: rgba(255, 255, 255, 1);
        }
        
        .brand-logo {
            height: <?php echo $logo_height; ?>;
            width: auto;
            max-width: 200px;
            transition: all 0.3s ease;
            object-fit: contain;
        }
        
        .brand:hover .brand-logo {
            transform: scale(1.05);
        }
        
        .nav-links {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .search-container {
            position: relative;
            margin-left: 1rem;
        }
        
        .search-box {
            position: relative;
            display: flex;
            align-items: center;
            background: rgba(249, 115, 22, 0.1);
            border: 1px solid rgba(249, 115, 22, 0.2);
            border-radius: 25px;
            padding: 0.4rem 0.75rem;
            min-width: 180px;
            max-width: 180px;
            transition: all 0.3s ease;
        }
        
        .search-box:focus-within {
            background: rgba(255, 255, 255, 0.9);
            border-color: var(--secondary);
            box-shadow: 0 4px 15px rgba(249, 115, 22, 0.3);
            transform: translateY(-1px);
        }
        
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 0.95rem;
            color: var(--dark);
            font-weight: 500;
            padding-right: 40px;
        }
        
        .search-input::placeholder {
            color: rgba(107, 114, 128, 0.7);
        }
        
        .search-btn {
            background: transparent;
            border: none;
            color: var(--gray);
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .search-btn:hover {
            background: rgba(107, 114, 128, 0.1);
            color: var(--dark);
            transform: scale(1.05);
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid var(--gray-light);
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            margin-top: 0.5rem;
        }
        
        .search-results.active {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .search-result-item {
            padding: 1rem;
            border-bottom: 1px solid var(--gray-light);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .search-result-item:hover {
            background: rgba(249, 115, 22, 0.05);
            transform: translateX(5px);
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .search-result-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid var(--gray-light);
        }
        
        .search-result-info {
            flex: 1;
        }
        
        .search-result-title {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.25rem;
        }
        
        .search-result-category {
            font-size: 0.875rem;
            color: var(--gray);
        }
        
        .search-result-price {
            font-weight: 600;
            color: var(--secondary);
            font-size: 1.1rem;
        }
        
        .search-no-results {
            padding: 2rem;
            text-align: center;
            color: var(--gray);
        }
        
        .search-loading {
            padding: 1rem;
            text-align: center;
            color: var(--gray);
        }
        
        .nav-links a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
            min-height: 2.5rem;
            box-sizing: border-box;
            background: transparent;
            border: 2px solid transparent;
        }
        
        .nav-links a:hover {
            color: var(--secondary);
            background-color: rgba(249, 115, 22, 0.1);
            border-color: rgba(249, 115, 22, 0.2);
            transform: translateY(-1px);
        }
        
        .nav-links a.active {
            color: var(--secondary);
            background-color: rgba(249, 115, 22, 0.1);
            border-color: rgba(249, 115, 22, 0.3);
        }
        
        .nav-icon {
            width: 18px;
            height: 18px;
        }
        
        .user-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }
        
        /* Dropdown Menu Sistemi */
        .dropdown {
            position: relative;
            display: inline-block;
        }
        
        .dropdown-toggle {
            background: none;
            border: none;
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
            min-height: 2.5rem;
            box-sizing: border-box;
            border: 2px solid transparent;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
        }
        
        .dropdown-toggle:hover {
            color: var(--secondary);
            background-color: rgba(249, 115, 22, 0.1);
            border-color: rgba(249, 115, 22, 0.2);
            transform: translateY(-1px);
        }
        
        .dropdown-toggle .dropdown-arrow {
            width: 12px;
            height: 12px;
            transition: transform 0.3s ease;
        }
        
        .dropdown.active .dropdown-toggle .dropdown-arrow {
            transform: rotate(180deg);
        }
        
        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid var(--gray-light);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 0.5rem;
        }
        
        .dropdown.active .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .dropdown-item {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--dark);
            text-decoration: none;
            transition: all 0.2s ease;
            border-bottom: 1px solid var(--gray-light);
            font-weight: 500;
        }
        
        .dropdown-item:first-child {
            border-radius: 0.75rem 0.75rem 0 0;
        }
        
        .dropdown-item:last-child {
            border-bottom: none;
            border-radius: 0 0 0.75rem 0.75rem;
        }
        
        .dropdown-item:hover {
            background: rgba(249, 115, 22, 0.1);
            color: var(--secondary);
            transform: translateX(5px);
        }
        
        .dropdown-item svg {
            width: 16px;
            height: 16px;
            margin-right: 0.5rem;
        }
        
        .company-name {
            font-size: 0.75rem;
            color: var(--gray);
            margin-top: -0.25rem;
            font-weight: 400;
            text-align: center;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.2;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: var(--secondary);
            color: var(--light) !important;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid var(--secondary);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-transform: none;
            letter-spacing: 0.3px;
            font-size: 1rem;
            z-index: 1;
            min-height: 2.5rem;
            box-sizing: border-box;
            gap: 0.5rem;
            white-space: nowrap;
        }
        
        .btn:hover {
            background: var(--accent);
            border-color: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
            color: var(--light) !important;
        }
        
        .btn-secondary {
            background: var(--light);
            color: var(--dark) !important;
            border-color: var(--gray-light);
        }
        
        .btn-secondary:hover {
            background: var(--gray-light);
            border-color: var(--gray);
            color: var(--dark) !important;
        }
        
        .btn-outline {
            background: transparent;
            color: var(--secondary) !important;
            border-color: var(--secondary);
        }
        
        .btn-outline:hover {
            background: var(--secondary);
            color: var(--light) !important;
        }
        
        .btn-danger {
            background: var(--danger);
            border-color: var(--danger);
            color: var(--light) !important;
        }
        
        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }
        
        .btn-success {
            background: var(--success);
            border-color: var(--success);
            color: var(--light) !important;
        }
        
        .btn-success:hover {
            background: #059669;
            border-color: #059669;
        }
        
        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border-color: rgba(16, 185, 129, 0.2);
        }
        
        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border-color: rgba(239, 68, 68, 0.2);
        }
        
        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border-color: rgba(245, 158, 11, 0.2);
        }
        
        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            color: #1e40af;
            border-color: rgba(59, 130, 246, 0.2);
        }
        
        /* Form Elements */
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--gray-light);
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background: var(--light);
            color: var(--dark);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--secondary);
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark);
            display: block;
        }
        
        /* Card Styles */
        .content-card {
            background: var(--light);
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--gray-light);
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem 0;
        }
        
        .page-title h1 {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        /* Mobile Bottom Navigation */
        .mobile-bottom-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.75rem 0;
            z-index: 1000;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .mobile-bottom-nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 100%;
            margin: 0 auto;
            padding: 0 1rem;
            gap: 0.25rem;
        }
        
        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--gray);
            padding: 0.5rem 0.25rem;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
            min-width: 3rem;
            min-height: 3rem;
            position: relative;
            flex: 1;
            max-width: 4.5rem;
        }
        
        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            color: var(--secondary);
            background: rgba(249, 115, 22, 0.1);
            transform: translateY(-2px);
        }
        
        .mobile-nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 0.375rem;
            stroke-width: 2;
            display: block;
            fill: none;
            stroke: currentColor;
        }
        
        .mobile-nav-text {
            font-size: 0.7rem;
            font-weight: 500;
            text-align: center;
            line-height: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            display: block;
        }
        
        /* Badge for cart */
        .mobile-nav-badge {
            position: absolute;
            top: -2px;
            right: 0.25rem;
            background: var(--danger);
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.5rem;
            font-weight: 700;
            min-width: 16px;
        }

        @media (max-width: 768px) {
            /* Mobile Bottom Navigation Görünür */
            .mobile-bottom-nav {
                display: block;
            }
            
            /* Ana sayfa içeriğine bottom padding ekle */
            body {
                padding-bottom: 5rem;
            }
            
            /* Mobile hamburger menu göster */
            .mobile-menu-toggle {
                display: block !important;
            }
            
            /* Navbar'ı sadeleştir */
            .navbar {
                padding: 0.75rem 1rem;
            }
            
            /* Search container'ı mobilde küçült */
            .search-container {
                margin-left: 0;
                margin-right: 0;
                flex: 1;
                max-width: none;
                order: 1;
            }
            
            .search-box {
                min-width: auto;
                max-width: none;
                width: 100%;
                padding: 0.4rem 0.75rem;
            }
            
            /* Dropdown mobilde düzgün görünsün */
            .dropdown-menu {
                position: fixed;
                top: auto;
                bottom: 5rem;
                left: 1rem;
                right: 1rem;
                margin-top: 0;
            }
            
            .search-btn {
                width: 30px;
                height: 30px;
                margin-left: 0.25rem;
            }
            
            /* Mobile dropdown menu */
            .nav-links {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(10px);
                flex-direction: column;
                padding: 1rem;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                border-radius: 0 0 1rem 1rem;
                transform: translateY(-100%);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                gap: 0.5rem;
            }
            
            .nav-links.active {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }
            
            .nav-links a {
                width: 100%;
                justify-content: flex-start;
                padding: 1rem;
                border-radius: 0.75rem;
            }
            
            .search-container {
                order: -1;
                width: 100%;
                margin-bottom: 1rem;
                margin-right: 0;
                max-width: 100%;
            }
            
            .search-box {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="navbar">
            <a href="index.php" class="brand">
                <img src="<?php echo $logo_url; ?>" alt="Otris" class="brand-logo">
                <span>B2B</span>
            </a>
            
            <!-- AVMPARK menüsü - Logo'nun hemen yanında -->
            <?php if (avmparkYetkisiVarMi()): ?>
            <a href="urunler.php?avmpark=1" class="<?php echo ($current_page == 'urunler' && isset($_GET['avmpark'])) ? 'active' : ''; ?>" style="background: linear-gradient(135deg, #4b5563, #374151); color: white !important; position: relative; padding: 0.75rem 1rem; border-radius: 0.5rem; text-decoration: none; font-weight: 500; display: flex; align-items: center; gap: 0.5rem; white-space: nowrap; min-height: 2.5rem; box-sizing: border-box; border: 2px solid transparent; transition: all 0.3s ease; margin-left: 1rem; margin-right: 1rem;">
                <svg style="width: 18px; height: 18px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <rect x="7" y="7" width="10" height="10" rx="1" ry="1"></rect>
                    <path d="M12 7v10"></path>
                    <path d="M7 12h10"></path>
                </svg>
                <span style="font-weight: 700; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">AVMPARK</span>
                <span style="position: absolute; top: -2px; right: -2px; background: #ef4444; color: white; font-size: 0.6rem; padding: 1px 4px; border-radius: 50%; font-weight: bold;">P</span>
            </a>
            <?php endif; ?>
            
            <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                </svg>
            </button>
            
            <div class="nav-links" id="navLinks">
                <a href="index.php" class="<?php echo ($current_page == 'index') ? 'active' : ''; ?>">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                    </svg>
                    Ana Sayfa
                </a>
                <a href="urunler.php" class="<?php echo ($current_page == 'urunler') ? 'active' : ''; ?>">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                        <line x1="7" y1="7" x2="7.01" y2="7"></line>
                    </svg>
                    Ürünler
                </a>
                <a href="kategoriler.php" class="<?php echo ($current_page == 'kategoriler') ? 'active' : ''; ?>" style="display: none;">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                    </svg>
                    Kategoriler
                </a>
                <?php if (!isset($_SESSION['b2b_cari_id'])): ?>
                <a href="bayi_ol.php" class="<?php echo ($current_page == 'bayi_ol') ? 'active' : ''; ?>">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                    Bayi Ol
                </a>
                <?php endif; ?>
                <?php if (!isset($_SESSION['b2b_cari_id'])): ?>
                <a href="login.php" class="<?php echo ($current_page == 'login') ? 'active' : ''; ?>">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                        <polyline points="10 17 15 12 10 7"></polyline>
                        <line x1="15" y1="12" x2="3" y2="12"></line>
                    </svg>
                    Giriş Yap
                </a>
                <?php endif; ?>
                <?php if (isset($_SESSION['b2b_cari_id'])): ?>
                    <a href="sepet.php" class="<?php echo ($current_page == 'sepet') ? 'active' : ''; ?>">
                        <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="9" cy="21" r="1"></circle>
                            <circle cx="20" cy="21" r="1"></circle>
                            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                        </svg>
                        Sepetim
                    </a>
                    
                    <!-- Hesabım Dropdown -->
                    <div class="dropdown" id="accountDropdown">
                        <button class="dropdown-toggle" onclick="toggleDropdown('accountDropdown')">
                            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            <span>Hesabım</span>
                            <svg class="dropdown-arrow" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </button>
                        <div class="dropdown-menu">
                            <a href="hesabim.php" class="dropdown-item">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                Hesap Bilgilerim
                            </a>
                            <?php if (!isset($_SESSION['admin_adina_siparis']) || !$_SESSION['admin_adina_siparis']): ?>
                            <a href="siparislerim.php" class="dropdown-item">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                                </svg>
                                Siparişlerim
                            </a>
                            <a href="ssh_taleplerim.php" class="dropdown-item">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14.828 14.828a4 4 0 0 1-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a7 7 0 1 0-14 0C5 15.25 9 19 12 22c3-3 7-6.75 7-12z"></path>
                                </svg>
                                SSH Taleplerim
                            </a>
                            <?php endif; ?>
                            <a href="cikis.php" class="dropdown-item">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                    <polyline points="16 17 21 12 16 7"></polyline>
                                    <line x1="21" y1="12" x2="9" y2="12"></line>
                                </svg>
                                Çıkış Yap
                            </a>
                        </div>
                        <?php if (!empty($current_user_company)): ?>
                        <div class="company-name" title="<?php echo htmlspecialchars($current_user_company); ?>">
                            <?php echo htmlspecialchars($current_user_company); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Compact Arama Kutusu -->
                <div class="search-container">
                    <div class="search-box">
                        <input type="text" class="search-input" placeholder="Ara..." id="searchInput" autocomplete="off">
                        <button class="search-btn" type="button" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="search-results" id="searchResults"></div>
                </div>

            </div>
        </div>
    </header>
    
    <!-- Admin Adına Sipariş Bildirimi -->
    <?php if (isset($_SESSION['admin_adina_siparis']) && $_SESSION['admin_adina_siparis']): ?>
        <div style="background: linear-gradient(135deg, #8B4513, #A0522D); color: white; text-align: center; padding: 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="max-width: 1280px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div style="flex: 1;">
                    <?php 
                    if (isset($_SESSION['admin_siparis_mesaj'])) {
                        echo $_SESSION['admin_siparis_mesaj'];
                        unset($_SESSION['admin_siparis_mesaj']);
                    } else {
                        echo "🛍️ Admin olarak müşteri adına sipariş veriyorsunuz.";
                    }
                    ?>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <a href="siparislerim.php" style="background: rgba(255,255,255,0.2); color: white; padding: 0.5rem 1rem; border-radius: 0.5rem; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-clipboard-list"></i> Siparişlerim
                    </a>
                    <a href="admin/admin_cikis.php" style="background: rgba(255,255,255,0.2); color: white; padding: 0.5rem 1rem; border-radius: 0.5rem; text-decoration: none; font-weight: 600;">
                        <i class="fas fa-arrow-left"></i> Admin Paneline Dön
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <script>
        // Arama fonksiyonları
        let searchTimeout;
        
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const searchResults = document.getElementById('searchResults');
            
            // Gerçek zamanlı arama
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();
                
                if (query.length < 2) {
                    hideSearchResults();
                    return;
                }
                
                searchTimeout = setTimeout(() => {
                    performLiveSearch(query);
                }, 300);
            });
            
            // Enter tuşu ile arama
            searchInput.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    performSearch();
                }
            });
            
            // Arama sonuçlarını gizle
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.search-container')) {
                    hideSearchResults();
                }
            });
        });
        
        function performSearch() {
            const searchInput = document.getElementById('searchInput');
            const query = searchInput.value.trim();
            if (query.length >= 2) {
                // AVMPARK modu kontrolü
                const isAvmpark = window.location.search.includes('avmpark=1');
                const url = isAvmpark ? 
                    `urunler.php?arama=${encodeURIComponent(query)}&avmpark=1` :
                    `urunler.php?arama=${encodeURIComponent(query)}`;
                window.location.href = url;
            }
        }
        
        function performLiveSearch(query) {
            const searchResults = document.getElementById('searchResults');
            showLoadingResults();
            
            // AVMPARK modu kontrolü
            const isAvmpark = window.location.search.includes('avmpark=1');
            
            // B2B sisteminin live_search.php dosyasını kullan
            fetch(`ajax/live_search.php`, {
                method: 'POST',
                body: new URLSearchParams({
                    q: query,
                    avmpark: isAvmpark ? 1 : 0
                })
            })
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data);
                })
                .catch(error => {
                    console.error('Arama hatası:', error);
                    hideSearchResults();
                });
        }
        
        function showLoadingResults() {
            const searchResults = document.getElementById('searchResults');
            searchResults.innerHTML = '<div class="search-loading"><i class="fas fa-spinner fa-spin"></i> Aranıyor...</div>';
            searchResults.classList.add('active');
        }
        
        function displaySearchResults(results) {
            const searchResults = document.getElementById('searchResults');
            
            if (results.length === 0) {
                searchResults.innerHTML = `
                    <div class="search-no-results">
                        <i class="fas fa-search"></i>
                        <p>Aradığınız ürün bulunamadı</p>
                        <small>Daha geniş arama için tüm sonuçları görün</small>
                    </div>`;
            } else {
                let html = '';
                results.slice(0, 5).forEach(product => {
                    // B2B sisteminin resim URL yapısı
                    const baseUrl = window.location.hostname.includes('otris.com.tr') ? 'https://stok.otris.com.tr' : 'http://localhost/barkod';
                    const image = product.resim ? `${baseUrl}/uploads/urun_resimleri/${product.resim}` : '../images/no-image.jpg';
                    const price = product.fiyat && product.fiyat > 0 ? `₺${parseFloat(product.fiyat).toLocaleString('tr-TR')}` : 'Fiyat için giriş yapın';
                    const productName = product.gosterim_adi || product.stok_adi;
                    
                    // Paket ürünleri için URL oluştur
                    let productUrl = `urun_detay.php?id=${product.id}`;
                    if (product.paket_adi && product.renk_detay) {
                        productUrl += `&paket_adi=${encodeURIComponent(product.paket_adi)}&renk=${encodeURIComponent(product.renk_detay)}`;
                    }
                    
                    html += `
                        <div class="search-result-item" onclick="window.location.href='${productUrl}'">
                            <img src="${image}" alt="${productName}" class="search-result-image" onerror="this.src='../images/no-image.jpg'">
                            <div class="search-result-info">
                                <div class="search-result-title">${productName}</div>
                                <div class="search-result-category">
                                    🏷️ ${product.stok_kodu}
                                    ${product.takip_kodu ? `📍 ${product.takip_kodu}` : ''}
                                    ${product.renk ? `🎨 ${product.renk}` : ''}
                                </div>
                            </div>
                            <div class="search-result-price">${price}</div>
                        </div>`;
                });
                
                html += `
                    <div style="padding: 1rem; text-align: center; border-top: 1px solid var(--gray-light); background: #f8fafc;">
                        <button onclick="performSearch()" class="btn" style="padding: 0.5rem 1rem; font-size: 0.875rem;">
                            <i class="fas fa-search"></i> Tüm Sonuçları Gör
                        </button>
                    </div>`;
                
                searchResults.innerHTML = html;
            }
            searchResults.classList.add('active');
        }
        
        function hideSearchResults() {
            const searchResults = document.getElementById('searchResults');
            searchResults.classList.remove('active');
        }
        
        function toggleMobileMenu() {
            const navLinks = document.getElementById('navLinks');
            navLinks.classList.toggle('active');
        }
        
        // Dropdown menü fonksiyonları
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            
            // Diğer dropdown'ları kapat
            document.querySelectorAll('.dropdown').forEach(dd => {
                if (dd.id !== dropdownId) {
                    dd.classList.remove('active');
                }
            });
            
            // Bu dropdown'ı aç/kapat
            dropdown.classList.toggle('active');
        }
        
        // Dropdown'ları dışarı tıklandığında kapat
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
        
        // Mobil menüyü dışarı tıklandığında kapat
        document.addEventListener('click', function(event) {
            const navLinks = document.getElementById('navLinks');
            const menuToggle = document.querySelector('.mobile-menu-toggle');
            
            if (!navLinks.contains(event.target) && !menuToggle.contains(event.target)) {
                navLinks.classList.remove('active');
            }
        });
        
        // Ekran boyutu değişince mobil menüyü kapat
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('navLinks').classList.remove('active');
            }
        });
    </script>
    
    <!-- Mobile Bottom Navigation -->
    <div class="mobile-bottom-nav">
        <div class="mobile-bottom-nav-items">
            <!-- Ana Sayfa -->
            <a href="index.php" class="mobile-nav-item <?php echo ($current_page == 'index') ? 'active' : ''; ?>">
                <svg class="mobile-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
                <span class="mobile-nav-text">Ana Sayfa</span>
            </a>
            
            <!-- Ürünler -->
            <a href="urunler.php" class="mobile-nav-item <?php echo ($current_page == 'urunler') ? 'active' : ''; ?>">
                <svg class="mobile-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                    <line x1="7" y1="7" x2="7.01" y2="7"></line>
                </svg>
                <span class="mobile-nav-text">Ürünler</span>
            </a>
            
            <!-- Kategoriler -->
            <a href="kategoriler.php" class="mobile-nav-item <?php echo ($current_page == 'kategoriler') ? 'active' : ''; ?>" style="display: none;">
                <svg class="mobile-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                </svg>
                <span class="mobile-nav-text">Kategoriler</span>
            </a>
            
            <?php if (isset($_SESSION['b2b_cari_id'])): ?>
                <!-- Sepetim -->
                <a href="sepet.php" class="mobile-nav-item <?php echo ($current_page == 'sepet') ? 'active' : ''; ?>">
                    <svg class="mobile-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="9" cy="21" r="1"></circle>
                        <circle cx="20" cy="21" r="1"></circle>
                        <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                    </svg>
                    <span class="mobile-nav-text">Sepetim</span>
                    <!-- Sepet badge'i için yer ayrıldı -->
                    <span class="mobile-nav-badge" id="mobile-cart-badge" style="display: none;">0</span>
                </a>
                
                <!-- Hesabım -->
                <a href="hesabim.php" class="mobile-nav-item <?php echo ($current_page == 'hesabim') ? 'active' : ''; ?>">
                    <svg class="mobile-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    <span class="mobile-nav-text">Hesabım</span>
                </a>
            <?php else: ?>
                <!-- Bayi Ol -->
                <a href="bayi_ol.php" class="mobile-nav-item <?php echo ($current_page == 'bayi_ol') ? 'active' : ''; ?>">
                    <svg class="mobile-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                    <span class="mobile-nav-text">Bayi Ol</span>
                </a>
                
                <!-- Giriş Yap -->
                <a href="login.php" class="mobile-nav-item <?php echo ($current_page == 'login') ? 'active' : ''; ?>">
                    <svg class="mobile-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                        <polyline points="10 17 15 12 10 7"></polyline>
                        <line x1="15" y1="12" x2="3" y2="12"></line>
                    </svg>
                    <span class="mobile-nav-text">Giriş</span>
                </a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html> 