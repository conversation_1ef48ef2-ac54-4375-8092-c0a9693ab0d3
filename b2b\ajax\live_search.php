<?php
// Canlı arama için AJAX backend
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// CORS için (gerekirse)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

include '../config.php';

// POST kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Yalnızca POST istekleri kabul edilir']);
    exit;
}

// Arama sorgusu al
$query = isset($_POST['q']) ? trim($_POST['q']) : '';
$avmpark_mode = isset($_POST['avmpark']) && $_POST['avmpark'] == '1';

// Minimum karakter kontrolü
if (strlen($query) < 2) {
    echo json_encode([]);
    exit;
}

// SQL injection koruması
$query = $conn->real_escape_string($query);

// Stok hesaplama fonksiyonu
function getEnDusukPaketAdedi($urun_id, $conn, $avmpark_mode = false) {
    $takip_kodu_filter = $avmpark_mode ? "takip_kodu LIKE 'P%'" : "(takip_kodu IS NULL OR takip_kodu LIKE 'T%' OR takip_kodu NOT LIKE 'P%')";
    
    $sql = "
        SELECT 
            SUBSTRING_INDEX(paket_kodu, '-', -1) as grup,
            SUM(CASE WHEN durum = 'stokta' THEN 1 ELSE 0 END) as stok_adet
        FROM paketler 
        WHERE urun_id = ? AND $takip_kodu_filter
        GROUP BY grup
        HAVING stok_adet > 0
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $urun_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return 0;
    }
    
    $min_adet = PHP_INT_MAX;
    while ($row = $result->fetch_assoc()) {
        if ($row['stok_adet'] < $min_adet) {
            $min_adet = $row['stok_adet'];
        }
    }
    
    return $min_adet === PHP_INT_MAX ? 0 : $min_adet;
}

try {
    // Paketler tablosu varlık kontrolü
    $paketler_exists = $conn->query("SHOW TABLES LIKE 'paketler'")->num_rows > 0;
    
    // Arama sorgusu
    if ($avmpark_mode) {
        // AVMPARK modu - sadece P kodlu ürünler
        $sql = "
        SELECT DISTINCT
            u.id,
            u.stok_adi,
            u.stok_kodu,
            u.renk,
            u.fiyat,
            COALESCE(u.resim1, u.resim2, u.resim3) AS resim,
            u.stok_adi as gosterim_adi,
            '' as paket_adi,
            '' as renk_detay,
            MIN(p.takip_kodu) as takip_kodu
        FROM urunler u
        INNER JOIN paketler p ON u.id = p.urun_id
        WHERE u.aktif = 1 
        AND p.takip_kodu LIKE 'P%'
        AND (
            u.stok_adi LIKE '%$query%' 
            OR u.stok_kodu LIKE '%$query%'
            OR p.takip_kodu LIKE '%$query%'
        )
        GROUP BY u.id
        ORDER BY 
            CASE 
                WHEN u.stok_adi LIKE '$query%' THEN 1
                WHEN u.stok_kodu LIKE '$query%' THEN 2
                WHEN p.takip_kodu LIKE '$query%' THEN 3
                ELSE 4
            END,
            u.stok_adi
        LIMIT 8
        ";
    } else {
        // Normal mod - T kodlu ve P olmayan ürünler
        $sql = "
        (
            -- Normal ürünler
            SELECT 
                u.id,
                u.stok_adi,
                u.stok_kodu,
                u.renk,
                u.fiyat,
                COALESCE(u.resim1, u.resim2, u.resim3) AS resim,
                u.stok_adi as gosterim_adi,
                '' as paket_adi,
                '' as renk_detay,
                " . ($paketler_exists ? "MIN(p.takip_kodu)" : "''") . " as takip_kodu
            FROM urunler u
            " . ($paketler_exists ? "LEFT JOIN paketler p ON u.id = p.urun_id AND (p.takip_kodu IS NULL OR p.takip_kodu NOT LIKE 'P%')" : "") . "
            WHERE u.aktif = 1 
            AND NOT EXISTS (SELECT 1 FROM paketler p2 WHERE p2.urun_id = u.id AND p2.takip_kodu LIKE 'P%')
            AND (
                u.stok_adi LIKE '%$query%' 
                OR u.stok_kodu LIKE '%$query%'
                " . ($paketler_exists ? "OR p.takip_kodu LIKE '%$query%'" : "") . "
            )
            GROUP BY u.id
        )
        
        UNION
        
        (
            -- Model bileşenleri
            SELECT 
                u.id,
                u.stok_adi,
                u.stok_kodu,
                u.renk,
                COALESCE(p.fiyat, u.fiyat, 0) as fiyat,
                COALESCE(pr.dosya_adi, u.resim1, u.resim2, u.resim3) AS resim,
                CONCAT(p.paket_adi, ' (', p.renk, ')') as gosterim_adi,
                p.paket_adi,
                p.renk as renk_detay,
                p.takip_kodu
            FROM urunler u
            INNER JOIN paketler p ON u.id = p.urun_id
            INNER JOIN bilesenler b ON p.bilesen_id = b.id
            LEFT JOIN paket_renk_resimleri pr ON (pr.urun_id = u.id AND pr.paket_adi = p.paket_adi AND pr.renk = p.renk)
            WHERE b.tip = 'model' 
            AND u.aktif = 1
            AND p.aktif = 1
            AND (p.takip_kodu IS NULL OR p.takip_kodu NOT LIKE 'P%')
            AND (
                u.stok_adi LIKE '%$query%' 
                OR u.stok_kodu LIKE '%$query%'
                OR p.paket_adi LIKE '%$query%'
                OR p.takip_kodu LIKE '%$query%'
            )
            GROUP BY u.id, p.paket_adi, p.renk
        )
        
        ORDER BY 
            CASE 
                WHEN stok_adi LIKE '$query%' THEN 1
                WHEN stok_kodu LIKE '$query%' THEN 2
                WHEN takip_kodu LIKE '$query%' THEN 3
                ELSE 4
            END,
            gosterim_adi
        LIMIT 8
        ";
    }
    
    $result = $conn->query($sql);
    $urunler = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            // Stok hesapla
            $row['stok_adedi'] = getEnDusukPaketAdedi($row['id'], $conn, $avmpark_mode);
            
            // Resim URL'sini düzelt
            if (!empty($row['resim'])) {
                // Dosya yolu kontrolü
                if (strpos($row['resim'], 'uploads/') === false && strpos($row['resim'], 'http') === false) {
                    $row['resim'] = $row['resim']; // Sadece dosya adı
                }
            }
            
            $urunler[] = $row;
        }
    }
    
    // JSON olarak döndür
    echo json_encode($urunler, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Arama sırasında bir hata oluştu',
        'details' => $e->getMessage()
    ]);
}

$conn->close();
?> 