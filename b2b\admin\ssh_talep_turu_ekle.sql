-- B2B SSH Kayıtları tablosuna talep_turu sütunu ekleme
-- Bu sütun müşteri ve admin taleplerini ayırmak için kullanılacak

ALTER TABLE b2b_ssh_kayitlari 
ADD COLUMN talep_turu ENUM('admin', 'musteri') DEFAULT 'admin' 
AFTER durum;

-- Mevcut kayıtları admin talebi olarak işaretle (varsa)
UPDATE b2b_ssh_kayitlari 
SET talep_turu = 'admin' 
WHERE talep_turu IS NULL;

-- Kontrol için kayıt sayısını göster
SELECT 
    talep_turu, 
    COUNT(*) as adet 
FROM b2b_ssh_kayitlari 
GROUP BY talep_turu;

-- Tablo yapısını kontrol et
DESCRIBE b2b_ssh_kayitlari; 