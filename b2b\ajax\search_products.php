<?php
header('Content-Type: application/json');
include '../config.php';

if (!isset($_GET['q']) || strlen(trim($_GET['q'])) < 2) {
    echo json_encode([]);
    exit;
}

$query = trim($_GET['q']);
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

// SQL injection koruması
$search_query = "%" . $query . "%";

// Ana arama sorgusu
$sql = "SELECT 
            u.id,
            u.urun_adi,
            u.fiyat,
            k.kategori_adi as kategori,
            COALESCE(ur.dosya_adi, '') as resim_url
        FROM urunler u
        LEFT JOIN b2b_kategoriler k ON u.kategori_id = k.id
        LEFT JOIN urun_resimleri ur ON u.id = ur.urun_id AND ur.ana_resim = 1
        WHERE (
            u.urun_adi LIKE ? 
            OR u.aciklama LIKE ?
            OR u.urun_kodu LIKE ?
            OR k.kategori_adi LIKE ?
        )
        AND u.aktif = 1
        ORDER BY 
            CASE 
                WHEN u.urun_adi LIKE ? THEN 1
                WHEN u.urun_adi LIKE ? THEN 2
                WHEN u.urun_kodu LIKE ? THEN 3
                ELSE 4
            END,
            u.urun_adi ASC
        LIMIT ?";

$stmt = $conn->prepare($sql);

// Arama parametreleri
$exact_match = $query . "%";
$contains_match = "%" . $query . "%";

$stmt->bind_param(
    'sssssssi', 
    $search_query,      // urun_adi LIKE
    $search_query,      // aciklama LIKE  
    $search_query,      // urun_kodu LIKE
    $search_query,      // kategori_adi LIKE
    $exact_match,       // ORDER BY - başında eşleşme
    $contains_match,    // ORDER BY - içinde eşleşme  
    $exact_match,       // ORDER BY - kod başında eşleşme
    $limit
);

$stmt->execute();
$result = $stmt->get_result();

$products = [];
while ($row = $result->fetch_assoc()) {
    // Resim URL'ini düzenle
    if (!empty($row['resim_url'])) {
        $row['resim_url'] = '../uploads/urun_resimleri/' . $row['resim_url'];
    } else {
        $row['resim_url'] = '../images/no-image.jpg';
    }
    
    // Fiyatı formatla
    if (!empty($row['fiyat'])) {
        $row['fiyat'] = number_format($row['fiyat'], 2, ',', '.');
    }
    
    $products[] = $row;
}

echo json_encode($products);
?> 