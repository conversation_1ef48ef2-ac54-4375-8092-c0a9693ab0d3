<?php
session_start();
include dirname(__DIR__) . '/config.php';

// Page bilgileri
$page_title = "SSH Detay - B2B Admin";
$current_page = "ssh_kayitlari";

$mesaj = '';
$mesaj_tur = '';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];

$ssh_id = intval($_GET['id'] ?? 0);

if (!$ssh_id) {
    header('Location: ssh_kayitlari.php');
    exit;
}

// SSH durumu güncelleme
if (isset($_POST['durum_guncelle'])) {
    $yeni_durum = $_POST['yeni_durum'];
    $admin_notu = trim($_POST['admin_notu']);
    
    // Mevcut durumu al
    $eski_durum_sql = "SELECT durum FROM b2b_ssh_kayitlari WHERE id = ?";
    $eski_durum_stmt = $conn->prepare($eski_durum_sql);
    $eski_durum_stmt->bind_param('i', $ssh_id);
    $eski_durum_stmt->execute();
    $eski_durum_result = $eski_durum_stmt->get_result();
    $eski_durum_row = $eski_durum_result->fetch_assoc();
    $eski_durum = $eski_durum_row['durum'];
    
    // SSH kaydını güncelle
    $update_sql = "UPDATE b2b_ssh_kayitlari SET durum = ?, admin_notu = ?, islem_yapan_admin_id = ?, updated_at = NOW()";
    $params = [$yeni_durum, $admin_notu, $admin_id];
    $types = "ssi";
    
    // Eğer durum tamamlandı ise çıkış tarihini de güncelle
    if ($yeni_durum == 'tamamlandi') {
        $update_sql .= ", cikis_tarihi = NOW()";
    }
    
    $update_sql .= " WHERE id = ?";
    $params[] = $ssh_id;
    $types .= "i";
    
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param($types, ...$params);
    
    if ($update_stmt->execute()) {
        // Durum geçmişine kaydet
        $history_sql = "INSERT INTO b2b_ssh_durum_gecmisi (ssh_id, eski_durum, yeni_durum, degisiklik_notu, degistiren_admin_id) VALUES (?, ?, ?, ?, ?)";
        $history_stmt = $conn->prepare($history_sql);
        $history_stmt->bind_param('isssi', $ssh_id, $eski_durum, $yeni_durum, $admin_notu, $admin_id);
        $history_stmt->execute();
        
        $mesaj = "SSH kaydı durumu başarıyla güncellendi.";
        $mesaj_tur = "success";
    } else {
        $mesaj = "SSH kaydı güncellenirken hata oluştu.";
        $mesaj_tur = "danger";
    }
}

// SSH kaydını getir
$ssh_sql = "SELECT s.*, 
            cf.firm_name as musteri_adi, cf.telefon as musteri_telefon, cf.email as musteri_email,
            bs.id as siparis_no, bs.created_at as siparis_tarihi,
            u.stok_kodu, u.stok_adi,
            ba.kullanici_adi as islem_yapan_admin
            FROM b2b_ssh_kayitlari s
            LEFT JOIN cari_firmalar cf ON s.cari_id = cf.id
            LEFT JOIN b2b_siparisler bs ON s.siparis_id = bs.id
            LEFT JOIN urunler u ON s.urun_id = u.id
            LEFT JOIN b2b_admin ba ON s.islem_yapan_admin_id = ba.id
            WHERE s.id = ?";
$ssh_stmt = $conn->prepare($ssh_sql);
$ssh_stmt->bind_param('i', $ssh_id);
$ssh_stmt->execute();
$ssh_result = $ssh_stmt->get_result();
$ssh = $ssh_result->fetch_assoc();

if (!$ssh) {
    header('Location: ssh_kayitlari.php');
    exit;
}

// Durum geçmişini getir
$history_sql = "SELECT h.*, a.kullanici_adi as admin_adi 
                FROM b2b_ssh_durum_gecmisi h
                LEFT JOIN b2b_admin a ON h.degistiren_admin_id = a.id
                WHERE h.ssh_id = ?
                ORDER BY h.degisiklik_tarihi DESC";
$history_stmt = $conn->prepare($history_sql);
$history_stmt->bind_param('i', $ssh_id);
$history_stmt->execute();
$history_result = $history_stmt->get_result();
$durum_gecmisi = [];
while ($row = $history_result->fetch_assoc()) {
    $durum_gecmisi[] = $row;
}

// Header dahil et
include __DIR__ . '/includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-tools"></i>
            SSH Detay #<?php echo $ssh['id']; ?>
        </h1>
        <div style="display: flex; gap: 1rem;">
            <a href="ssh_duzenle.php?id=<?php echo $ssh['id']; ?>" class="btn">
                <i class="fas fa-edit"></i>
                Düzenle
            </a>
            <a href="ssh_kayitlari.php" class="btn" style="background: var(--gray);">
                <i class="fas fa-arrow-left"></i>
                Geri Dön
            </a>
        </div>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
    <!-- Ana Bilgiler -->
    <div class="content-card">
        <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-info-circle"></i>
            SSH Bilgileri
        </h3>
        
        <div style="display: grid; gap: 1.5rem;">
            <!-- Müşteri Bilgileri -->
            <div class="info-section">
                <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                    <i class="fas fa-user"></i> Müşteri Bilgileri
                </h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                    <div>
                        <strong>Firma Adı:</strong><br>
                        <span style="color: var(--gray);"><?php echo htmlspecialchars($ssh['musteri_adi']); ?></span>
                    </div>
                    <div>
                        <strong>Telefon:</strong><br>
                        <span style="color: var(--gray);"><?php echo htmlspecialchars($ssh['musteri_telefon'] ?: '-'); ?></span>
                    </div>
                    <div>
                        <strong>Email:</strong><br>
                        <span style="color: var(--gray);"><?php echo htmlspecialchars($ssh['musteri_email'] ?: '-'); ?></span>
                    </div>
                    <?php if ($ssh['siparis_no']): ?>
                    <div>
                        <strong>Sipariş No:</strong><br>
                        <span style="color: var(--gray);">#<?php echo $ssh['siparis_no']; ?> (<?php echo date('d.m.Y', strtotime($ssh['siparis_tarihi'])); ?>)</span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Ürün Bilgileri -->
            <div class="info-section">
                <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                    <i class="fas fa-box"></i> Ürün Bilgileri
                </h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                    <div>
                        <strong>Ürün Adı:</strong><br>
                        <span style="color: var(--gray);"><?php echo htmlspecialchars($ssh['urun_adi']); ?></span>
                    </div>
                    <?php if ($ssh['stok_kodu']): ?>
                    <div>
                        <strong>Stok Kodu:</strong><br>
                        <span style="color: var(--gray);"><?php echo htmlspecialchars($ssh['stok_kodu']); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if ($ssh['parca_adi']): ?>
                    <div>
                        <strong>Parça Adı:</strong><br>
                        <span style="color: var(--gray);"><?php echo htmlspecialchars($ssh['parca_adi']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- SSH Detayları -->
            <div class="info-section">
                <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                    <i class="fas fa-tools"></i> SSH Detayları
                </h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                    <div>
                        <strong>SSH Nedeni:</strong><br>
                        <span class="reason-badge" style="padding: 0.25rem 0.5rem; border-radius: 0.375rem; font-size: 0.8rem; background-color: rgba(107, 114, 128, 0.1); color: var(--gray);">
                            <?php 
                            $nedeni_labels = [
                                'hasar' => 'Hasar',
                                'iade' => 'İade',
                                'degisim' => 'Değişim',
                                'garanti' => 'Garanti',
                                'kalite_sorunu' => 'Kalite Sorunu',
                                'yanlis_urun' => 'Yanlış Ürün',
                                'diger' => 'Diğer'
                            ];
                            echo $nedeni_labels[$ssh['ssh_nedeni']] ?? $ssh['ssh_nedeni'];
                            ?>
                        </span>
                    </div>
                    <div>
                        <strong>SSH Kaynağı:</strong><br>
                        <span style="color: var(--gray);"><?php echo ucfirst($ssh['ssh_kaynagi']); ?></span>
                    </div>
                    <div>
                        <strong>Giriş Tarihi:</strong><br>
                        <span style="color: var(--gray);"><?php echo date('d.m.Y H:i', strtotime($ssh['giris_tarihi'])); ?></span>
                    </div>
                    <?php if ($ssh['cikis_tarihi']): ?>
                    <div>
                        <strong>Çıkış Tarihi:</strong><br>
                        <span style="color: var(--gray);"><?php echo date('d.m.Y H:i', strtotime($ssh['cikis_tarihi'])); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Açıklama -->
            <?php if ($ssh['aciklama']): ?>
            <div class="info-section">
                <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                    <i class="fas fa-comment"></i> Açıklama
                </h4>
                <div style="background: rgba(107, 114, 128, 0.05); padding: 1rem; border-radius: 0.5rem; border-left: 4px solid var(--gray);">
                    <?php echo nl2br(htmlspecialchars($ssh['aciklama'])); ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Admin Notu -->
            <?php if ($ssh['admin_notu']): ?>
            <div class="info-section">
                <h4 style="margin-bottom: 1rem; color: var(--dark); border-bottom: 2px solid var(--gray-light); padding-bottom: 0.5rem;">
                    <i class="fas fa-sticky-note"></i> Admin Notu
                </h4>
                <div style="background: rgba(249, 115, 22, 0.05); padding: 1rem; border-radius: 0.5rem; border-left: 4px solid var(--secondary);">
                    <?php echo nl2br(htmlspecialchars($ssh['admin_notu'])); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Yan Panel -->
    <div style="display: grid; gap: 2rem;">
        <!-- Durum Güncelleme -->
        <div class="content-card">
            <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-flag"></i>
                Durum Güncelle
            </h3>
            
            <div style="margin-bottom: 1rem;">
                <strong>Mevcut Durum:</strong><br>
                <span class="status-badge status-<?php echo $ssh['durum']; ?>" style="padding: 0.5rem 1rem; border-radius: 0.5rem; font-size: 0.875rem; margin-top: 0.5rem; display: inline-flex; align-items: center; gap: 0.5rem;">
                    <?php 
                    $durum_icons = [
                        'beklemede' => 'clock',
                        'inceleniyor' => 'search',
                        'gonderildi' => 'shipping-fast',
                        'onaylandi' => 'check',
                        'reddedildi' => 'times',
                        'tamamlandi' => 'check-double',
                        'iptal' => 'ban'
                    ];
                    $durum_labels = [
                        'beklemede' => 'Beklemede',
                        'inceleniyor' => 'İnceleniyor',
                        'gonderildi' => 'Gönderildi',
                        'onaylandi' => 'Onaylandı',
                        'reddedildi' => 'Reddedildi',
                        'tamamlandi' => 'Tamamlandı',
                        'iptal' => 'İptal'
                    ];
                    ?>
                    <i class="fas fa-<?php echo $durum_icons[$ssh['durum']] ?? 'question'; ?>"></i>
                    <?php echo $durum_labels[$ssh['durum']] ?? $ssh['durum']; ?>
                </span>
            </div>
            
            <form method="post" style="display: grid; gap: 1rem;">
                <div>
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">Yeni Durum</label>
                    <select name="yeni_durum" required style="width: 100%; padding: 0.75rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                        <option value="beklemede" <?php echo ($ssh['durum'] == 'beklemede') ? 'selected' : ''; ?>>Beklemede</option>
                        <option value="inceleniyor" <?php echo ($ssh['durum'] == 'inceleniyor') ? 'selected' : ''; ?>>İnceleniyor</option>
                        <option value="gonderildi" <?php echo ($ssh['durum'] == 'gonderildi') ? 'selected' : ''; ?>>Gönderildi</option>
                        <option value="onaylandi" <?php echo ($ssh['durum'] == 'onaylandi') ? 'selected' : ''; ?>>Onaylandı</option>
                        <option value="reddedildi" <?php echo ($ssh['durum'] == 'reddedildi') ? 'selected' : ''; ?>>Reddedildi</option>
                        <option value="tamamlandi" <?php echo ($ssh['durum'] == 'tamamlandi') ? 'selected' : ''; ?>>Tamamlandı</option>
                        <option value="iptal" <?php echo ($ssh['durum'] == 'iptal') ? 'selected' : ''; ?>>İptal</option>
                    </select>
                </div>
                
                <div>
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">Admin Notu</label>
                    <textarea name="admin_notu" rows="3" style="width: 100%; padding: 0.75rem; border: 2px solid var(--gray-light); border-radius: 0.5rem; resize: vertical;" placeholder="Durum değişikliği ile ilgili not..."><?php echo htmlspecialchars($ssh['admin_notu']); ?></textarea>
                </div>
                
                <button type="submit" name="durum_guncelle" class="btn">
                    <i class="fas fa-save"></i>
                    Durumu Güncelle
                </button>
            </form>
        </div>
        
        <!-- Durum Geçmişi -->
        <div class="content-card">
            <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-history"></i>
                Durum Geçmişi
            </h3>
            
            <?php if (empty($durum_gecmisi)): ?>
                <p style="color: var(--gray); text-align: center; padding: 2rem;">Henüz durum değişikliği bulunmuyor.</p>
            <?php else: ?>
                <div style="display: grid; gap: 1rem;">
                    <?php foreach ($durum_gecmisi as $gecmis): ?>
                    <div style="background: rgba(107, 114, 128, 0.05); padding: 1rem; border-radius: 0.5rem; border-left: 4px solid var(--gray);">
                        <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 0.5rem;">
                            <div>
                                <?php if ($gecmis['eski_durum']): ?>
                                    <span style="font-size: 0.8rem; color: var(--gray);"><?php echo ucfirst($gecmis['eski_durum']); ?></span>
                                    <i class="fas fa-arrow-right" style="margin: 0 0.5rem; color: var(--gray);"></i>
                                <?php endif; ?>
                                <span style="font-weight: 600; color: var(--dark);"><?php echo ucfirst($gecmis['yeni_durum']); ?></span>
                            </div>
                        </div>
                        <div style="font-size: 0.8rem; color: var(--gray); margin-bottom: 0.5rem;">
                            <?php echo date('d.m.Y H:i', strtotime($gecmis['degisiklik_tarihi'])); ?>
                            <?php if ($gecmis['admin_adi']): ?>
                                - <?php echo htmlspecialchars($gecmis['admin_adi']); ?>
                            <?php endif; ?>
                        </div>
                        <?php if ($gecmis['degisiklik_notu']): ?>
                            <div style="font-size: 0.9rem;">
                                <?php echo nl2br(htmlspecialchars($gecmis['degisiklik_notu'])); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.status-beklemede {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}
.status-inceleniyor {
    background-color: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border: 1px solid rgba(59, 130, 246, 0.2);
}
.status-gonderildi {
    background-color: rgba(139, 69, 19, 0.1);
    color: #8b4513;
    border: 1px solid rgba(139, 69, 19, 0.2);
}
.status-onaylandi {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}
.status-reddedildi, .status-iptal {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}
.status-tamamlandi {
    background-color: rgba(34, 197, 94, 0.1);
    color: #15803d;
    border: 1px solid rgba(34, 197, 94, 0.2);
}
</style>

<?php include __DIR__ . '/includes/footer.php'; ?> 