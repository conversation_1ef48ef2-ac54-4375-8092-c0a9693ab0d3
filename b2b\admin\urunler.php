<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "Ürünler - B2B Admin";
$current_page = "urunler";

$mesaj = '';
$mesaj_tur = '';

// Kategorileri çek
$kategoriler = [];
$kat_sql = "SELECT id, kategori_adi FROM b2b_kategoriler ORDER BY sira, kategori_adi";
$kat_result = $conn->query($kat_sql);
if ($kat_result && $kat_result->num_rows > 0) {
    while ($row = $kat_result->fetch_assoc()) {
        $kategoriler[] = $row;
    }
}

// Seçili ürünlere kategori atama
if (isset($_POST['secili_urunlere_kategori_ata'])) {
    $secili_urunler = isset($_POST['secili_urunler']) ? $_POST['secili_urunler'] : [];
    $atanacak_kategori = $_POST['atanacak_kategori'];
    
    if (!empty($secili_urunler) && !empty($atanacak_kategori)) {
        $basarili = 0;
        foreach ($secili_urunler as $urun_id) {
            // Ana urunler tablosunda kategori_id kolonunu güncelle
            $update_sql = "UPDATE urunler SET kategori_id = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param('ii', $atanacak_kategori, $urun_id);
            if ($update_stmt->execute()) {
                $basarili++;
            }
        }
        $mesaj = "$basarili ürüne kategori başarıyla atandı.";
        $mesaj_tur = "success";
    } else {
        $mesaj = "Lütfen ürün seçin ve kategori belirtin.";
        $mesaj_tur = "warning";
    }
}

// Ana veritabanından ürünleri listele
$urunler = [];
$kategori_filtre = isset($_GET['kategori']) ? intval($_GET['kategori']) : 0;

if ($kategori_filtre) {
    $sql = "SELECT u.id, u.stok_kodu, u.stok_adi, u.kategori_id, bk.kategori_adi
            FROM urunler u
            LEFT JOIN b2b_kategoriler bk ON u.kategori_id = bk.id
            WHERE u.kategori_id = ?
            ORDER BY u.id DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $kategori_filtre);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $sql = "SELECT u.id, u.stok_kodu, u.stok_adi, u.kategori_id, bk.kategori_adi
            FROM urunler u
            LEFT JOIN b2b_kategoriler bk ON u.kategori_id = bk.id
            ORDER BY u.id DESC";
    $result = $conn->query($sql);
}

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $urunler[] = $row;
    }
}

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-box"></i>
            Ürün Yönetimi
        </h1>
        <a href="urun_ekle.php" class="btn">
            <i class="fas fa-plus"></i>
            Yeni Ürün Ekle
        </a>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<!-- Kategori Filtreleme -->
<div class="content-card" style="margin-bottom: 1rem;">
    <h3 style="margin-bottom: 1rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-filter"></i>
        Kategoriye Göre Ürün Ataması
    </h3>
    <div style="display: grid; grid-template-columns: 2fr 1fr auto; gap: 1rem; align-items: end;">
        <div>
            <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">Kategori Filtresi</label>
            <select style="width: 100%; padding: 0.75rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" onchange="window.location.href='?kategori=' + this.value">
                <option value="">Tüm Ürünler</option>
                <?php foreach ($kategoriler as $kategori): ?>
                <option value="<?php echo $kategori['id']; ?>" <?php echo ($kategori_filtre == $kategori['id']) ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($kategori['kategori_adi']); ?>
                </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div>
            <form method="post" style="display: flex; gap: 1rem; align-items: end;">
                <div style="flex: 1;">
                    <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">Seçili Ürünlere Kategori Ata</label>
                    <select name="atanacak_kategori" style="width: 100%; padding: 0.75rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;" required>
                        <option value="">Kategori Seçin</option>
                        <?php foreach ($kategoriler as $kategori): ?>
                        <option value="<?php echo $kategori['id']; ?>">
                            <?php echo htmlspecialchars($kategori['kategori_adi']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <button type="submit" name="secili_urunlere_kategori_ata" class="btn">
                    <i class="fas fa-tags"></i>
                    Kategori Ata
                </button>
            </form>
        </div>
    </div>
</div>

<div class="content-card">
    <?php if (empty($urunler)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--gray);">
            <i class="fas fa-box" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem;">Henüz ürün bulunmuyor</h3>
            <p>Yeni ürün eklemek için yukarıdaki "Yeni Ürün Ekle" butonunu kullanabilirsiniz.</p>
        </div>
    <?php else: ?>
        <style>
            .products-table {
                width: 100%;
                border-collapse: collapse;
                border-radius: 0.75rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            }
            .products-table th {
                background: linear-gradient(135deg, var(--primary), var(--primary-dark));
                color: white;
                font-weight: 600;
                padding: 1rem;
                text-align: left;
                font-size: 0.875rem;
            }
            .products-table td {
                padding: 1rem;
                border-bottom: 1px solid var(--gray-light);
                vertical-align: middle;
            }
            .products-table tr:hover {
                background-color: rgba(249, 115, 22, 0.05);
            }
            .products-table tr:last-child td {
                border-bottom: none;
            }
            .action-links {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                align-items: center;
            }
            .action-btn {
                padding: 0.5rem 0.875rem;
                border-radius: 0.5rem;
                text-decoration: none;
                font-size: 0.8rem;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.375rem;
                transition: all 0.3s ease;
                border: 1px solid transparent;
            }
            .action-btn:hover {
                transform: translateY(-1px);
            }
            .action-view {
                background-color: rgba(59, 130, 246, 0.1);
                color: #1e40af;
                border-color: rgba(59, 130, 246, 0.2);
            }
            .action-view:hover {
                background-color: #3b82f6;
                color: white;
            }
            .category-badge {
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                font-size: 0.75rem;
                font-weight: 600;
                background-color: rgba(139, 92, 246, 0.1);
                color: #7c3aed;
                border: 1px solid rgba(139, 92, 246, 0.2);
            }
            .no-category {
                background-color: rgba(107, 114, 128, 0.1);
                color: var(--gray);
                border: 1px solid rgba(107, 114, 128, 0.2);
            }
        </style>
        
        <form method="post">
            <div style="overflow-x: auto;">
                <table class="products-table">
                    <thead>
                        <tr>
                            <th style="width: 40px;">
                                <input type="checkbox" id="select-all" onchange="toggleAllCheckboxes(this)">
                            </th>
                            <th>ID</th>
                            <th>Ürün Kodu</th>
                            <th>Ürün Adı</th>
                            <th>Kategori</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($urunler as $urun): ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="secili_urunler[]" value="<?php echo $urun['id']; ?>" class="urun-checkbox">
                            </td>
                            <td style="font-weight: 600;"><?php echo $urun['id']; ?></td>
                            <td style="font-weight: 600; color: var(--secondary);">
                                <?php echo htmlspecialchars($urun['stok_kodu']); ?>
                            </td>
                            <td style="font-weight: 600; color: var(--dark);">
                                <?php echo htmlspecialchars($urun['stok_adi']); ?>
                            </td>
                            <td>
                                <?php if ($urun['kategori_adi']): ?>
                                    <span class="category-badge">
                                        <i class="fas fa-tag"></i>
                                        <?php echo htmlspecialchars($urun['kategori_adi']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="category-badge no-category">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Kategori Yok
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="action-links">
                                    <a href="urun_detay.php?id=<?php echo $urun['id']; ?>" class="action-btn action-view" title="Detay Görüntüle">
                                        <i class="fas fa-eye"></i>
                                        Detay
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </form>
    <?php endif; ?>
</div>

<script>
function toggleAllCheckboxes(source) {
    const checkboxes = document.querySelectorAll('.urun-checkbox');
    for (let checkbox of checkboxes) {
        checkbox.checked = source.checked;
    }
}
</script>

<?php include 'includes/footer.php'; ?> 