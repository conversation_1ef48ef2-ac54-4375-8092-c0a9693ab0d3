<?php
session_start();
include '../config.php';
include 'includes/admin_check.php';

$page_title = "Banner Yönetimi";
$current_page = "banners";

// CRUD İşlemleri
if (isset($_POST['action'])) {
    if ($_POST['action'] == 'add') {
        $baslik = $_POST['baslik'];
        $aciklama = $_POST['aciklama'];
        $link_url = $_POST['link_url'];
        $button_text = $_POST['button_text'];
        $sira = $_POST['sira'];
        $aktif = isset($_POST['aktif']) ? 1 : 0;
        
        // Resim upload işlemi
        $resim_url = '';
        if (isset($_FILES['banner_image']) && $_FILES['banner_image']['error'] == 0) {
            // Dinamik yol belirleme (canlı ortam uyumlu)
            $upload_dir = dirname(__DIR__) . "/uploads/banners/";
            $web_path = "uploads/banners/";
            
            // Klasör yoksa oluştur
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES["banner_image"]["name"], PATHINFO_EXTENSION));
            $file_name = time() . "_" . uniqid() . "." . $file_extension;
            $target_file = $upload_dir . $file_name;
            
            // Dosya türü kontrolü
            $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'webp');
            if (in_array($file_extension, $allowed_types)) {
                // Dosya boyutu kontrolü (5MB max)
                if ($_FILES["banner_image"]["size"] <= 5000000) {
                    if (move_uploaded_file($_FILES["banner_image"]["tmp_name"], $target_file)) {
                        $resim_url = $web_path . $file_name;
                    } else {
                        $error_message = "Dosya yüklenirken hata oluştu!";
                    }
                } else {
                    $error_message = "Dosya boyutu çok büyük! (Max 5MB)";
                }
            } else {
                $error_message = "Sadece JPG, JPEG, PNG, GIF, WebP dosyaları kabul edilir!";
            }
        } else if (!empty($_POST['resim_url'])) {
            $resim_url = $_POST['resim_url'];
        }
        
        if (!isset($error_message) && !empty($resim_url)) {
            $sql = "INSERT INTO b2b_banners (baslik, aciklama, resim_url, link_url, button_text, sira, aktif) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('sssssii', $baslik, $aciklama, $resim_url, $link_url, $button_text, $sira, $aktif);
            $stmt->execute();
            $success_message = "Banner başarıyla eklendi!";
        } else if (!isset($error_message)) {
            $error_message = "Lütfen bir resim yükleyin veya resim URL'si girin!";
        }
    }
    
    if ($_POST['action'] == 'edit') {
        $id = $_POST['id'];
        $baslik = $_POST['baslik'];
        $aciklama = $_POST['aciklama'];
        $link_url = $_POST['link_url'];
        $button_text = $_POST['button_text'];
        $sira = $_POST['sira'];
        $aktif = isset($_POST['aktif']) ? 1 : 0;
        
        // Mevcut resim URL'sini al
        $sql = "SELECT resim_url FROM b2b_banners WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $current_banner = $result->fetch_assoc();
        $resim_url = $current_banner['resim_url'];
        
        // Yeni resim upload edildi mi?
        if (isset($_FILES['banner_image']) && $_FILES['banner_image']['error'] == 0) {
            // Dinamik yol belirleme (canlı ortam uyumlu)
            $upload_dir = dirname(__DIR__) . "/uploads/banners/";
            $web_path = "uploads/banners/";
            
            // Klasör yoksa oluştur
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES["banner_image"]["name"], PATHINFO_EXTENSION));
            $file_name = time() . "_" . uniqid() . "." . $file_extension;
            $target_file = $upload_dir . $file_name;
            
            // Dosya türü kontrolü
            $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'webp');
            if (in_array($file_extension, $allowed_types)) {
                // Dosya boyutu kontrolü (5MB max)
                if ($_FILES["banner_image"]["size"] <= 5000000) {
                    if (move_uploaded_file($_FILES["banner_image"]["tmp_name"], $target_file)) {
                        // Eski dosyayı sil (eğer local dosya ise)
                        if (strpos($resim_url, 'uploads/banners/') !== false) {
                            $old_file = dirname(__DIR__) . "/" . $resim_url;
                            if (file_exists($old_file)) {
                                unlink($old_file);
                            }
                        }
                        $resim_url = $web_path . $file_name;
                    } else {
                        $error_message = "Dosya yüklenirken hata oluştu!";
                    }
                } else {
                    $error_message = "Dosya boyutu çok büyük! (Max 5MB)";
                }
            } else {
                $error_message = "Sadece JPG, JPEG, PNG, GIF, WebP dosyaları kabul edilir!";
            }
        } else if (!empty($_POST['resim_url']) && $_POST['resim_url'] != $resim_url) {
            $resim_url = $_POST['resim_url'];
        }
        
        if (!isset($error_message)) {
            $sql = "UPDATE b2b_banners SET baslik=?, aciklama=?, resim_url=?, link_url=?, button_text=?, sira=?, aktif=? WHERE id=?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('sssssiii', $baslik, $aciklama, $resim_url, $link_url, $button_text, $sira, $aktif, $id);
            $stmt->execute();
            $success_message = "Banner başarıyla güncellendi!";
        }
    }
    
    if ($_POST['action'] == 'update_order') {
        $order_data = json_decode($_POST['order_data'], true);
        
        if ($order_data && is_array($order_data)) {
            foreach ($order_data as $item) {
                $id = intval($item['id']);
                $order = intval($item['order']);
                
                $sql = "UPDATE b2b_banners SET sira = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('ii', $order, $id);
                $stmt->execute();
            }
            echo "OK"; // AJAX response
            exit;
        }
    }
}

if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    
    // Önce resmi sil
    $sql = "SELECT resim_url FROM b2b_banners WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $banner = $result->fetch_assoc();
    
    if ($banner && strpos($banner['resim_url'], 'uploads/banners/') !== false) {
        $file_path = dirname(__DIR__) . "/" . $banner['resim_url'];
        if (file_exists($file_path)) {
            unlink($file_path);
        }
    }
    
    $sql = "DELETE FROM b2b_banners WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $success_message = "Banner başarıyla silindi!";
}

// Banner'ları getir
$sql = "SELECT * FROM b2b_banners ORDER BY sira, id";
$result = $conn->query($sql);
$banners = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $banners[] = $row;
    }
}

include 'includes/header.php';
?>

<style>
.banner-preview {
    position: relative;
    height: 200px;
    background-size: cover;
    background-position: center;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.banner-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 1rem;
}

.banner-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.banner-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.banner-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.banner-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-light);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark);
    margin: 0;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success);
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: var(--warning);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-light);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(55, 65, 81, 0.1);
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    padding: 2rem;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-light);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark);
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: var(--gray-light);
    color: var(--dark);
}

.row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.col-6 {
    flex: 1;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check input {
    width: auto;
}

.image-preview {
    max-width: 200px;
    max-height: 150px;
    object-fit: cover;
    border-radius: 0.5rem;
    margin-top: 0.5rem;
}

/* Sürükle bırak stili */
.sortable-item {
    cursor: move;
}

.sortable-placeholder {
    background: rgba(55, 65, 81, 0.1);
    border: 2px dashed var(--gray);
    margin: 0.5rem 0;
    height: 60px;
    border-radius: 0.5rem;
}
</style>

<div class="container">
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-images"></i>
            Banner Yönetimi
        </h1>
        <button class="btn btn-primary" onclick="openModal('add')">
            <i class="fas fa-plus"></i>
            Yeni Banner Ekle
        </button>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <div class="banner-grid">
        <?php foreach ($banners as $banner): ?>
            <div class="banner-card sortable-item" data-id="<?php echo $banner['id']; ?>">
                <div class="banner-preview" style="background-image: url('<?php echo $banner['resim_url']; ?>');">
                    <div class="banner-overlay">
                        <h3><?php echo htmlspecialchars($banner['baslik']); ?></h3>
                        <p><?php echo htmlspecialchars($banner['aciklama']); ?></p>
                    </div>
                </div>
                <div style="padding: 1rem;">
                    <div style="margin-bottom: 1rem;">
                        <strong>Sıra:</strong> <?php echo $banner['sira']; ?> | 
                        <strong>Durum:</strong> 
                        <span style="color: <?php echo $banner['aktif'] ? 'var(--success)' : 'var(--danger)'; ?>">
                                <?php echo $banner['aktif'] ? 'Aktif' : 'Pasif'; ?>
                            </span>
                    </div>
                    <div class="banner-actions">
                        <button class="btn btn-warning btn-sm" onclick="editBanner(<?php echo $banner['id']; ?>)">
                            <i class="fas fa-edit"></i> Düzenle
                        </button>
                        <a href="?delete=<?php echo $banner['id']; ?>" class="btn btn-danger btn-sm" 
                           onclick="return confirm('Bu banner\'ı silmek istediğinizden emin misiniz?')">
                            <i class="fas fa-trash"></i> Sil
                        </a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <?php if (empty($banners)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--gray);">
            <i class="fas fa-images" style="font-size: 3rem; margin-bottom: 1rem;"></i>
            <h3>Henüz banner eklenmemiş</h3>
            <p>İlk banner'ınızı eklemek için yukarıdaki "Yeni Banner Ekle" butonunu kullanın.</p>
        </div>
    <?php endif; ?>
</div>

<!-- Banner Ekleme/Düzenleme Modal -->
<div id="bannerModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" id="modalTitle">Yeni Banner Ekle</h2>
            <button class="close-modal" onclick="closeModal()">&times;</button>
        </div>
        <form id="bannerForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" id="formAction" value="add">
            <input type="hidden" name="id" id="bannerId">
            
            <div class="form-group">
                <label class="form-label" for="baslik">Banner Başlığı *</label>
                <input type="text" class="form-control" id="baslik" name="baslik" required>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="aciklama">Açıklama</label>
                <textarea class="form-control" id="aciklama" name="aciklama" rows="3"></textarea>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="banner_image">Banner Resmi</label>
                <input type="file" class="form-control" id="banner_image" name="banner_image" accept="image/*" onchange="previewImage(this)">
                <small style="color: var(--gray); font-size: 0.875rem;">
                    Önerilen boyut: 600x400px (JPG, PNG, GIF, WebP - Max 5MB)
                </small>
                <img id="imagePreview" class="image-preview" style="display: none;">
            </div>
            
            <div class="form-group">
                <label class="form-label" for="resim_url">Veya Resim URL'si</label>
                <input type="url" class="form-control" id="resim_url" name="resim_url" placeholder="https://...">
            </div>
            
            <div class="row">
                <div class="col-6">
            <div class="form-group">
                        <label class="form-label" for="link_url">Link URL</label>
                        <input type="text" class="form-control" id="link_url" name="link_url" placeholder="urunler.php?indirim=1 veya https://...">
                        <small style="color: var(--gray); font-size: 0.875rem;">
                            Örn: urunler.php?indirim=1 veya https://example.com
                        </small>
            </div>
            </div>
                <div class="col-6">
            <div class="form-group">
                        <label class="form-label" for="button_text">Buton Metni</label>
                        <input type="text" class="form-control" id="button_text" name="button_text" placeholder="Detaylar">
            </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-6">
                    <div class="form-group">
                        <label class="form-label" for="sira">Sıra</label>
                        <input type="number" class="form-control" id="sira" name="sira" value="1" min="1">
    </div>
</div>
                <div class="col-6">
            <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <div class="form-check">
                            <input type="checkbox" id="aktif" name="aktif" checked>
                            <label for="aktif">Aktif</label>
            </div>
            </div>
                </div>
            </div>
            
            <div style="text-align: right; margin-top: 2rem;">
                <button type="button" class="btn" style="background: var(--gray-light); color: var(--dark);" onclick="closeModal()">
                    İptal
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Kaydet
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Modal fonksiyonları
function openModal(action, bannerId = null) {
    const modal = document.getElementById('bannerModal');
    const modalTitle = document.getElementById('modalTitle');
    const form = document.getElementById('bannerForm');
    const formAction = document.getElementById('formAction');
    
    if (action === 'add') {
        modalTitle.textContent = 'Yeni Banner Ekle';
        formAction.value = 'add';
        form.reset();
        document.getElementById('aktif').checked = true;
        document.getElementById('imagePreview').style.display = 'none';
    } else if (action === 'edit' && bannerId) {
        modalTitle.textContent = 'Banner Düzenle';
        formAction.value = 'edit';
        document.getElementById('bannerId').value = bannerId;
        // Banner verilerini yükle
        loadBannerData(bannerId);
    }
    
    modal.classList.add('active');
}

function closeModal() {
    const modal = document.getElementById('bannerModal');
    modal.classList.remove('active');
}

function editBanner(bannerId) {
    openModal('edit', bannerId);
}

function loadBannerData(bannerId) {
    <?php foreach ($banners as $banner): ?>
        if (<?php echo $banner['id']; ?> == bannerId) {
            document.getElementById('baslik').value = '<?php echo addslashes($banner['baslik']); ?>';
            document.getElementById('aciklama').value = '<?php echo addslashes($banner['aciklama']); ?>';
            document.getElementById('resim_url').value = '<?php echo addslashes($banner['resim_url']); ?>';
            document.getElementById('link_url').value = '<?php echo addslashes($banner['link_url']); ?>';
            document.getElementById('button_text').value = '<?php echo addslashes($banner['button_text']); ?>';
            document.getElementById('sira').value = <?php echo $banner['sira']; ?>;
            document.getElementById('aktif').checked = <?php echo $banner['aktif'] ? 'true' : 'false'; ?>;
            
            // Resim önizlemesi
            const preview = document.getElementById('imagePreview');
            if ('<?php echo $banner['resim_url']; ?>') {
                preview.src = '<?php echo $banner['resim_url']; ?>';
                preview.style.display = 'block';
            }
        }
    <?php endforeach; ?>
}

function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        }
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

// Modal dışına tıklama ile kapatma
document.getElementById('bannerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// ESC tuşu ile kapatma
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});
</script>

<?php include 'includes/footer.php'; ?> 