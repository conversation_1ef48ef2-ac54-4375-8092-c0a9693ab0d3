<?php
// Admin sistemi test dosyası
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

echo "<h1>B2B Admin Test Sayfası</h1>";

// Debug: Mevcut dizini göster
echo "<p><strong>Mevcut dizin:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Config.php aranıyor:</strong> " . realpath(__DIR__ . '/../config.php') . "</p>";

// Config dosyasını dahil et
$config_path = __DIR__ . '/../config.php';
if (file_exists($config_path)) {
    echo "<p style='color:green'>✅ Config dosyası bulundu: $config_path</p>";
    try {
        include $config_path;
        echo "<p style='color:green'>✅ Config dosyası başarıyla yüklendi</p>";
    } catch (Exception $e) {
        echo "<p style='color:red'>❌ Config dosyası yükleme hatası: " . $e->getMessage() . "</p>";
        exit;
    }
} else {
    echo "<p style='color:red'>❌ Config dosyası bulunamadı: $config_path</p>";
    exit;
}

// Veritabanı bağlantısını test et
try {
    $test_query = $conn->query("SELECT 1");
    echo "<p style='color:green'>✅ Veritabanı bağlantısı başarılı</p>";
} catch (Exception $e) {
    echo "<p style='color:red'>❌ Veritabanı bağlantı hatası: " . $e->getMessage() . "</p>";
}

// Admin tablosunun varlığını kontrol et
$admin_table_exists = $conn->query("SHOW TABLES LIKE 'b2b_admin'")->num_rows > 0;

if ($admin_table_exists) {
    echo "<p style='color:green'>✅ b2b_admin tablosu mevcut</p>";
    
    // Admin kullanıcı sayısını kontrol et
    $result = $conn->query("SELECT COUNT(*) as count FROM b2b_admin");
    $row = $result->fetch_assoc();
    $admin_count = $row['count'];
    
    if ($admin_count > 0) {
        echo "<p style='color:green'>✅ {$admin_count} admin kullanıcısı mevcut</p>";
        
        // Admin kullanıcıları listele
        $result = $conn->query("SELECT id, kullanici_adi, ad_soyad, aktif FROM b2b_admin");
        echo "<table border='1' style='border-collapse:collapse; margin:10px 0;'>";
        echo "<tr><th>ID</th><th>Kullanıcı Adı</th><th>Ad Soyad</th><th>Aktif</th></tr>";
        while ($admin = $result->fetch_assoc()) {
            $aktif_text = $admin['aktif'] ? 'Evet' : 'Hayır';
            $color = $admin['aktif'] ? 'green' : 'red';
            echo "<tr><td>{$admin['id']}</td><td>{$admin['kullanici_adi']}</td><td>{$admin['ad_soyad']}</td><td style='color:{$color}'>{$aktif_text}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color:orange'>⚠️ Admin kullanıcısı bulunamadı</p>";
        echo "<p>Yeni admin oluşturmak için:</p>";
        echo "<form method='post' style='border:1px solid #ccc; padding:10px; margin:10px 0;'>";
        echo "<input type='hidden' name='create_admin' value='1'>";
        echo "<p>Kullanıcı Adı: <input type='text' name='kullanici_adi' value='admin' required></p>";
        echo "<p>Şifre: <input type='text' name='sifre' value='123456' required></p>";
        echo "<p>Ad Soyad: <input type='text' name='ad_soyad' value='Admin Kullanıcı' required></p>";
        echo "<p><button type='submit'>Admin Oluştur</button></p>";
        echo "</form>";
    }
} else {
    echo "<p style='color:red'>❌ b2b_admin tablosu mevcut değil</p>";
    echo "<p>Admin tablosunu oluşturmak için:</p>";
    echo "<form method='post' style='border:1px solid #ccc; padding:10px; margin:10px 0;'>";
    echo "<input type='hidden' name='create_table' value='1'>";
    echo "<button type='submit'>b2b_admin Tablosunu Oluştur</button>";
    echo "</form>";
}

// POST işlemleri
if ($_POST) {
    if (isset($_POST['create_table'])) {
        $sql = "CREATE TABLE `b2b_admin` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `kullanici_adi` varchar(50) NOT NULL,
            `sifre` varchar(255) NOT NULL,
            `ad_soyad` varchar(100) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `yetki_seviyesi` tinyint(1) DEFAULT 1,
            `aktif` tinyint(1) DEFAULT 1,
            `son_giris` datetime DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `kullanici_adi` (`kullanici_adi`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        if ($conn->query($sql)) {
            echo "<p style='color:green'>✅ b2b_admin tablosu oluşturuldu! Sayfayı yenileyin.</p>";
        } else {
            echo "<p style='color:red'>❌ Tablo oluşturma hatası: " . $conn->error . "</p>";
        }
    }
    
    if (isset($_POST['create_admin'])) {
        $kullanici_adi = $_POST['kullanici_adi'];
        $sifre = password_hash($_POST['sifre'], PASSWORD_DEFAULT);
        $ad_soyad = $_POST['ad_soyad'];
        
        $stmt = $conn->prepare("INSERT INTO b2b_admin (kullanici_adi, sifre, ad_soyad) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $kullanici_adi, $sifre, $ad_soyad);
        
        if ($stmt->execute()) {
            echo "<p style='color:green'>✅ Admin kullanıcısı oluşturuldu! Sayfayı yenileyin.</p>";
        } else {
            echo "<p style='color:red'>❌ Admin oluşturma hatası: " . $conn->error . "</p>";
        }
    }
}

// Session bilgilerini göster
echo "<h3>Session Bilgileri:</h3>";
if (isset($_SESSION) && !empty($_SESSION)) {
    echo "<pre style='background:#f5f5f5; padding:10px; border:1px solid #ccc;'>";
    print_r($_SESSION);
    echo "</pre>";
} else {
    echo "<p>Session bilgisi yok veya boş</p>";
}

echo "<p><a href='index.php'>Admin Giriş Sayfasına Dön</a></p>";
echo "<p><a href='dashboard.php'>Dashboard'a Git</a></p>";
?> 