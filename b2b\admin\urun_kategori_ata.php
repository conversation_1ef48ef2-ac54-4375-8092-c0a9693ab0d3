<?php
session_start();
include '../config.php';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// POST kontrolü
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['urun_id'], $_POST['kategori_id'])) {
    $urun_id = (int)$_POST['urun_id'];
    $kategori_id = (int)$_POST['kategori_id'];
    
    // Kategori bilgisini kontrol et
    $kategori_adi = '';
    $sql = "SELECT kategori_adi FROM b2b_kategoriler WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $kategori_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $row = $result->fetch_assoc()) {
        // Ürünün kategori_id alanını güncelle
        $sql = "UPDATE urunler SET kategori_id = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ii', $kategori_id, $urun_id);
        
        if ($stmt->execute()) {
            header("Location: urunler.php?mesaj=basarili&islem=kategori_atandi");
            exit;
        } else {
            header("Location: urunler.php?mesaj=hata&islem=kategori_atanamadi");
            exit;
        }
    } else {
        header("Location: urunler.php?mesaj=hata&islem=kategori_bulunamadi");
        exit;
    }
} else {
    header("Location: urunler.php");
    exit;
} 