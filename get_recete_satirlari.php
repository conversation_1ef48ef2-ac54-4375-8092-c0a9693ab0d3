<?php
// 📋 REÇETE SATIRLARINI GETIR
// Bu dosya belirli bir reçete ID'sine ait satırları döndürür

header('Content-Type: application/json; charset=utf-8');
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'includes/db.php';

function debugLog($message, $data = null) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message";
    if ($data !== null) {
        $logMessage .= " | Data: " . print_r($data, true);
    }
    file_put_contents('debug_get_recete_satirlari.log', $logMessage . "\n", FILE_APPEND);
}

$recete_id = intval($_GET['recete_id'] ?? 0);

debugLog("🔍 Reçete satırları isteniyor", ['recete_id' => $recete_id]);

if ($recete_id <= 0) {
    debugLog("❌ Geçersiz reçete ID", $recete_id);
    echo json_encode(['success' => false, 'message' => 'Geçersiz reçete ID']);
    exit;
}

try {
    // Reçete satırlarını getir
    $query = "
        SELECT 
            rs.*,
            CASE 
                WHEN rs.kategori = 'sunta' THEN (
                    SELECT CONCAT(COALESCE(s.malz_kodu, ''), ' - ', COALESCE(s.malzeme_adi, ''))
                    FROM uretim_malzemeler_sunta s 
                    WHERE s.id = rs.malzeme_id
                )
                WHEN rs.kategori = 'pvc' THEN (
                    SELECT CONCAT(COALESCE(p.malz_kodu, ''), ' - ', COALESCE(p.malzeme_adi, ''))
                    FROM uretim_malzemeler_pvc p 
                    WHERE p.id = rs.malzeme_id
                )
                WHEN rs.kategori = 'hirdavat' THEN (
                    SELECT CONCAT(COALESCE(h.malz_kodu, ''), ' - ', COALESCE(h.malzeme_adi, ''))
                    FROM uretim_malzemeler_hirdavat h 
                    WHERE h.id = rs.malzeme_id
                )
                WHEN rs.kategori = 'koli' THEN (
                    SELECT CONCAT(COALESCE(k.malz_kodu, ''), ' - ', COALESCE(k.malzeme_adi, ''))
                    FROM uretim_malzemeler_koli k 
                    WHERE k.id = rs.malzeme_id
                )
                ELSE 'Bilinmeyen Malzeme'
            END as malzeme_adi_full
        FROM recete_satirlari rs
        WHERE rs.recete_id = ?
        ORDER BY rs.kategori, rs.id
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $recete_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $satirlar = [];
    while ($row = $result->fetch_assoc()) {
        $satirlar[] = $row;
    }
    
    debugLog("✅ Reçete satırları bulundu", ['count' => count($satirlar)]);
    
    if (count($satirlar) > 0) {
        // Her kategori için ayrı debug
        $kategoriler = [];
        foreach ($satirlar as $satir) {
            $kategoriler[$satir['kategori']][] = $satir;
        }
        
        foreach ($kategoriler as $kategori => $kategori_satirlari) {
            debugLog("📦 {$kategori} kategorisi", ['count' => count($kategori_satirlari)]);
        }
        
        echo json_encode([
            'success' => true, 
            'satirlar' => $satirlar,
            'count' => count($satirlar),
            'kategoriler' => array_keys($kategoriler)
        ]);
    } else {
        debugLog("⚠️ Reçete satırları boş", $recete_id);
        echo json_encode([
            'success' => true, 
            'satirlar' => [],
            'count' => 0,
            'message' => 'Bu reçete için henüz satır eklenmemiş'
        ]);
    }
    
} catch (Exception $e) {
    debugLog("❌ Veritabanı hatası", [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'recete_id' => $recete_id
    ]);
    
    echo json_encode([
        'success' => false, 
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ]);
}
?>
