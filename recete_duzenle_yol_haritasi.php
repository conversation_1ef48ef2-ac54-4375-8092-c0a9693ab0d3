<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>🗺️ Reçete Düzenle Yol Haritası</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .card { box-shadow: 0 10px 30px rgba(0,0,0,0.3); border: none; }
        .step-card { transition: transform 0.3s; }
        .step-card:hover { transform: translateY(-5px); }
        .problem { background: #ff6b6b; color: white; }
        .solution { background: #4ecdc4; color: white; }
        .status { background: #45b7d1; color: white; }
        .test { background: #96ceb4; color: white; }
    </style>
</head>
<body>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5">
                <h1 class="text-white mb-3">🗺️ Reçete Düzenle Sorun Çözüm Yol Haritası</h1>
                <p class="text-white-50">Sistematik sorun tespit ve çözüm rehberi</p>
            </div>
        </div>
    </div>

    <!-- SORUN TESPİTİ -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card problem">
                <div class="card-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> 🔍 TESPİT EDİLEN SORUNLAR</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>1. Ürün Gelmiyor Sorunu</h5>
                            <ul>
                                <li>URL'de ürün ID var ama dropdown'da seçilmiyor</li>
                                <li>JavaScript ürün yükleme fonksiyonu çalışmıyor</li>
                                <li>PHP'den gelen current_urun_id kullanılmıyor</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>2. Malzeme Değişince Satır Dolmuyor</h5>
                            <ul>
                                <li>Event listener'lar eksik veya yanlış</li>
                                <li>Malzeme API'sinden gelen data attribute'ları yok</li>
                                <li>4 kategori için de aynı sorun var</li>
                            </ul>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h5>3. Hesaplamalar Çalışmıyor</h5>
                            <ul>
                                <li>Input değişikliklerinde hesaplama tetiklenmiyor</li>
                                <li>Sunta, PVC, Hırdavat, Koli hesaplamaları bozuk</li>
                                <li>Maliyet özeti güncellenmiyor</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>4. Koli Birim Fiyatı 0 Gelme</h5>
                            <ul>
                                <li>Malzeme tablosundaki fiyat sütunları yanlış</li>
                                <li>birim_fiyat_m2 ve birim_fiyat_adet boş</li>
                                <li>Toplam fiyat her zaman 0</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ÇÖZÜM STRATEJİSİ -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card solution">
                <div class="card-header">
                    <h3><i class="fas fa-tools"></i> 🛠️ ÇÖZÜM STRATEJİSİ</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card step-card mb-3">
                                <div class="card-body text-center">
                                    <h5>1. Debug Sistemi</h5>
                                    <p>Ekranda renkli debug mesajları</p>
                                    <span class="badge bg-success">✅ Tamamlandı</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card step-card mb-3">
                                <div class="card-body text-center">
                                    <h5>2. API Test Sayfası</h5>
                                    <p>Malzeme ve ürün API'lerini test et</p>
                                    <span class="badge bg-success">✅ Tamamlandı</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card step-card mb-3">
                                <div class="card-body text-center">
                                    <h5>3. Yeni JavaScript</h5>
                                    <p>Temiz, sorun çözücü JS dosyası</p>
                                    <span class="badge bg-success">✅ Tamamlandı</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card step-card mb-3">
                                <div class="card-body text-center">
                                    <h5>4. Entegrasyon</h5>
                                    <p>Tüm sistemleri birleştir</p>
                                    <span class="badge bg-warning">🔄 Devam Ediyor</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- OLUŞTURULAN DOSYALAR -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card status">
                <div class="card-header">
                    <h3><i class="fas fa-file-code"></i> 📁 OLUŞTURULAN DOSYALAR</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h5>Debug Dosyaları</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <a href="debug_recete_duzenle.php?id=1" target="_blank">
                                        🔍 debug_recete_duzenle.php
                                    </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="debug_malzeme_api.php" target="_blank">
                                        🔧 debug_malzeme_api.php
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h5>JavaScript Dosyaları</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    📄 recete_duzenle_fixed.js
                                    <span class="badge bg-success">Yeni</span>
                                </li>
                                <li class="list-group-item">
                                    📄 get_recete_satirlari.php
                                    <span class="badge bg-success">Yeni</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h5>Güncellenmiş Dosyalar</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    📝 recete_duzenle.php
                                    <span class="badge bg-warning">Güncellendi</span>
                                </li>
                                <li class="list-group-item">
                                    📝 malzeme_detay.php
                                    <span class="badge bg-warning">Güncellendi</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- TEST ADIMLARI -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card test">
                <div class="card-header">
                    <h3><i class="fas fa-vial"></i> 🧪 TEST ADIMLARI</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>1. Debug Testleri</h5>
                            <ol>
                                <li>
                                    <a href="debug_recete_duzenle.php?id=1" target="_blank" class="btn btn-sm btn-outline-light">
                                        Reçete Debug Sayfasını Aç
                                    </a>
                                </li>
                                <li>
                                    <a href="debug_malzeme_api.php" target="_blank" class="btn btn-sm btn-outline-light">
                                        Malzeme API Test Sayfasını Aç
                                    </a>
                                </li>
                                <li>Tüm API'lerin çalıştığını kontrol et</li>
                                <li>Veritabanı tablolarını kontrol et</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>2. Fonksiyon Testleri</h5>
                            <ol>
                                <li>
                                    <a href="recete_duzenle.php?id=1" target="_blank" class="btn btn-sm btn-outline-light">
                                        Reçete Düzenle Sayfasını Aç
                                    </a>
                                </li>
                                <li>Sağ üstte debug mesajlarını kontrol et</li>
                                <li>Test butonlarını kullan</li>
                                <li>Malzeme seçimlerini test et</li>
                                <li>Hesaplamaları test et</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SONRAKI ADIMLAR -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h3><i class="fas fa-road"></i> 🚀 SONRAKİ ADIMLAR</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h5>Kısa Vadeli (Bugün)</h5>
                            <ul>
                                <li>Debug sayfalarını test et</li>
                                <li>Malzeme API'lerini kontrol et</li>
                                <li>Ürün yükleme sorununu çöz</li>
                                <li>Temel hesaplamaları düzelt</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h5>Orta Vadeli (Bu Hafta)</h5>
                            <ul>
                                <li>Tüm kategorilerde malzeme seçimi</li>
                                <li>Otomatik hesaplama sistemleri</li>
                                <li>Maliyet özeti güncellemeleri</li>
                                <li>Satır ekleme/silme işlemleri</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h5>Uzun Vadeli (Gelecek)</h5>
                            <ul>
                                <li>Performans optimizasyonu</li>
                                <li>Kullanıcı deneyimi iyileştirmeleri</li>
                                <li>Hata yönetimi sistemi</li>
                                <li>Otomatik test sistemi</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- HIZLI ERİŞİM -->
    <div class="row">
        <div class="col-12">
            <div class="card bg-dark text-white">
                <div class="card-header">
                    <h3><i class="fas fa-rocket"></i> ⚡ HIZLI ERİŞİM</h3>
                </div>
                <div class="card-body text-center">
                    <a href="debug_recete_duzenle.php?id=1" class="btn btn-danger btn-lg me-3 mb-2">
                        <i class="fas fa-bug"></i> Debug Sayfası
                    </a>
                    <a href="debug_malzeme_api.php" class="btn btn-warning btn-lg me-3 mb-2">
                        <i class="fas fa-cog"></i> API Test
                    </a>
                    <a href="recete_duzenle.php?id=1" class="btn btn-success btn-lg me-3 mb-2">
                        <i class="fas fa-edit"></i> Reçete Düzenle
                    </a>
                    <a href="recete_ekle.php" class="btn btn-info btn-lg mb-2">
                        <i class="fas fa-plus"></i> Reçete Ekle
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
