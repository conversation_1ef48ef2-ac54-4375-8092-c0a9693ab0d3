<?php
session_start();
include '../config.php';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Ajax isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // JSON verisini al
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    
    if (isset($data['urun_idler']) && isset($data['kategori_id']) && !empty($data['urun_idler']) && !empty($data['kategori_id'])) {
        $kategori_id = (int)$data['kategori_id'];
        $urun_idler = array_map('intval', $data['urun_idler']); // ID'leri integer'a çevir
        
        // Kategori bilgisini al
        $sql = "SELECT kategori_adi FROM b2b_kategoriler WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $kategori_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $row = $result->fetch_assoc()) {
            // Başarı/hata sayacı
            $basarili = 0;
            $hatali = 0;
            
            // Her bir ürün için kategori ID'sini güncelle
            $sql = "UPDATE urunler SET kategori_id = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            
            foreach ($urun_idler as $urun_id) {
                $stmt->bind_param('ii', $kategori_id, $urun_id);
                if ($stmt->execute()) {
                    $basarili++;
                } else {
                    $hatali++;
                }
            }
            
            // Başarılı yanıt
            echo json_encode([
                'success' => true,
                'message' => "$basarili ürün başarıyla kategoriye atandı. $hatali ürün atanamadı.",
                'basarili' => $basarili,
                'hatali' => $hatali
            ]);
            exit;
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Kategori bulunamadı.'
            ]);
            exit;
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Geçersiz veri gönderildi.'
        ]);
        exit;
    }
} else {
    header("Location: urunler.php");
    exit;
} 