<?php
// Veritabanı bağlantısı
include '../config.php';

// Tablo kontrol et
$tableExists = $conn->query("SHOW TABLES LIKE 'urun_kategorileri'")->num_rows > 0;

if (!$tableExists) {
    // urun_kategorileri tablosunu oluştur
    $sql = "CREATE TABLE `urun_kategorileri` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `urun_id` int(11) NOT NULL,
        `kategori_id` int(11) NOT NULL,
        `kategori_adi` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
        `tarih` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `urun_id` (`urun_id`),
        KEY `kategori_id` (`kategori_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;";

    if ($conn->query($sql)) {
        echo "urun_kategorileri tablosu başarıyla oluşturuldu.";
    } else {
        echo "Tablo oluşturulurken hata oluştu: " . $conn->error;
    }
} else {
    echo "urun_kategorileri tablosu zaten mevcut.";
}
?> 