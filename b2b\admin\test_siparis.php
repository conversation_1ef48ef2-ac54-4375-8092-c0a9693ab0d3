<?php
echo "<h1>Sipariş Test Sayfası</h1>";

// Config dosyası yolunu test et
$config_paths = ['../../config.php', '../config.php', '../../b2b/config.php', '../../../config.php', '../../../../config.php'];

echo "<h2>1. Config Test:</h2>";
foreach ($config_paths as $path) {
    echo "Test: $path - ";
    if (file_exists($path)) {
        echo "<span style='color:green'>✓ Dosya var</span><br>";
        include_once $path;
        if (isset($conn)) {
            echo "Database bağlantısı: <span style='color:green'>✓ OK</span><br>";
            break;
        } else {
            echo "Database bağlantısı: <span style='color:red'>✗ Yok</span><br>";
        }
    } else {
        echo "<span style='color:red'>✗ Dosya yok</span><br>";
    }
}

if (!isset($conn)) {
    die("<h3 style='color:red'>Config dosyası bulunamadı!</h3>");
}

// Sipariş test
echo "<h2>2. Sipariş Test:</h2>";
$siparis_id = isset($_GET['id']) ? intval($_GET['id']) : 7;
echo "Test Sipariş ID: $siparis_id<br>";

// Sipariş bilgilerini çek
$siparis_sql = "SELECT s.*, c.firm_name 
                FROM b2b_siparisler s
                LEFT JOIN cari_firmalar c ON s.cari_id = c.id
                WHERE s.id = ?";
                
echo "SQL: $siparis_sql<br>";

$siparis_stmt = $conn->prepare($siparis_sql);
if (!$siparis_stmt) {
    echo "<span style='color:red'>SQL Prepare Hatası: " . $conn->error . "</span><br>";
    die();
}

$siparis_stmt->bind_param('i', $siparis_id);
$result = $siparis_stmt->execute();

if (!$result) {
    echo "<span style='color:red'>SQL Execute Hatası: " . $siparis_stmt->error . "</span><br>";
    die();
}

$siparis_result = $siparis_stmt->get_result();
echo "Bulunan sipariş sayısı: " . $siparis_result->num_rows . "<br>";

if ($siparis_result->num_rows > 0) {
    $siparis = $siparis_result->fetch_assoc();
    echo "<h3 style='color:green'>✓ Sipariş Bulundu:</h3>";
    echo "<pre>";
    print_r($siparis);
    echo "</pre>";
} else {
    echo "<span style='color:red'>Sipariş bulunamadı!</span><br>";
    
    // Tüm siparişleri listele
    $all_sql = "SELECT id, cari_id, durum, created_at FROM b2b_siparisler LIMIT 10";
    $all_result = $conn->query($all_sql);
    echo "<h3>Mevcut Siparişler:</h3>";
    if ($all_result->num_rows > 0) {
        while ($row = $all_result->fetch_assoc()) {
            echo "ID: {$row['id']}, Müşteri: {$row['cari_id']}, Durum: {$row['durum']}, Tarih: {$row['created_at']}<br>";
        }
    } else {
        echo "Hiç sipariş bulunamadı!<br>";
    }
}

echo "<br><a href='?id=7'>ID=7 Test</a> | <a href='?id=1'>ID=1 Test</a> | <a href='siparis_detay.php?id=7'>Asıl Sayfa</a>";
?> 