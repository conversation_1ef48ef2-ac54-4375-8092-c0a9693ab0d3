<?php
session_start();
include '../config.php';

echo "<h1>Müşteri Tabloları Düzeltmeleri</h1>";

// 1. b2b_musteri_ayarlari tablosu var mı kontrol et
echo "<h3>1. b2b_musteri_ayarlari tablosu kontrol ediliyor...</h3>";
$check = $conn->query("SHOW TABLES LIKE 'b2b_musteri_ayarlari'");
if ($check->num_rows == 0) {
    echo "b2b_musteri_ayarlari tablosu bulunamadı, oluşturuluyor...<br>";
    $sql = "CREATE TABLE b2b_musteri_ayarlari (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        cari_id INT(11) NOT NULL,
        aktif TINYINT(1) DEFAULT 1,
        iskonto_orani DECIMAL(5,2) DEFAULT 0.00,
        kredi_limiti DECIMAL(15,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (cari_id)
    )";
    if ($conn->query($sql)) {
        echo "✓ b2b_musteri_ayarlari tablosu oluşturuldu<br>";
    } else {
        echo "✗ Hata: " . $conn->error . "<br>";
    }
} else {
    echo "✓ b2b_musteri_ayarlari tablosu zaten mevcut<br>";
}

// 2. b2b_cari_ayarlar tablosu var mı kontrol et
echo "<h3>2. b2b_cari_ayarlar tablosu kontrol ediliyor...</h3>";
$check = $conn->query("SHOW TABLES LIKE 'b2b_cari_ayarlar'");
if ($check->num_rows == 0) {
    echo "b2b_cari_ayarlar tablosu bulunamadı, oluşturuluyor...<br>";
    $sql = "CREATE TABLE b2b_cari_ayarlar (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        cari_id INT(11) NOT NULL,
        iskonto_orani DECIMAL(5,2) DEFAULT 0.00,
        ek_indirim_orani DECIMAL(5,2) DEFAULT 0.00,
        kredi_limiti DECIMAL(15,2) DEFAULT 0.00,
        odeme_vadesi INT(11) DEFAULT 30,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (cari_id)
    )";
    if ($conn->query($sql)) {
        echo "✓ b2b_cari_ayarlar tablosu oluşturuldu<br>";
    } else {
        echo "✗ Hata: " . $conn->error . "<br>";
    }
} else {
    echo "✓ b2b_cari_ayarlar tablosu zaten mevcut<br>";
}

// 3. musteriler.php için tek tablo kullanmaya çevir
echo "<h3>3. Müşteri verilerini b2b_cari_ayarlar tablosuna kopyalıyor...</h3>";

// b2b_musteri_ayarlari'ndaki verileri b2b_cari_ayarlar'a kopyala
$copy_sql = "INSERT IGNORE INTO b2b_cari_ayarlar (cari_id, iskonto_orani, kredi_limiti) 
             SELECT cari_id, iskonto_orani, kredi_limiti 
             FROM b2b_musteri_ayarlari";

if ($conn->query($copy_sql)) {
    $affected = $conn->affected_rows;
    echo "✓ $affected müşteri ayarı kopyalandı<br>";
} else {
    echo "✗ Kopyalama hatası: " . $conn->error . "<br>";
}

// 4. Test dataları
echo "<h3>4. Test verilerini kontrol ediliyor...</h3>";

$musteri_count = $conn->query("SELECT COUNT(*) as sayi FROM b2b_musteri_ayarlari")->fetch_assoc();
echo "b2b_musteri_ayarlari kayıt sayısı: " . $musteri_count['sayi'] . "<br>";

$cari_count = $conn->query("SELECT COUNT(*) as sayi FROM b2b_cari_ayarlar")->fetch_assoc();
echo "b2b_cari_ayarlar kayıt sayısı: " . $cari_count['sayi'] . "<br>";

$firma_count = $conn->query("SELECT COUNT(*) as sayi FROM cari_firmalar WHERE b2b_aktif = 1")->fetch_assoc();
echo "Aktif B2B firması sayısı: " . $firma_count['sayi'] . "<br>";

echo "<br><h3>✓ Tüm düzeltmeler tamamlandı!</h3>";
echo "<a href='musteriler.php'>← Müşteriler sayfasına dön</a>";
?> 