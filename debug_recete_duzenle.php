<?php
// 🔍 RECETE DÜZENLE DEBUG SAYFASI
// Bu sayfa recete düzenleme sorunlarını tespit eder ve çözer

header('Content-Type: text/html; charset=UTF-8');
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'includes/db.php';

$recete_id = intval($_GET['id'] ?? 0);

function debugOutput($title, $content, $type = 'info') {
    $colors = [
        'success' => '#28a745',
        'error' => '#dc3545', 
        'warning' => '#ffc107',
        'info' => '#17a2b8',
        'primary' => '#007bff'
    ];
    
    $color = $colors[$type] ?? $colors['info'];
    
    echo "<div style='background: {$color}; color: white; padding: 10px; margin: 5px; border-radius: 5px; font-family: monospace;'>";
    echo "<strong>🔍 {$title}</strong><br>";
    if (is_array($content) || is_object($content)) {
        echo "<pre style='color: white; margin: 5px 0;'>" . print_r($content, true) . "</pre>";
    } else {
        echo $content;
    }
    echo "</div>";
}

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>🔍 Reçete Düzenle Debug</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Courier New', monospace; }
        .debug-section { margin: 20px 0; }
        .table-debug { font-size: 12px; }
    </style>
</head>
<body>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">🔍 Reçete Düzenle Debug Sistemi</h1>
            
            <?php if ($recete_id <= 0): ?>
                <div class="alert alert-danger">
                    <h4>❌ HATA: Geçersiz Reçete ID</h4>
                    <p>URL'de geçerli bir reçete ID'si belirtiniz. Örnek: debug_recete_duzenle.php?id=1</p>
                </div>
            <?php else: ?>
                
                <!-- 1. RECETE BİLGİLERİ KONTROLÜ -->
                <div class="debug-section">
                    <?php
                    debugOutput("1. REÇETE BİLGİLERİ KONTROLÜ", "Reçete ID: {$recete_id}", 'primary');
                    
                    try {
                        $recete_query = "SELECT * FROM receteler WHERE id = ?";
                        $stmt = $conn->prepare($recete_query);
                        $stmt->bind_param('i', $recete_id);
                        $stmt->execute();
                        $recete = $stmt->get_result()->fetch_assoc();
                        
                        if ($recete) {
                            debugOutput("✅ Reçete Bulundu", $recete, 'success');
                        } else {
                            debugOutput("❌ Reçete Bulunamadı", "ID {$recete_id} için reçete yok!", 'error');
                        }
                    } catch (Exception $e) {
                        debugOutput("❌ Reçete Sorgu Hatası", $e->getMessage(), 'error');
                    }
                    ?>
                </div>

                <!-- 2. ÜRÜN BİLGİLERİ KONTROLÜ -->
                <div class="debug-section">
                    <?php
                    debugOutput("2. ÜRÜN BİLGİLERİ KONTROLÜ", "", 'primary');
                    
                    if (isset($recete['urun_id'])) {
                        try {
                            $urun_query = "SELECT * FROM urunler WHERE id = ?";
                            $stmt = $conn->prepare($urun_query);
                            $stmt->bind_param('i', $recete['urun_id']);
                            $stmt->execute();
                            $urun = $stmt->get_result()->fetch_assoc();
                            
                            if ($urun) {
                                debugOutput("✅ Ürün Bulundu", $urun, 'success');
                            } else {
                                debugOutput("❌ Ürün Bulunamadı", "Ürün ID: {$recete['urun_id']}", 'error');
                            }
                        } catch (Exception $e) {
                            debugOutput("❌ Ürün Sorgu Hatası", $e->getMessage(), 'error');
                        }
                    }
                    ?>
                </div>

                <!-- 3. REÇETE SATIRLARI KONTROLÜ -->
                <div class="debug-section">
                    <?php
                    debugOutput("3. REÇETE SATIRLARI KONTROLÜ", "", 'primary');
                    
                    try {
                        $satirlar_query = "SELECT * FROM recete_satirlari WHERE recete_id = ? ORDER BY kategori, id";
                        $stmt = $conn->prepare($satirlar_query);
                        $stmt->bind_param('i', $recete_id);
                        $stmt->execute();
                        $satirlar = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
                        
                        if ($satirlar) {
                            debugOutput("✅ Reçete Satırları Bulundu", count($satirlar) . " satır", 'success');
                            
                            // Kategorilere göre grupla
                            $kategoriler = [];
                            foreach ($satirlar as $satir) {
                                $kategoriler[$satir['kategori']][] = $satir;
                            }
                            
                            foreach ($kategoriler as $kategori => $satir_listesi) {
                                debugOutput("📦 {$kategori} Kategorisi", count($satir_listesi) . " satır", 'info');
                                echo "<div class='table-responsive'>";
                                echo "<table class='table table-sm table-debug'>";
                                echo "<thead><tr>";
                                foreach (array_keys($satir_listesi[0]) as $kolon) {
                                    echo "<th>{$kolon}</th>";
                                }
                                echo "</tr></thead><tbody>";
                                foreach ($satir_listesi as $satir) {
                                    echo "<tr>";
                                    foreach ($satir as $deger) {
                                        echo "<td>" . htmlspecialchars($deger ?? '') . "</td>";
                                    }
                                    echo "</tr>";
                                }
                                echo "</tbody></table></div>";
                            }
                        } else {
                            debugOutput("⚠️ Reçete Satırları Boş", "Bu reçete için hiç satır yok", 'warning');
                        }
                    } catch (Exception $e) {
                        debugOutput("❌ Reçete Satırları Sorgu Hatası", $e->getMessage(), 'error');
                    }
                    ?>
                </div>

                <!-- 4. MALZEME TABLOLARI KONTROLÜ -->
                <div class="debug-section">
                    <?php
                    debugOutput("4. MALZEME TABLOLARI KONTROLÜ", "", 'primary');
                    
                    $malzeme_tablolari = [
                        'uretim_malzemeler_sunta',
                        'uretim_malzemeler_pvc', 
                        'uretim_malzemeler_hirdavat',
                        'uretim_malzemeler_koli'
                    ];
                    
                    foreach ($malzeme_tablolari as $tablo) {
                        try {
                            $check_query = "SHOW TABLES LIKE '{$tablo}'";
                            $result = $conn->query($check_query);
                            
                            if ($result->num_rows > 0) {
                                // Tablo var, kayıt sayısını kontrol et
                                $count_query = "SELECT COUNT(*) as toplam FROM {$tablo}";
                                $count_result = $conn->query($count_query);
                                $count = $count_result->fetch_assoc()['toplam'];
                                
                                debugOutput("✅ {$tablo}", "{$count} kayıt", 'success');
                                
                                // Tablo yapısını göster
                                $structure_query = "DESCRIBE {$tablo}";
                                $structure_result = $conn->query($structure_query);
                                $columns = [];
                                while ($row = $structure_result->fetch_assoc()) {
                                    $columns[] = $row['Field'] . ' (' . $row['Type'] . ')';
                                }
                                debugOutput("📋 {$tablo} Sütunları", implode(', ', $columns), 'info');
                                
                            } else {
                                debugOutput("❌ {$tablo}", "Tablo bulunamadı!", 'error');
                            }
                        } catch (Exception $e) {
                            debugOutput("❌ {$tablo} Kontrol Hatası", $e->getMessage(), 'error');
                        }
                    }
                    ?>
                </div>

                <!-- 5. RECETE_SATIRLARI TABLO YAPISI -->
                <div class="debug-section">
                    <?php
                    debugOutput("5. RECETE_SATIRLARI TABLO YAPISI", "", 'primary');
                    
                    try {
                        $structure_query = "DESCRIBE recete_satirlari";
                        $result = $conn->query($structure_query);
                        
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr><th>Sütun</th><th>Tip</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
                        echo "<tbody>";
                        while ($row = $result->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td><strong>{$row['Field']}</strong></td>";
                            echo "<td>{$row['Type']}</td>";
                            echo "<td>{$row['Null']}</td>";
                            echo "<td>{$row['Key']}</td>";
                            echo "<td>{$row['Default']}</td>";
                            echo "<td>{$row['Extra']}</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table></div>";
                        
                    } catch (Exception $e) {
                        debugOutput("❌ Tablo Yapısı Hatası", $e->getMessage(), 'error');
                    }
                    ?>
                </div>

            <?php endif; ?>
            
            <!-- 6. ÇÖZÜM ÖNERİLERİ -->
            <div class="debug-section">
                <div class="alert alert-info">
                    <h4>🛠️ ÇÖZÜM ÖNERİLERİ</h4>
                    <ol>
                        <li><strong>Ürün Gelmiyor:</strong> JavaScript'te ürün yükleme fonksiyonunu kontrol edin</li>
                        <li><strong>Malzeme Değişince Satır Dolmuyor:</strong> Event listener'ları kontrol edin</li>
                        <li><strong>Hesaplamalar Çalışmıyor:</strong> JavaScript hesaplama fonksiyonlarını kontrol edin</li>
                        <li><strong>Koli Birim Fiyatı 0:</strong> Malzeme tablosundaki fiyat sütunlarını kontrol edin</li>
                    </ol>
                </div>
            </div>
            
            <!-- 7. HIZLI ÇÖZÜM BUTONLARI -->
            <div class="debug-section">
                <div class="card">
                    <div class="card-header">
                        <h5>🚀 Hızlı Çözüm Araçları</h5>
                    </div>
                    <div class="card-body">
                        <a href="recete_duzenle.php?id=<?= $recete_id ?>" class="btn btn-primary">
                            📝 Recete Düzenle Sayfasına Git
                        </a>
                        <a href="debug_malzeme_api.php" class="btn btn-info">
                            🔧 Malzeme API Debug
                        </a>
                        <a href="debug_javascript.php?id=<?= $recete_id ?>" class="btn btn-warning">
                            ⚡ JavaScript Debug
                        </a>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
