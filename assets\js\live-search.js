/**
 * <PERSON><PERSON><PERSON><PERSON> Zamanlı Ürün Arama
 * Bu script kullanıcı arama kutusuna yazarken AJAX ile sonuçları getirir
 */
$(document).ready(function() {
    // Arama kutusu değiştiğinde
    $('#search').on('input', function() {
        var searchText = $(this).val().trim();
        
        // En az 2 karakter girilmişse aramayı başlat
        if (searchText.length >= 2) {
            // AJAX ile arama yap
            $.ajax({
                url: 'ajax_urun_arama.php',
                type: 'GET',
                data: {
                    search: searchText,
                    status: $('#status').val()
                },
                dataType: 'json',
                beforeSend: function() {
                    // İsteği göndermeden önce yükleniyor göstergesi ekle
                    $('#searchResults').html('<tr><td colspan="7" class="text-center py-5"><i class="fas fa-spinner fa-spin me-2"></i> Aranıyor...</td></tr>');
                },
                success: function(response) {
                    // Tabloyu temizle
                    $('#searchResults').empty();
                    
                    if (response.length === 0) {
                        // Sonuç yoksa bilgi mesajı göster
                        $('#searchResults').html('<tr><td colspan="7" class="text-center py-5">Arama kriterlerine uygun sonuç bulunamadı.</td></tr>');
                    } else {
                        // Sonuçları tabloya ekle
                        $.each(response, function(index, item) {
                            var row = `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${item.stok_adi}</td>
                                    <td>${item.stok_kodu}</td>
                                    <td>${item.renk}</td>
                                    <td>${item.paket_sayisi}</td>
                                    <td>${item.stokta_paket > 0 ? '<span class="badge bg-success">Stokta</span>' : '<span class="badge bg-danger">Stokta Yok</span>'}</td>
                                    <td>${item.ekleme_tarihi}</td>
                                    <td>
                                        <a href="urun_detay.php?id=${item.id}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                    </td>
                                </tr>
                            `;
                            $('#searchResults').append(row);
                        });
                    }
                },
                error: function(xhr, status, error) {
                    // Hata durumunda bilgi mesajı göster
                    $('#searchResults').html('<tr><td colspan="7" class="text-center py-5 text-danger"><i class="fas fa-exclamation-circle me-2"></i> Arama sırasında bir hata oluştu.</td></tr>');
                    console.error('AJAX Hatası:', error);
                }
            });
        } else if (searchText.length === 0) {
            // Arama kutusu boşsa tüm ürünleri göster (sayfayı yenile)
            location.reload();
        }
    });
    
    // Durum filtresi değiştiğinde de aramayı çalıştır
    $('#status').on('change', function() {
        $('#search').trigger('input');
    });
}); 