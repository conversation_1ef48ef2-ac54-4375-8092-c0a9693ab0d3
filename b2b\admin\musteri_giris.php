<?php
session_start();
include '../config.php';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Müşteri ID kontrolü
if (!isset($_GET['musteri_id'])) {
    header('Location: musteriler.php');
    exit;
}

$musteri_id = intval($_GET['musteri_id']);

// Müşteri bilgilerini al ve aktif olup olmadığını kontrol et
$musteri_sql = "SELECT * FROM cari_firmalar WHERE id = ? AND b2b_aktif = 1";
$musteri_stmt = $conn->prepare($musteri_sql);
$musteri_stmt->bind_param('i', $musteri_id);
$musteri_stmt->execute();
$musteri_result = $musteri_stmt->get_result();

if (!$musteri_result || $musteri_result->num_rows == 0) {
    $_SESSION['admin_mesaj'] = "Müşteri bulunamadı veya B2B sisteminde aktif değil.";
    $_SESSION['admin_mesaj_tipi'] = "danger";
    header('Location: musteriler.php');
    exit;
}

$musteri = $musteri_result->fetch_assoc();

// Admin bilgilerini sakla (geri dönmek için)
$_SESSION['admin_onceki_session'] = [
    'b2b_admin_id' => $_SESSION['b2b_admin_id'],
    'b2b_admin_kullanici' => $_SESSION['b2b_admin_kullanici'],
    'b2b_admin_ad_soyad' => $_SESSION['b2b_admin_ad_soyad'] ?? $_SESSION['b2b_admin_kullanici']
];

// B2B admin session'ını temizle
unset($_SESSION['b2b_admin_id']);
unset($_SESSION['b2b_admin_kullanici']);
unset($_SESSION['b2b_admin_ad_soyad']);

// Müşteri adına B2B giriş yap
$_SESSION['b2b_cari_id'] = $musteri_id;
$_SESSION['b2b_firma_adi'] = $musteri['firm_name'];
$_SESSION['admin_adina_siparis'] = true; // Admin adına sipariş olduğunu belirt

// Başarı mesajı
$_SESSION['admin_siparis_mesaj'] = "🛍️ Admin olarak <strong>" . htmlspecialchars($musteri['firm_name']) . "</strong> adına sipariş veriyorsunuz.";

// B2B ana sayfaya yönlendir
header('Location: ../index.php');
exit;
?> 