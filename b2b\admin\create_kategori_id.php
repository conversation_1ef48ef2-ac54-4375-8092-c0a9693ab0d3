<?php
// Veritabanı bağlantısı
include '../config.php';

// Urunler tablosunda kategori_id sütunu var mı?
$result = $conn->query("SHOW COLUMNS FROM urunler LIKE 'kategori_id'");
$exists = ($result->num_rows > 0);

if (!$exists) {
    // kategori_id sütunu ekle
    $sql = "ALTER TABLE urunler ADD COLUMN kategori_id INT NULL";
    if ($conn->query($sql)) {
        // Indeks ekle
        $conn->query("ALTER TABLE urunler ADD INDEX (kategori_id)");
        echo "kategori_id sütunu başarıyla eklendi ve indekslendi.<br>";
    } else {
        echo "Hata: " . $conn->error;
    }
} else {
    echo "kategori_id sütunu zaten mevcut.<br>";
}

echo "<br><a href='urunler.php'><PERSON><PERSON><PERSON><PERSON> Listesine <PERSON>ö<PERSON></a>";
?> 