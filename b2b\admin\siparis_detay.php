<?php
// Output buffering başlat (headers already sent sorunu için)
ob_start();

// Hata raporlamayı aç
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Config dosyası yolunu bul (admin klasöründen kök dizine)
$config_paths = ['../../config.php', '../config.php', '../../b2b/config.php', '../../../config.php', '../../../../config.php'];
$config_loaded = false;

foreach ($config_paths as $path) {
    if (file_exists($path)) {
        include $path;
        if (isset($conn)) {
            $config_loaded = true;
            break;
        }
    }
}

if (!$config_loaded) {
    die("<h1>HATA: Config dosyası bulunamadı!</h1><p>Test edilen yollar:</p><ul><li>" . implode("</li><li>", $config_paths) . "</li></ul>");
}

// Database test
try {
    $test_query = "SELECT 1 as test";
    $test_result = $conn->query($test_query);
    if (!$test_result) {
        die("Database bağlantı hatası: " . $conn->error);
    }
} catch (Exception $e) {
    die("Database exception: " . $e->getMessage());
}

// Page bilgileri
$page_title = "Sipariş Detayı - B2B Admin";
$current_page = "siparisler";

$mesaj = '';
$mesaj_tur = '';

// Sipariş ID kontrolü
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header("Location: siparisler.php");
    exit;
}

$siparis_id = intval($_GET['id']);

// Sipariş durumu güncelleme
if (isset($_POST['durum_guncelle'])) {
    $yeni_durum = $_POST['yeni_durum'];
    
    $update_sql = "UPDATE b2b_siparisler SET durum = ? WHERE id = ?";
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param('si', $yeni_durum, $siparis_id);
    
    if ($update_stmt->execute()) {
        $mesaj = "Sipariş durumu başarıyla güncellendi.";
        $mesaj_tur = "success";
    } else {
        $mesaj = "Sipariş durumu güncellenirken hata oluştu.";
        $mesaj_tur = "danger";
    }
}

// Sipariş bilgilerini çek
$siparis_sql = "SELECT s.*, c.firm_name, c.telefon, c.adres, c.vergi_dairesi, c.vergi_no
                FROM b2b_siparisler s
                LEFT JOIN cari_firmalar c ON s.cari_id = c.id
                WHERE s.id = ?";
                
try {
    $siparis_stmt = $conn->prepare($siparis_sql);
    if (!$siparis_stmt) {
        die("SQL Prepare Hatası: " . $conn->error);
    }
    
    $siparis_stmt->bind_param('i', $siparis_id);
    $execute_result = $siparis_stmt->execute();
    
    if (!$execute_result) {
        die("SQL Execute Hatası: " . $siparis_stmt->error);
    }
    
    $siparis_result = $siparis_stmt->get_result();
    
} catch (Exception $e) {
    die("Sipariş sorgu hatası: " . $e->getMessage());
}

if ($siparis_result->num_rows == 0) {
    header("Location: siparisler.php");
    exit;
}

$siparis = $siparis_result->fetch_assoc();

// Sipariş kalemlerini çek (ürün bilgileri ve takip kodları ile birlikte)
$kalemler_sql = "SELECT sk.*, u.stok_adi as urun_adi, u.stok_kodu as urun_kodu, u.renk,
                        GROUP_CONCAT(DISTINCT p.takip_kodu ORDER BY p.takip_kodu SEPARATOR ', ') as takip_kodlari
                 FROM b2b_siparis_detaylari sk
                 LEFT JOIN urunler u ON sk.urun_id = u.id
                 LEFT JOIN paketler p ON p.urun_id = u.id AND p.durum = 'stokta'
                 WHERE sk.siparis_id = ?
                 GROUP BY sk.id
                 ORDER BY sk.id";
                 
// Debug mesajları kaldırıldı

$kalemler_stmt = $conn->prepare($kalemler_sql);
if ($kalemler_stmt === false) {
    die("Kalem SQL Prepare Hatası: " . $conn->error);
}

$kalemler_stmt->bind_param('i', $siparis_id);
$kalemler_stmt->execute();
$kalemler_result = $kalemler_stmt->get_result();

// Debug: Bulunan kalem sayısı kontrol edildi

$kalemler = [];
$hesaplanan_toplam = 0;
while ($kalem = $kalemler_result->fetch_assoc()) {
    // Debug: Kalem bilgileri işlendi
    $kalemler[] = $kalem;
    $hesaplanan_toplam += ($kalem['birim_fiyat'] * $kalem['adet']);
}

// Sipariş tablosundaki toplam tutarı kullan, yoksa hesaplanan toplamı kullan
$toplam_tutar = $siparis['toplam_tutar'] ?: $hesaplanan_toplam;

// Durum seçenekleri
$durum_secenekleri = [
    'beklemede' => ['label' => 'Beklemede', 'color' => '#f59e0b', 'bg' => 'rgba(245, 158, 11, 0.1)'],
    'hazirlaniyor' => ['label' => 'Hazırlanıyor', 'color' => '#3b82f6', 'bg' => 'rgba(59, 130, 246, 0.1)'],
    'tamamlandi' => ['label' => 'Tamamlandı', 'color' => '#10b981', 'bg' => 'rgba(16, 185, 129, 0.1)'],
    'iptal' => ['label' => 'İptal', 'color' => '#ef4444', 'bg' => 'rgba(239, 68, 68, 0.1)']
];

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-shopping-cart"></i>
            Sipariş Detayı #<?php echo $siparis_id; ?>
        </h1>
        <a href="siparisler.php" class="btn">
            <i class="fas fa-arrow-left"></i>
            Siparişler
        </a>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<style>
    .detail-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
    }
    
    .info-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 1.5rem;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--gray-light);
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: var(--gray);
    }
    
    .info-value {
        font-weight: 500;
        color: var(--dark);
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .items-table {
        width: 100%;
        border-collapse: collapse;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
    
    .items-table th {
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        color: white;
        font-weight: 600;
        padding: 1rem;
        text-align: left;
        font-size: 0.875rem;
    }
    
    .items-table td {
        padding: 1rem;
        border-bottom: 1px solid var(--gray-light);
        vertical-align: middle;
    }
    
    .items-table tr:hover {
        background-color: rgba(249, 115, 22, 0.05);
    }
    
    .items-table tr:last-child td {
        border-bottom: none;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-group label {
        display: block;
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 0.5rem;
    }
    
    .form-group select {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--gray-light);
        border-radius: 0.5rem;
        font-size: 1rem;
        background: white;
    }
    
    .form-group select:focus {
        outline: none;
        border-color: var(--secondary);
    }
    
    @media (max-width: 768px) {
        .detail-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .info-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>

<div class="detail-grid">
    <div>
        <!-- Sipariş Bilgileri -->
        <div class="info-card">
            <h3 style="margin-bottom: 1rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-info-circle"></i>
                Sipariş Bilgileri
            </h3>
            
            <div class="info-row">
                <span class="info-label">Sipariş No:</span>
                <span class="info-value">#<?php echo $siparis['id']; ?></span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Tarih:</span>
                <span class="info-value"><?php echo date('d.m.Y H:i', strtotime($siparis['siparis_tarihi'])); ?></span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Durum:</span>
                <span class="info-value">
                    <?php 
                    $durum_info = $durum_secenekleri[$siparis['durum']] ?? $durum_secenekleri['beklemede'];
                    ?>
                    <span class="status-badge" style="background-color: <?php echo $durum_info['bg']; ?>; color: <?php echo $durum_info['color']; ?>;">
                        <i class="fas fa-circle"></i>
                        <?php echo $durum_info['label']; ?>
                    </span>
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Toplam Tutar:</span>
                <span class="info-value" style="font-size: 1.25rem; font-weight: 700; color: var(--secondary);">
                    <?php echo number_format($toplam_tutar, 2, ',', '.'); ?> ₺
                </span>
            </div>
            
            <?php if ($siparis['siparis_notu']): ?>
            <div class="info-row">
                <span class="info-label">Sipariş Notu:</span>
                <span class="info-value"><?php echo nl2br(htmlspecialchars($siparis['siparis_notu'])); ?></span>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Sipariş Kalemleri -->
        <div class="info-card">
            <h3 style="margin-bottom: 1rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-list"></i>
                Sipariş Kalemleri (<?php echo count($kalemler); ?> ürün)
            </h3>
            
            <?php if (empty($kalemler)): ?>
                <div style="text-align: center; padding: 2rem; color: var(--gray);">
                    <i class="fas fa-box-open" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>Bu siparişte hiç ürün bulunmuyor.</p>
                </div>
            <?php else: ?>
                <div style="overflow-x: auto;">
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>Ürün Kodu</th>
                                <th>Ürün Adı</th>
                                <th>Renk</th>
                                <th>Takip Kodları</th>
                                <th>Adet</th>
                                <th>Fiyat</th>
                                <th>Toplam</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($kalemler as $kalem): ?>
                            <tr>
                                <td style="font-weight: 600; color: var(--secondary);">
                                    <?php echo htmlspecialchars($kalem['urun_kodu'] ?? 'N/A'); ?>
                                </td>
                                <td style="font-weight: 600; color: var(--dark);">
                                    <?php echo htmlspecialchars($kalem['urun_adi'] ?? 'Ürün bulunamadı'); ?>
                                </td>
                                <td style="font-size: 0.875rem; color: var(--dark); font-weight: 600;">
                                    <?php echo $kalem['renk'] ? htmlspecialchars($kalem['renk']) : '<span style="color: #999;">-</span>'; ?>
                                </td>
                                <td style="font-size: 0.875rem; color: var(--gray); font-weight: 600;">
                                    <?php echo $kalem['takip_kodlari'] ? htmlspecialchars($kalem['takip_kodlari']) : '<span style="color: #999;">-</span>'; ?>
                                </td>
                                <td style="text-align: center; font-weight: 600;">
                                    <?php echo number_format($kalem['adet'], 0, ',', '.'); ?>
                                </td>
                                <td style="text-align: right; font-weight: 600;">
                                    <?php echo number_format($kalem['birim_fiyat'], 2, ',', '.'); ?> ₺
                                </td>
                                <td style="text-align: right; font-weight: 700; color: var(--secondary);">
                                    <?php echo number_format($kalem['birim_fiyat'] * $kalem['adet'], 2, ',', '.'); ?> ₺
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr style="background: linear-gradient(135deg, var(--primary), var(--primary-dark)); color: white;">
                                <th colspan="6" style="text-align: right; font-size: 1rem;">GENEL TOPLAM:</th>
                                <th style="text-align: right; font-size: 1.125rem;">
                                    <?php echo number_format($toplam_tutar, 2, ',', '.'); ?> ₺
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div>
        <!-- Müşteri Bilgileri -->
        <div class="info-card">
            <h3 style="margin-bottom: 1rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-building"></i>
                Müşteri Bilgileri
            </h3>
            
            <div class="info-row">
                <span class="info-label">Firma Adı:</span>
                <span class="info-value"><?php echo htmlspecialchars($siparis['firm_name'] ?? 'N/A'); ?></span>
            </div>
            
            <?php if ($siparis['vergi_dairesi'] || $siparis['vergi_no']): ?>
            <div class="info-row">
                <span class="info-label">Vergi Bilgileri:</span>
                <span class="info-value">
                    <?php if ($siparis['vergi_dairesi']): ?>
                        <?php echo htmlspecialchars($siparis['vergi_dairesi']); ?>
                        <?php if ($siparis['vergi_no']): ?>
                            - <?php echo htmlspecialchars($siparis['vergi_no']); ?>
                        <?php endif; ?>
                    <?php elseif ($siparis['vergi_no']): ?>
                        <?php echo htmlspecialchars($siparis['vergi_no']); ?>
                    <?php endif; ?>
                </span>
            </div>
            <?php endif; ?>
            
            <div class="info-row">
                <span class="info-label">Telefon:</span>
                <span class="info-value">
                    <?php if ($siparis['telefon']): ?>
                        <a href="tel:<?php echo $siparis['telefon']; ?>" style="color: var(--secondary); text-decoration: none;">
                            <i class="fas fa-phone"></i>
                            <?php echo htmlspecialchars($siparis['telefon']); ?>
                        </a>
                    <?php else: ?>
                        N/A
                    <?php endif; ?>
                </span>
            </div>
            
            <?php if ($siparis['adres']): ?>
            <div class="info-row">
                <span class="info-label">Adres:</span>
                <span class="info-value"><?php echo nl2br(htmlspecialchars($siparis['adres'])); ?></span>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Durum Güncelleme -->
        <div class="info-card">
            <h3 style="margin-bottom: 1rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-edit"></i>
                Durum Güncelle
            </h3>
            
            <form method="post">
                <div class="form-group">
                    <label for="yeni_durum">Yeni Durum:</label>
                    <select name="yeni_durum" id="yeni_durum" required>
                        <?php foreach ($durum_secenekleri as $durum_key => $durum_info): ?>
                        <option value="<?php echo $durum_key; ?>" <?php echo ($siparis['durum'] == $durum_key) ? 'selected' : ''; ?>>
                            <?php echo $durum_info['label']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" name="durum_guncelle" class="btn" style="width: 100%;">
                    <i class="fas fa-save"></i>
                    Durumu Güncelle
                </button>
            </form>
        </div>
        
        <!-- Hızlı İşlemler -->
        <div class="info-card">
            <h3 style="margin-bottom: 1rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-lightning-bolt"></i>
                Hızlı İşlemler
            </h3>
            
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                <a href="cari_detay.php?id=<?php echo $siparis['cari_id']; ?>" class="btn" style="justify-content: center;">
                    <i class="fas fa-user"></i>
                    Müşteriyi Görüntüle
                </a>
                
                <button onclick="window.print()" class="btn" style="background: var(--success); justify-content: center;">
                    <i class="fas fa-print"></i>
                    Yazdır
                </button>
                
                <a href="siparis_duzenle.php?id=<?php echo $siparis_id; ?>" class="btn" style="background: var(--warning); justify-content: center;">
                    <i class="fas fa-edit"></i>
                    Düzenle
                </a>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<?php 
// Output buffering'i temizle ve gönder
ob_end_flush(); 
?> 