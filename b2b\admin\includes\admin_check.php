<?php
// Admin panel güvenlik kontrolü
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Admin giriş kontrolü
if (!isset($_SESSION['b2b_admin_id']) || empty($_SESSION['b2b_admin_id'])) {
    // Ajax istekse JSON yanıt
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Unauthorized access']);
        exit;
    }
    
    // Normal istekse login sayfasına yönlendir
    header("Location: index.php?error=unauthorized");
    exit;
}

// Admin bilgilerini global değişkenlere ata
$GLOBALS['admin_id'] = $_SESSION['b2b_admin_id'];
$GLOBALS['admin_kullanici'] = $_SESSION['b2b_admin_kullanici'] ?? '';
$GLOBALS['admin_ad_soyad'] = $_SESSION['b2b_admin_ad_soyad'] ?? $GLOBALS['admin_kullanici'];
?> 