<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "Admin Yönetimi - B2B Admin";
$current_page = "admin_yonetimi";

$mesaj = '';
$mesaj_tur = '';

// G<PERSON>ş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Mevcut admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];
$admin_kullanici = $_SESSION['b2b_admin_kullanici'];
$admin_ad_soyad = $_SESSION['b2b_admin_ad_soyad'] ?? $admin_kullanici;

// Yeni admin ekleme
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['admin_ekle'])) {
    $kullanici_adi = trim($_POST['kullanici_adi']);
    $sifre = trim($_POST['sifre']);
    $ad_soyad = trim($_POST['ad_soyad']);
    $email = trim($_POST['email']);
    $yetki_seviyesi = (int)$_POST['yetki_seviyesi'];
    
    // Validation
    if (empty($kullanici_adi) || empty($sifre) || empty($ad_soyad)) {
        $mesaj = "Kullanıcı adı, şifre ve ad soyad alanları zorunludur.";
        $mesaj_tur = "danger";
    } elseif (strlen($sifre) < 6) {
        $mesaj = "Şifre en az 6 karakter olmalıdır.";
        $mesaj_tur = "danger";
    } else {
        // Kullanıcı adı kontrolü
        $check_sql = "SELECT id FROM b2b_admin WHERE kullanici_adi = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param('s', $kullanici_adi);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $mesaj = "Bu kullanıcı adı zaten kullanılıyor.";
            $mesaj_tur = "danger";
        } else {
            // Şifreyi hash'le
            $hashed_password = password_hash($sifre, PASSWORD_DEFAULT);
            
            // Admin ekle
            $insert_sql = "INSERT INTO b2b_admin (kullanici_adi, sifre, ad_soyad, email, yetki_seviyesi, aktif, olusturan_admin_id) VALUES (?, ?, ?, ?, ?, 1, ?)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param('ssssii', $kullanici_adi, $hashed_password, $ad_soyad, $email, $yetki_seviyesi, $admin_id);
            
            if ($insert_stmt->execute()) {
                $mesaj = "Admin başarıyla eklendi!";
                $mesaj_tur = "success";
            } else {
                $mesaj = "Admin eklenirken hata oluştu: " . $conn->error;
                $mesaj_tur = "danger";
            }
        }
    }
}

// Admin durumu güncelleme
if (isset($_GET['durum_guncelle']) && isset($_GET['admin_id']) && isset($_GET['aktif'])) {
    $update_admin_id = (int)$_GET['admin_id'];
    $yeni_durum = (int)$_GET['aktif'];
    
    // Kendi hesabını deaktif etmeyi engelle
    if ($update_admin_id == $admin_id && $yeni_durum == 0) {
        $mesaj = "Kendi hesabınızı deaktif edemezsiniz!";
        $mesaj_tur = "danger";
    } else {
        $update_sql = "UPDATE b2b_admin SET aktif = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param('ii', $yeni_durum, $update_admin_id);
        
        if ($update_stmt->execute()) {
            $mesaj = "Admin durumu başarıyla güncellendi.";
            $mesaj_tur = "success";
        } else {
            $mesaj = "Admin durumu güncellenirken hata oluştu.";
            $mesaj_tur = "danger";
        }
    }
}

// Admin listesini getir
$adminler = [];
$sql = "SELECT 
            a.*,
            (SELECT ad_soyad FROM b2b_admin WHERE id = a.olusturan_admin_id) as olusturan_admin_adi,
            (SELECT COUNT(*) FROM cari_firmalar WHERE ekleyen_admin_id = a.id) as eklenen_musteri_sayisi
        FROM b2b_admin a
        ORDER BY a.olusturma_tarihi DESC";

$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $adminler[] = $row;
    }
}

include 'includes/header.php';
?>

<style>
    .container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .page-header {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .page-header h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 700;
    }
    
    .form-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .admin-table {
        width: 100%;
        border-collapse: collapse;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
    
    .admin-table th {
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        color: white;
        font-weight: 600;
        padding: 1rem;
        text-align: left;
        font-size: 0.875rem;
    }
    
    .admin-table td {
        padding: 1rem;
        border-bottom: 1px solid var(--gray-light);
        vertical-align: middle;
    }
    
    .admin-table tr:hover {
        background-color: rgba(249, 115, 22, 0.05);
    }
    
    .status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
    }
    
    .status-active {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }
    
    .status-inactive {
        background-color: rgba(239, 68, 68, 0.1);
        color: var(--danger);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .yetki-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .yetki-1 {
        background-color: rgba(239, 68, 68, 0.1);
        color: var(--danger);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .yetki-2 {
        background-color: rgba(249, 115, 22, 0.1);
        color: var(--secondary);
        border: 1px solid rgba(249, 115, 22, 0.2);
    }
    
    .yetki-3 {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }
    
    .action-btn {
        padding: 0.5rem 0.875rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        margin-right: 0.5rem;
    }
    
    .action-btn:hover {
        transform: translateY(-1px);
    }
    
    .action-activate {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success);
        border-color: rgba(16, 185, 129, 0.2);
    }
    
    .action-activate:hover {
        background-color: var(--success);
        color: white;
    }
    
    .action-deactivate {
        background-color: rgba(239, 68, 68, 0.1);
        color: var(--danger);
        border-color: rgba(239, 68, 68, 0.2);
    }
    
    .action-deactivate:hover {
        background-color: var(--danger);
        color: white;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--dark);
    }
    
    .form-control {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 1px solid var(--gray-light);
        border-radius: 0.5rem;
        font-family: inherit;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: white;
    }
    
    .form-control:focus {
        outline: none;
        border-color: var(--secondary);
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
    }
    
    .btn {
        padding: 0.875rem 2rem;
        background: var(--secondary);
        color: white;
        border: none;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn:hover {
        background: var(--accent);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
    }
    
    .btn-secondary {
        background: var(--gray);
        color: white;
    }
    
    .btn-secondary:hover {
        background: var(--dark);
    }
    
    .alert {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    .alert-success {
        background-color: #f0fdf4;
        border: 1px solid #bbf7d0;
        color: #16a34a;
    }
    
    .alert-danger {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }
    
    .row {
        display: flex;
        gap: 1rem;
        margin: 0 -0.5rem;
    }
    
    .col-md-6 {
        flex: 1;
        padding: 0 0.5rem;
    }
    
    .back-link {
        margin-bottom: 2rem;
    }
</style>

<div class="container">
    <div class="back-link">
        <a href="dashboard.php" class="btn btn-secondary">
            ← Dashboard'a Dön
        </a>
    </div>
    
    <div class="page-header">
        <h1>👥 Admin Yönetimi</h1>
        <p>B2B sistemindeki admin kullanıcıları yönetin</p>
    </div>
    
    <?php if (!empty($mesaj)): ?>
        <div class="alert alert-<?php echo $mesaj_tur; ?>">
            <?php echo $mesaj; ?>
        </div>
    <?php endif; ?>
    
    <!-- Yeni Admin Ekleme Formu -->
    <div class="form-card">
        <h3 style="margin-bottom: 1.5rem; color: var(--dark);">
            <i class="fas fa-user-plus"></i>
            Yeni Admin Ekle
        </h3>
        
        <form method="post" action="admin_yonetimi.php">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="kullanici_adi" class="form-label">Kullanıcı Adı *</label>
                        <input type="text" class="form-control" id="kullanici_adi" name="kullanici_adi" 
                               placeholder="Kullanıcı adını girin" required 
                               value="<?php echo isset($_POST['kullanici_adi']) ? htmlspecialchars($_POST['kullanici_adi']) : ''; ?>">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="sifre" class="form-label">Şifre *</label>
                        <input type="password" class="form-control" id="sifre" name="sifre" 
                               placeholder="En az 6 karakter" required minlength="6">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="ad_soyad" class="form-label">Ad Soyad *</label>
                        <input type="text" class="form-control" id="ad_soyad" name="ad_soyad" 
                               placeholder="Ad ve soyadını girin" required 
                               value="<?php echo isset($_POST['ad_soyad']) ? htmlspecialchars($_POST['ad_soyad']) : ''; ?>">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email" class="form-label">E-posta</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="<EMAIL>" 
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="yetki_seviyesi" class="form-label">Yetki Seviyesi</label>
                <select class="form-control" id="yetki_seviyesi" name="yetki_seviyesi" required>
                    <option value="3">Düşük Yetki (Sadece görüntüleme)</option>
                    <option value="2" selected>Orta Yetki (Müşteri işlemleri)</option>
                    <option value="1">Yüksek Yetki (Tüm yetkiler)</option>
                </select>
            </div>
            
            <div style="text-align: center; margin-top: 2rem;">
                <button type="submit" name="admin_ekle" class="btn">
                    <i class="fas fa-user-plus"></i>
                    Admin Ekle
                </button>
            </div>
        </form>
    </div>
    
    <!-- Admin Listesi -->
    <div class="form-card">
        <h3 style="margin-bottom: 1.5rem; color: var(--dark);">
            <i class="fas fa-users"></i>
            Mevcut Adminler
        </h3>
        
        <?php if (empty($adminler)): ?>
            <div style="text-align: center; padding: 3rem; color: var(--gray);">
                <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <h4 style="margin-bottom: 0.5rem;">Henüz admin bulunmuyor</h4>
                <p>Yukarıdaki formu kullanarak yeni admin ekleyebilirsiniz.</p>
            </div>
        <?php else: ?>
            <div style="overflow-x: auto;">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Kullanıcı Adı</th>
                            <th>Ad Soyad</th>
                            <th>E-posta</th>
                            <th>Yetki Seviyesi</th>
                            <th>Eklenen Müşteri</th>
                            <th>Kim Ekledi</th>
                            <th>Son Giriş</th>
                            <th>Durum</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($adminler as $admin_user): ?>
                        <tr>
                            <td style="font-weight: 600;"><?php echo $admin_user['id']; ?></td>
                            <td style="font-weight: 600; color: var(--dark);">
                                <?php echo htmlspecialchars($admin_user['kullanici_adi']); ?>
                                <?php if ($admin_user['id'] == $admin_id): ?>
                                    <span style="color: var(--secondary); font-size: 0.75rem;">(Siz)</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($admin_user['ad_soyad']); ?></td>
                            <td><?php echo htmlspecialchars($admin_user['email'] ?? ''); ?></td>
                            <td>
                                <?php
                                $yetki_text = '';
                                $yetki_class = '';
                                switch($admin_user['yetki_seviyesi']) {
                                    case 1:
                                        $yetki_text = 'Yüksek Yetki';
                                        $yetki_class = 'yetki-1';
                                        break;
                                    case 2:
                                        $yetki_text = 'Orta Yetki';
                                        $yetki_class = 'yetki-2';
                                        break;
                                    case 3:
                                        $yetki_text = 'Düşük Yetki';
                                        $yetki_class = 'yetki-3';
                                        break;
                                }
                                ?>
                                <span class="yetki-badge <?php echo $yetki_class; ?>">
                                    <?php echo $yetki_text; ?>
                                </span>
                            </td>
                            <td style="font-weight: 600; color: var(--secondary);">
                                <?php echo $admin_user['eklenen_musteri_sayisi']; ?> müşteri
                            </td>
                            <td style="font-size: 0.8rem;">
                                <?php if (!empty($admin_user['olusturan_admin_adi'])): ?>
                                    <i class="fas fa-user-shield"></i>
                                    <?php echo htmlspecialchars($admin_user['olusturan_admin_adi']); ?>
                                <?php else: ?>
                                    <span style="color: var(--gray); font-style: italic;">Sistem</span>
                                <?php endif; ?>
                            </td>
                            <td style="font-size: 0.8rem;">
                                <?php echo $admin_user['son_giris'] ? date('d.m.Y H:i', strtotime($admin_user['son_giris'])) : 'Hiç giriş yapmamış'; ?>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $admin_user['aktif'] ? 'status-active' : 'status-inactive'; ?>">
                                    <i class="fas fa-<?php echo $admin_user['aktif'] ? 'check-circle' : 'times-circle'; ?>"></i>
                                    <?php echo $admin_user['aktif'] ? 'Aktif' : 'Pasif'; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($admin_user['id'] != $admin_id): ?>
                                    <?php if ($admin_user['aktif']): ?>
                                        <a href="admin_yonetimi.php?durum_guncelle=1&admin_id=<?php echo $admin_user['id']; ?>&aktif=0" 
                                           class="action-btn action-deactivate" 
                                           onclick="return confirm('Bu admini pasif duruma almak istediğinize emin misiniz?')"
                                           title="Pasif Yap">
                                            <i class="fas fa-times"></i>
                                            Pasif Yap
                                        </a>
                                    <?php else: ?>
                                        <a href="admin_yonetimi.php?durum_guncelle=1&admin_id=<?php echo $admin_user['id']; ?>&aktif=1" 
                                           class="action-btn action-activate"
                                           title="Aktif Yap">
                                            <i class="fas fa-check"></i>
                                            Aktif Yap
                                        </a>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="color: var(--gray); font-style: italic; font-size: 0.8rem;">
                                        Kendi hesabınız
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
</rewritten_file>