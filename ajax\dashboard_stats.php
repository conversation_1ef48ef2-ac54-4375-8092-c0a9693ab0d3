<?php
// ajax/dashboard_stats.php - Dashboard İstatistikleri
require_once '../includes/db.php';

header('Content-Type: application/json');

try {
    // Toplam plan sayısı
    $stmt = $db->query("SELECT COUNT(*) as total FROM uretim_planlari");
    $total_plans = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Tamamlanan planlar
    $stmt = $db->query("SELECT COUNT(*) as total FROM uretim_planlari WHERE durum = 'tamamlandi'");
    $completed_plans = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Devam eden planlar
    $stmt = $db->query("SELECT COUNT(*) as total FROM uretim_planlari WHERE durum IN ('basladi', 'devam_ediyor')");
    $in_progress_plans = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Geciken planlar (teslim tarihi geçmiş ve tamamlanmamış)
    $stmt = $db->query("SELECT COUNT(*) as total FROM uretim_planlari WHERE bitis_tarihi < CURDATE() AND durum != 'tamamlandi'");
    $delayed_plans = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Toplam üretim adedi
    $stmt = $db->query("SELECT SUM(miktar) as total FROM uretim_planlari WHERE durum = 'tamamlandi'");
    $total_production = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?: 0;

    // Verimlilik oranı (tamamlanan / toplam)
    $efficiency_rate = $total_plans > 0 ? round(($completed_plans / $total_plans) * 100, 1) : 0;

    echo json_encode([
        'success' => true,
        'stats' => [
            'total_plans' => $total_plans,
            'completed_plans' => $completed_plans,
            'in_progress_plans' => $in_progress_plans,
            'delayed_plans' => $delayed_plans,
            'total_production' => $total_production,
            'efficiency_rate' => $efficiency_rate
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'İstatistik hatası: ' . $e->getMessage()
    ]);
}
?> 