<?php
session_start();
include '../config.php';

// Page bilgileri
$page_title = "Kategoriler - B2B Admin";
$current_page = "kategoriler";

$mesaj = '';
$mesaj_tur = '';

// Giriş kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    header('Location: index.php');
    exit;
}

// Admin bilgileri
$admin_id = $_SESSION['b2b_admin_id'];
$admin_kullanici = $_SESSION['b2b_admin_kullanici'];
$admin_ad_soyad = $_SESSION['b2b_admin_ad_soyad'] ?? $admin_kullanici;

// Düzenleme için seçili kategori
$duzenle = null;
if (isset($_GET['duzenle'])) {
    $duzenle_id = intval($_GET['duzenle']);
    $sql = "SELECT * FROM b2b_kategoriler WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $duzenle_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result && $result->num_rows > 0) {
        $duzenle = $result->fetch_assoc();
    }
}

// Kategori ekleme/güncelleme
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $kategori_adi = trim($_POST['kategori_adi']);
    $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
    $aciklama = trim($_POST['aciklama']);
    $sira = intval($_POST['sira']);
    $gorunur = isset($_POST['gorunur']) ? 1 : 0;
    
    if (empty($kategori_adi)) {
        $mesaj = "Kategori adı gereklidir.";
        $mesaj_tur = "danger";
    } else {
        if (isset($_POST['id']) && !empty($_POST['id'])) {
            // Güncelleme
            $id = intval($_POST['id']);
            $sql = "UPDATE b2b_kategoriler SET kategori_adi = ?, parent_id = ?, aciklama = ?, sira = ?, gorunur = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('sisiii', $kategori_adi, $parent_id, $aciklama, $sira, $gorunur, $id);
            
            if ($stmt->execute()) {
                $mesaj = "Kategori başarıyla güncellendi.";
                $mesaj_tur = "success";
                $duzenle = null; // Düzenleme modundan çık
            } else {
                $mesaj = "Kategori güncellenirken hata oluştu.";
                $mesaj_tur = "danger";
            }
        } else {
            // Ekleme
            $sql = "INSERT INTO b2b_kategoriler (kategori_adi, parent_id, aciklama, sira, gorunur) VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('sisii', $kategori_adi, $parent_id, $aciklama, $sira, $gorunur);
            
            if ($stmt->execute()) {
                $mesaj = "Kategori başarıyla eklendi.";
                $mesaj_tur = "success";
            } else {
                $mesaj = "Kategori eklenirken hata oluştu.";
                $mesaj_tur = "danger";
            }
        }
    }
}

// Kategori silme
if (isset($_GET['sil'])) {
    $sil_id = intval($_GET['sil']);
    
    // Bu kategoriye bağlı ürün var mı kontrol et
    $check_sql = "SELECT COUNT(*) as sayi FROM urunler WHERE kategori_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param('i', $sil_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $check = $check_result->fetch_assoc();
    
    if ($check['sayi'] > 0) {
        $mesaj = "Bu kategoriye bağlı " . $check['sayi'] . " ürün bulunduğu için silinemez.";
        $mesaj_tur = "warning";
    } else {
        // Alt kategori var mı kontrol et
        $check_alt_sql = "SELECT COUNT(*) as sayi FROM b2b_kategoriler WHERE parent_id = ?";
        $check_alt_stmt = $conn->prepare($check_alt_sql);
        $check_alt_stmt->bind_param('i', $sil_id);
        $check_alt_stmt->execute();
        $check_alt_result = $check_alt_stmt->get_result();
        $check_alt = $check_alt_result->fetch_assoc();
        
        if ($check_alt['sayi'] > 0) {
            $mesaj = "Bu kategorinin " . $check_alt['sayi'] . " alt kategorisi bulunduğu için silinemez.";
            $mesaj_tur = "warning";
        } else {
            $sql = "DELETE FROM b2b_kategoriler WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('i', $sil_id);
            
            if ($stmt->execute()) {
                $mesaj = "Kategori başarıyla silindi.";
                $mesaj_tur = "success";
            } else {
                $mesaj = "Kategori silinirken hata oluştu.";
                $mesaj_tur = "danger";
            }
        }
    }
}

// Kategorileri listele - ana urunler tablosundan ürün sayısını hesapla
$kategoriler = [];
$sql = "SELECT k.*, 
        (SELECT COUNT(*) FROM urunler WHERE kategori_id = k.id) as urun_sayisi,
        (SELECT kategori_adi FROM b2b_kategoriler WHERE id = k.parent_id) as ust_kategori
        FROM b2b_kategoriler k 
        ORDER BY k.sira, k.kategori_adi";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $kategoriler[] = $row;
    }
}

// Üst kategoriler için liste
$ust_kategoriler = [];
$sql = "SELECT id, kategori_adi FROM b2b_kategoriler WHERE parent_id IS NULL OR parent_id = 0 ORDER BY sira, kategori_adi";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $ust_kategoriler[] = $row;
    }
}

// Header dahil et
include 'includes/header.php';
?>

<div class="page-header">
    <div class="page-title">
        <h1>
            <i class="fas fa-tags"></i>
            Kategori Yönetimi
        </h1>
    </div>
</div>

<?php if ($mesaj): ?>
    <div class="alert alert-<?php echo $mesaj_tur; ?>">
        <i class="fas fa-<?php echo $mesaj_tur == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $mesaj; ?>
    </div>
<?php endif; ?>

<!-- Kategori Ekleme/Düzenleme Formu -->
<div class="content-card" style="margin-bottom: 2rem;">
    <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-<?php echo $duzenle ? 'edit' : 'plus'; ?>"></i>
        <?php echo $duzenle ? 'Kategori Düzenle' : 'Kategori Ekle'; ?>
    </h3>
    
    <form method="post" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
        <?php if ($duzenle): ?>
            <input type="hidden" name="id" value="<?php echo $duzenle['id']; ?>">
        <?php endif; ?>
        
        <div class="form-group">
            <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                <i class="fas fa-tag"></i>
                Kategori Adı *
            </label>
            <input type="text" name="kategori_adi" 
                   style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem; transition: all 0.3s ease;"
                   value="<?php echo htmlspecialchars($duzenle['kategori_adi'] ?? ''); ?>" 
                   required
                   onfocus="this.style.borderColor='var(--secondary)'"
                   onblur="this.style.borderColor='var(--gray-light)'">
        </div>
        
        <div class="form-group">
            <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                <i class="fas fa-layer-group"></i>
                Üst Kategori
            </label>
            <select name="parent_id" style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;">
                <option value="">Ana Kategori</option>
                <?php foreach ($ust_kategoriler as $ust_kat): ?>
                    <?php if (!$duzenle || $ust_kat['id'] != $duzenle['id']): ?>
                        <option value="<?php echo $ust_kat['id']; ?>" 
                                <?php echo ($duzenle && $duzenle['parent_id'] == $ust_kat['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($ust_kat['kategori_adi']); ?>
                        </option>
                    <?php endif; ?>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                <i class="fas fa-sort-numeric-up"></i>
                Sıralama
            </label>
            <input type="number" name="sira" 
                   style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem;"
                   value="<?php echo $duzenle['sira'] ?? 0; ?>" 
                   min="0">
        </div>
        
        <div class="form-group">
            <label style="font-weight: 600; color: var(--dark); display: block; margin-bottom: 0.5rem;">
                <i class="fas fa-align-left"></i>
                Açıklama
            </label>
            <textarea name="aciklama" 
                      style="width: 100%; padding: 0.875rem; border: 2px solid var(--gray-light); border-radius: 0.5rem; min-height: 80px; resize: vertical;"
                      placeholder="Kategori açıklaması..."><?php echo htmlspecialchars($duzenle['aciklama'] ?? ''); ?></textarea>
        </div>
        
        <div style="display: flex; flex-direction: column; gap: 1rem;">
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <input type="checkbox" name="gorunur" id="gorunur" 
                       <?php echo (!$duzenle || $duzenle['gorunur']) ? 'checked' : ''; ?>
                       style="width: 18px; height: 18px;">
                <label for="gorunur" style="font-weight: 600; color: var(--dark);">
                    <i class="fas fa-eye"></i>
                    Görünür
                </label>
            </div>
            
            <div style="display: flex; gap: 1rem;">
                <button type="submit" class="btn" style="flex: 1;">
                    <i class="fas fa-<?php echo $duzenle ? 'save' : 'plus'; ?>"></i>
                    <?php echo $duzenle ? 'Güncelle' : 'Ekle'; ?>
                </button>
                
                <?php if ($duzenle): ?>
                    <a href="kategoriler.php" class="btn" style="flex: 1; background: var(--gray); text-align: center; text-decoration: none;">
                        <i class="fas fa-times"></i>
                        İptal
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </form>
</div>

<!-- Kategori Listesi -->
<div class="content-card">
    <h3 style="margin-bottom: 1.5rem; color: var(--dark); display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-list"></i>
        Kategori Listesi
    </h3>
    
    <?php if (empty($kategoriler)): ?>
        <div style="text-align: center; padding: 3rem; color: var(--gray);">
            <i class="fas fa-tags" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem;">Henüz kategori bulunmuyor</h3>
            <p>Yukarıdaki formu kullanarak yeni kategori ekleyebilirsiniz.</p>
        </div>
    <?php else: ?>
        <style>
            .categories-table {
                width: 100%;
                border-collapse: collapse;
                border-radius: 0.75rem;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            }
            .categories-table th {
                background: linear-gradient(135deg, var(--primary), var(--primary-dark));
                color: white;
                font-weight: 600;
                padding: 1rem;
                text-align: left;
                font-size: 0.875rem;
            }
            .categories-table td {
                padding: 1rem;
                border-bottom: 1px solid var(--gray-light);
                vertical-align: middle;
            }
            .categories-table tr:hover {
                background-color: rgba(249, 115, 22, 0.05);
            }
            .categories-table tr:last-child td {
                border-bottom: none;
            }
            .status-badge {
                padding: 0.375rem 0.75rem;
                border-radius: 0.5rem;
                font-size: 0.75rem;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.375rem;
            }
            .status-visible {
                background-color: rgba(16, 185, 129, 0.1);
                color: var(--success);
                border: 1px solid rgba(16, 185, 129, 0.2);
            }
            .status-hidden {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger);
                border: 1px solid rgba(239, 68, 68, 0.2);
            }
            .action-links {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                align-items: center;
            }
            .action-btn {
                padding: 0.5rem 0.875rem;
                border-radius: 0.5rem;
                text-decoration: none;
                font-size: 0.8rem;
                font-weight: 600;
                display: inline-flex;
                align-items: center;
                gap: 0.375rem;
                transition: all 0.3s ease;
                border: 1px solid transparent;
            }
            .action-btn:hover {
                transform: translateY(-1px);
            }
            .action-edit {
                background-color: rgba(249, 115, 22, 0.1);
                color: var(--secondary);
                border-color: rgba(249, 115, 22, 0.2);
            }
            .action-edit:hover {
                background-color: var(--secondary);
                color: white;
            }
            .action-delete {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger);
                border-color: rgba(239, 68, 68, 0.2);
            }
            .action-delete:hover {
                background-color: var(--danger);
                color: white;
            }
        </style>
        
        <div style="overflow-x: auto;">
            <table class="categories-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Kategori Adı</th>
                        <th>Üst Kategori</th>
                        <th>Açıklama</th>
                        <th>Sıra</th>
                        <th>Ürün Sayısı</th>
                        <th>Durum</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($kategoriler as $kategori): ?>
                    <tr>
                        <td style="font-weight: 600;"><?php echo $kategori['id']; ?></td>
                        <td style="font-weight: 600; color: var(--dark);">
                            <?php echo htmlspecialchars($kategori['kategori_adi']); ?>
                        </td>
                        <td>
                            <?php if ($kategori['ust_kategori']): ?>
                                <span style="color: var(--gray); font-size: 0.875rem;">
                                    <?php echo htmlspecialchars($kategori['ust_kategori']); ?>
                                </span>
                            <?php else: ?>
                                <span style="color: var(--gray); font-style: italic;">Ana Kategori</span>
                            <?php endif; ?>
                        </td>
                        <td style="max-width: 200px;">
                            <?php if ($kategori['aciklama']): ?>
                                <span title="<?php echo htmlspecialchars($kategori['aciklama']); ?>">
                                    <?php echo htmlspecialchars(substr($kategori['aciklama'], 0, 50) . (strlen($kategori['aciklama']) > 50 ? '...' : '')); ?>
                                </span>
                            <?php else: ?>
                                <span style="color: var(--gray); font-style: italic;">-</span>
                            <?php endif; ?>
                        </td>
                        <td style="font-weight: 600;"><?php echo $kategori['sira']; ?></td>
                        <td>
                            <span style="background: rgba(59, 130, 246, 0.1); color: #1e40af; padding: 0.25rem 0.5rem; border-radius: 0.375rem; font-weight: 600; font-size: 0.75rem;">
                                <?php echo $kategori['urun_sayisi']; ?> ürün
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-<?php echo $kategori['gorunur'] ? 'visible' : 'hidden'; ?>">
                                <i class="fas fa-<?php echo $kategori['gorunur'] ? 'eye' : 'eye-slash'; ?>"></i>
                                <?php echo $kategori['gorunur'] ? 'Görünür' : 'Gizli'; ?>
                            </span>
                        </td>
                        <td>
                            <div class="action-links">
                                <a href="kategoriler.php?duzenle=<?php echo $kategori['id']; ?>" class="action-btn action-edit" title="Düzenle">
                                    <i class="fas fa-edit"></i>
                                    Düzenle
                                </a>
                                <a href="kategoriler.php?sil=<?php echo $kategori['id']; ?>" 
                                   class="action-btn action-delete" 
                                   onclick="return confirm('Bu kategoriyi silmek istediğinize emin misiniz?')"
                                   title="Sil">
                                    <i class="fas fa-trash"></i>
                                    Sil
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?> 