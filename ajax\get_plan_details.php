<?php
// ajax/get_plan_details.php - Plan Detaylarını Getir
require_once '../config/database.php';

header('Content-Type: application/json');

if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Plan ID gerekli']);
    exit;
}

$plan_id = (int)$_GET['id'];

try {
    // Üretim planı ve ürün bilgilerini JOIN ile çek
    $stmt = $conn->prepare("
        SELECT 
            up.*,
            u.stok_kodu,
            u.stok_adi,
            up.bitis_tarihi as teslim_tarihi
        FROM uretim_planlari up
        LEFT JOIN urunler u ON up.urun_id = u.id
        WHERE up.id = ?
    ");
    $stmt->bind_param("i", $plan_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($plan = $result->fetch_assoc()) {
        // Eğer stok bilgileri yoksa varsayılan değerler ekle
        if (!isset($plan['stok_kodu']) || empty($plan['stok_kodu'])) {
            $plan['stok_kodu'] = 'Kod Yok';
        }
        if (!isset($plan['stok_adi']) || empty($plan['stok_adi'])) {
            $plan['stok_adi'] = 'Ürün Adı Yok';
        }
        
        // Öncelik değerlerini düzelt (1,2,3 -> dusuk,orta,yuksek)
        switch($plan['oncelik']) {
            case '1': $plan['oncelik'] = 'yuksek'; break;
            case '2': $plan['oncelik'] = 'orta'; break;
            case '3': $plan['oncelik'] = 'dusuk'; break;
            default: $plan['oncelik'] = 'orta';
        }
        
        // Durum değerlerini düzelt
        if ($plan['durum'] == 'planlandi') {
            $plan['durum'] = 'bekliyor';
        }
        if ($plan['durum'] == 'basladi') {
            $plan['durum'] = 'devam_ediyor';
        }
        
        echo json_encode([
            'success' => true,
            'plan' => $plan
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Plan bulunamadı']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Veritabanı hatası: ' . $e->getMessage()]);
}
?> 