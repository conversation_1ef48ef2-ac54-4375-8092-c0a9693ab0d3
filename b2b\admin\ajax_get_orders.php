<?php
session_start();
include '../config.php';

// Admin kontrolü
if (!isset($_SESSION['b2b_admin_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$cari_id = intval($_GET['cari_id'] ?? 0);

if ($cari_id) {
    $sql = "SELECT id, created_at, toplam_tutar, durum FROM b2b_siparisler WHERE cari_id = ? ORDER BY created_at DESC LIMIT 20";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $cari_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $siparisler = [];
    while ($row = $result->fetch_assoc()) {
        $row['created_at'] = date('d.m.Y', strtotime($row['created_at']));
        $row['toplam_tutar'] = number_format($row['toplam_tutar'], 2);
        $siparisler[] = $row;
    }
    
    header('Content-Type: application/json');
    echo json_encode($siparisler);
} else {
    header('Content-Type: application/json');
    echo json_encode([]);
}
?> 